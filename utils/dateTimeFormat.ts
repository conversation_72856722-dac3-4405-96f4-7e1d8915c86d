import { differenceInCalendarDays, format, parseISO } from 'date-fns'
import { toZonedTime, format as tzFormat } from 'date-fns-tz'

const zonedDateTime = (
  utcDate: string | Date,
  timeZone: string,
  formatString: string,
): string => {
  const dateObj = typeof utcDate === 'string' ? new Date(utcDate) : utcDate
  const zonedDate = toZonedTime(dateObj, timeZone)
  return tzFormat(zonedDate, formatString, { timeZone })
}

const isLessThanWeek = (utcDate: string | Date, timeZone: string): boolean => {
  const given = toZonedTime(new Date(utcDate), timeZone)
  const now = toZonedTime(new Date(), timeZone)
  return differenceInCalendarDays(now, given) < 7
}

const isToday = (utcDate: string | Date, timeZone: string): boolean => {
  const given = toZonedTime(new Date(utcDate), timeZone)
  const now = toZonedTime(new Date(), timeZone)
  return differenceInCalendarDays(now, given) === 0
}

const utcToLocalTime = (
  utcDate: string | Date,
  outputFormat = 'yyyy-MM-dd HH:mm:ss',
): string => {
  const dateObj = typeof utcDate === 'string' ? parseISO(utcDate) : utcDate

  return format(dateObj, outputFormat)
}

export { isLessThanWeek, isToday, utcToLocalTime, zonedDateTime }
