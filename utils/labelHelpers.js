export function getMaxId(items) {
  let maxId = 0
  function findMaxId(array) {
    array.forEach((item) => {
      if (item.id > maxId) maxId = item.id
      if (item.children?.length) findMaxId(item.children)
    })
  }
  findMaxId(items)
  return maxId
}

export const getNextId = (allContext) => {
  const ids = allContext.map((item) => item.id)
  return ids.length > 0 ? Math.max(...ids) + 1 : 1
}

export function findNestedItemAndRootParent(
  data,
  targetId,
  parent = null,
  rootParent = null,
) {
  let result = null

  data.forEach((item) => {
    const currentRoot =
      parent === null && item.nested === false ? item : rootParent

    if (result) return // exit early if found

    if (item.id === targetId) {
      result = { foundItem: item, rootParent: currentRoot }
      return
    }

    if (item.children && item.children.length > 0) {
      const childResult = findNestedItemAndRootParent(
        item.children,
        targetId,
        item,
        currentRoot,
      )
      if (childResult) {
        result = childResult
      }
    }
  })

  return result
}

export function findItemAndImmediateParent(items, targetId, parent = null) {
  for (const item of items) {
    if (item.id === targetId) {
      return { foundItem: item, parent: parent }
    }

    if (item.children && item.children.length > 0) {
      const result = findItemAndImmediateParent(item.children, targetId, item)
      if (result) return result
    }
  }
  return null
}

export function moveToRoot(items, targetId) {
  let foundItem = null

  // Deep clone to avoid mutating original
  const newItems = JSON.parse(JSON.stringify(items))

  // Recursive function to remove the item from children
  function removeFromChildren(list) {
    for (let i = 0; i < list.length; i++) {
      const item = list[i]
      if (item.children && item.children.length > 0) {
        const index = item.children.findIndex((child) => child.id === targetId)
        if (index !== -1) {
          foundItem = item.children.splice(index, 1)[0] // Remove it
          return
        } else {
          removeFromChildren(item.children)
        }
      }
    }
  }

  removeFromChildren(newItems)

  if (foundItem) {
    foundItem.nested = false
    newItems.push(foundItem)
  }

  return newItems
}

export function getUpdatedLabelsArray(items, targetId, newName) {
  return items.map((item) => {
    const newItem = { ...item }

    if (newItem.id === targetId) {
      newItem.name = newName
    }

    if (newItem.children && newItem.children.length > 0) {
      newItem.children = getUpdatedLabelsArray(
        newItem.children,
        targetId,
        newName,
      )
    }

    return newItem
  })
}
export function removeLabelById(data, targetId) {
  let removedItem = null

  function recurse(items) {
    return items
      .map((item) => {
        if (item.id === targetId) {
          removedItem = item
          return null
        }
        if (item.children?.length) {
          const updatedChildren = recurse(item.children)
          return { ...item, children: updatedChildren }
        }
        return item
      })
      .filter(Boolean)
  }

  const updatedTree = recurse(data)
  return { updatedTree, removedItem }
}
export function toggleSelectedById(data, targetId) {
  return data.map((item) => {
    let updatedItem = { ...item }

    if (item.id === targetId) {
      updatedItem.selected = !item.selected
    }

    if (item.children && item.children.length) {
      updatedItem.children = toggleSelectedById(item.children, targetId)
    }

    return updatedItem
  })
}
// Recursive function to flatten **only leaf nodes**
export function flattenLeaves(items, startId = 1, parentPath = '') {
  let result = []
  let nextId = startId

  items.forEach((item) => {
    const fullTitle = parentPath ? `${parentPath}/${item.name}` : item.name

    if (item.children && item.children.length > 0) {
      const { leaves, next } = flattenLeaves(item.children, nextId, fullTitle)
      result = result.concat(leaves)
      nextId = next
    } else {
      // Leaf node → include
      result.push({
        id: nextId,
        title: fullTitle,
        checked: false,
      })
      nextId++
    }
  })

  return { leaves: result, next: nextId }
}
// export function removeLabelById(data, targetId) {
//   return data
//     .filter(item => item.id !== targetId)
//     .map(item => ({
//       ...item,
//       children: item.children?.length
//         ? removeLabelById(item.children, targetId)
//         : [],
//     }));
// }
