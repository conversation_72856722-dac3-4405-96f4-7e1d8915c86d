interface Language {
  id: string
  code: string
  name: string
  nativeName: string
  region: string
  nativeRegion: string
  shortName: string
}

interface UniqueLanguage {
  id: string
  name: string
  nativeName: string
  shortName: string
}

export type { Language, UniqueLanguage }

export const useWorldLanguages = () => {
  const languages: Language[] = [
    {
      id: '1',
      code: 'af-NA',
      name: 'Afrikaans',
      nativeName: 'Afrikaans',
      region: 'Namibia',
      nativeRegion: 'Namibië',
      shortName: 'af',
    },
    {
      id: '2',
      code: 'af-ZA',
      name: 'Afrikaans',
      nativeName: 'Afrikaans',
      region: 'South Africa',
      nativeRegion: 'Suid-Afrika',
      shortName: 'af',
    },
    {
      id: '3',
      code: 'az-Latn-AZ',
      name: 'Azerbaijani',
      nativeName: 'azərbaycan',
      region: 'Azerbaijan',
      nativeRegion: 'Azərbaycan',
      shortName: 'az',
    },
    {
      id: '4',
      code: 'bs-Latn-BA',
      name: 'Bosnian',
      nativeName: 'bosanski',
      region: 'Bosnia and Herzegovina',
      nativeRegion: 'Bosna i Hercegovina',
      shortName: 'bs',
    },
    {
      id: '5',
      code: 'ca-AD',
      name: 'Catalan',
      nativeName: 'català',
      region: 'Andorra',
      nativeRegion: 'Andorra',
      shortName: 'ca',
    },
    {
      id: '6',
      code: 'ca-ES',
      name: 'Catalan',
      nativeName: 'català',
      region: 'Spain',
      nativeRegion: 'català',
      shortName: 'ca',
    },
    {
      id: '7',
      code: 'ca-FR',
      name: 'Catalan',
      nativeName: 'català',
      region: 'France',
      nativeRegion: 'França',
      shortName: 'ca',
    },
    {
      id: '8',
      code: 'ca-IT',
      name: 'Catalan',
      nativeName: 'català',
      region: 'Italy',
      nativeRegion: 'Itàlia',
      shortName: 'ca',
    },
    {
      id: '9',
      code: 'cs-CZ',
      name: 'Czech',
      nativeName: 'čeština',
      region: 'Czech Republic',
      nativeRegion: 'Česko',
      shortName: 'cs',
    },
    {
      id: '10',
      code: 'cy-GB',
      name: 'Welsh',
      nativeName: 'Cymraeg',
      region: 'United Kingdom',
      nativeRegion: 'Y Deyrnas Unedig',
      shortName: 'cy',
    },
    {
      id: '11',
      code: 'da-DK',
      name: 'Danish',
      nativeName: 'dansk',
      region: 'Denmark',
      nativeRegion: 'Danmark',
      shortName: 'da',
    },
    {
      id: '12',
      code: 'da-GL',
      name: 'Danish',
      nativeName: 'dansk',
      region: 'Greenland',
      nativeRegion: 'Grønland',
      shortName: 'da',
    },
    {
      id: '13',
      code: 'de-BE',
      name: 'German',
      nativeName: 'Deutsch',
      region: 'Belgium',
      nativeRegion: 'Belgien',
      shortName: 'de',
    },
    {
      id: '14',
      code: 'de-DE',
      name: 'German',
      nativeName: 'Deutsch',
      region: 'Germany',
      nativeRegion: 'Deutschland',
      shortName: 'de',
    },
    {
      id: '15',
      code: 'de-IT',
      name: 'German',
      nativeName: 'Deutsch',
      region: 'Italy',
      nativeRegion: 'Italien',
      shortName: 'de',
    },
    {
      id: '16',
      code: 'de-LI',
      name: 'German',
      nativeName: 'Deutsch',
      region: 'Liechtenstein',
      nativeRegion: 'Liechtenstein',
      shortName: 'de',
    },
    {
      id: '17',
      code: 'de-LU',
      name: 'German',
      nativeName: 'Deutsch',
      region: 'Luxembourg',
      nativeRegion: 'Luxemburg',
      shortName: 'de',
    },
    {
      id: '18',
      code: 'de-AT',
      name: 'German',
      nativeName: 'Deutsch',
      region: 'Austria',
      nativeRegion: 'Österreich',
      shortName: 'de',
    },
    {
      id: '19',
      code: 'de-CH',
      name: 'German',
      nativeName: 'Deutsch',
      region: 'Switzerland',
      nativeRegion: 'Schweiz',
      shortName: 'de',
    },
    {
      id: '20',
      code: 'et-EE',
      name: 'Estonian',
      nativeName: 'eesti',
      region: 'Estonia',
      nativeRegion: 'Eesti',
      shortName: 'et',
    },
    {
      id: '21',
      code: 'en-AS',
      name: 'English',
      nativeName: 'English',
      region: 'American Samoa',
      nativeRegion: 'American Samoa',
      shortName: 'en',
    },
    {
      id: '22',
      code: 'en-AI',
      name: 'English',
      nativeName: 'English',
      region: 'Anguilla',
      nativeRegion: 'Anguilla',
      shortName: 'en',
    },
    {
      id: '23',
      code: 'en-AG',
      name: 'English',
      nativeName: 'English',
      region: 'Antigua & Barbuda',
      nativeRegion: 'Antigua & Barbuda',
      shortName: 'en',
    },
    {
      id: '24',
      code: 'en-AU',
      name: 'English',
      nativeName: 'English',
      region: 'Australia',
      nativeRegion: 'Australia',
      shortName: 'en',
    },
    {
      id: '25',
      code: 'en-AT',
      name: 'English',
      nativeName: 'English',
      region: 'Austria',
      nativeRegion: 'Austria',
      shortName: 'en',
    },
    {
      id: '26',
      code: 'en-BS',
      name: 'English',
      nativeName: 'English',
      region: 'Bahamas',
      nativeRegion: 'Bahamas',
      shortName: 'en',
    },
    {
      id: '27',
      code: 'en-BB',
      name: 'English',
      nativeName: 'English',
      region: 'Barbados',
      nativeRegion: 'Barbados',
      shortName: 'en',
    },
    {
      id: '28',
      code: 'en-BE',
      name: 'English',
      nativeName: 'English',
      region: 'Belgium',
      nativeRegion: 'Belgium',
      shortName: 'en',
    },
    {
      id: '29',
      code: 'en-BZ',
      name: 'English',
      nativeName: 'English',
      region: 'Belize',
      nativeRegion: 'Belize',
      shortName: 'en',
    },
    {
      id: '30',
      code: 'en-BM',
      name: 'English',
      nativeName: 'English',
      region: 'Bermuda',
      nativeRegion: 'Bermuda',
      shortName: 'en',
    },
    {
      id: '31',
      code: 'en-BW',
      name: 'English',
      nativeName: 'English',
      region: 'Botswana',
      nativeRegion: 'Botswana',
      shortName: 'en',
    },
    {
      id: '32',
      code: 'en-IO',
      name: 'English',
      nativeName: 'English',
      region: 'British Indian Ocean Territory',
      nativeRegion: 'British Indian Ocean Territory',
      shortName: 'en',
    },
    {
      id: '33',
      code: 'en-VG',
      name: 'English',
      nativeName: 'English',
      region: 'British Virgin Islands',
      nativeRegion: 'British Virgin Islands',
      shortName: 'en',
    },
    {
      id: '34',
      code: 'en-BI',
      name: 'English',
      nativeName: 'English',
      region: 'Burundi',
      nativeRegion: 'Burundi',
      shortName: 'en',
    },
    {
      id: '35',
      code: 'en-CM',
      name: 'English',
      nativeName: 'English',
      region: 'Cameroon',
      nativeRegion: 'Cameroon',
      shortName: 'en',
    },
    {
      id: '36',
      code: 'en-CA',
      name: 'English',
      nativeName: 'English',
      region: 'Canada',
      nativeRegion: 'Canada',
      shortName: 'en',
    },
    {
      id: '37',
      code: 'en-029',
      name: 'English',
      nativeName: 'English',
      region: 'Caribbean',
      nativeRegion: 'Caribbean',
      shortName: 'en',
    },
    {
      id: '38',
      code: 'en-KY',
      name: 'English',
      nativeName: 'English',
      region: 'Cayman Islands',
      nativeRegion: 'Cayman Islands',
      shortName: 'en',
    },
    {
      id: '39',
      code: 'en-CX',
      name: 'English',
      nativeName: 'English',
      region: 'Christmas Island',
      nativeRegion: 'Christmas Island',
      shortName: 'en',
    },
    {
      id: '40',
      code: 'en-CC',
      name: 'English',
      nativeName: 'English',
      region: 'Cocos (Keeling) Islands',
      nativeRegion: 'Cocos (Keeling) Islands',
      shortName: 'en',
    },
    {
      id: '41',
      code: 'en-CK',
      name: 'English',
      nativeName: 'English',
      region: 'Cook Islands',
      nativeRegion: 'Cook Islands',
      shortName: 'en',
    },
    {
      id: '42',
      code: 'en-CY',
      name: 'English',
      nativeName: 'English',
      region: 'Cyprus',
      nativeRegion: 'Cyprus',
      shortName: 'en',
    },
    {
      id: '43',
      code: 'en-DK',
      name: 'English',
      nativeName: 'English',
      region: 'Denmark',
      nativeRegion: 'Denmark',
      shortName: 'en',
    },
    {
      id: '44',
      code: 'en-DM',
      name: 'English',
      nativeName: 'English',
      region: 'Dominica',
      nativeRegion: 'Dominica',
      shortName: 'en',
    },
    {
      id: '45',
      code: 'en-ER',
      name: 'English',
      nativeName: 'English',
      region: 'Eritrea',
      nativeRegion: 'Eritrea',
      shortName: 'en',
    },
    {
      id: '46',
      code: 'en-SZ',
      name: 'English',
      nativeName: 'English',
      region: 'Eswatini',
      nativeRegion: 'Eswatini',
      shortName: 'en',
    },
    {
      id: '47',
      code: 'en-150',
      name: 'English',
      nativeName: 'English',
      region: 'Europe',
      nativeRegion: 'Europe',
      shortName: 'en',
    },
    {
      id: '48',
      code: 'en-FK',
      name: 'English',
      nativeName: 'English',
      region: 'Falkland Islands',
      nativeRegion: 'Falkland Islands',
      shortName: 'en',
    },
    {
      id: '49',
      code: 'en-FJ',
      name: 'English',
      nativeName: 'English',
      region: 'Fiji',
      nativeRegion: 'Fiji',
      shortName: 'en',
    },
    {
      id: '50',
      code: 'en-FI',
      name: 'English',
      nativeName: 'English',
      region: 'Finland',
      nativeRegion: 'Finland',
      shortName: 'en',
    },
    {
      id: '51',
      code: 'en-GM',
      name: 'English',
      nativeName: 'English',
      region: 'Gambia',
      nativeRegion: 'Gambia',
      shortName: 'en',
    },
    {
      id: '52',
      code: 'en-DE',
      name: 'English',
      nativeName: 'English',
      region: 'Germany',
      nativeRegion: 'Germany',
      shortName: 'en',
    },
    {
      id: '53',
      code: 'en-GH',
      name: 'English',
      nativeName: 'English',
      region: 'Ghana',
      nativeRegion: 'Ghana',
      shortName: 'en',
    },
    {
      id: '54',
      code: 'en-GI',
      name: 'English',
      nativeName: 'English',
      region: 'Gibraltar',
      nativeRegion: 'Gibraltar',
      shortName: 'en',
    },
    {
      id: '55',
      code: 'en-GD',
      name: 'English',
      nativeName: 'English',
      region: 'Grenada',
      nativeRegion: 'Grenada',
      shortName: 'en',
    },
    {
      id: '56',
      code: 'en-GU',
      name: 'English',
      nativeName: 'English',
      region: 'Guam',
      nativeRegion: 'Guam',
      shortName: 'en',
    },
    {
      id: '57',
      code: 'en-GG',
      name: 'English',
      nativeName: 'English',
      region: 'Guernsey',
      nativeRegion: 'Guernsey',
      shortName: 'en',
    },
    {
      id: '58',
      code: 'en-GY',
      name: 'English',
      nativeName: 'English',
      region: 'Guyana',
      nativeRegion: 'Guyana',
      shortName: 'en',
    },
    {
      id: '59',
      code: 'en-IN',
      name: 'English',
      nativeName: 'English',
      region: 'India',
      nativeRegion: 'India',
      shortName: 'en',
    },
    {
      id: '60',
      code: 'en-ID',
      name: 'English',
      nativeName: 'English',
      region: 'Indonesia',
      nativeRegion: 'Indonesia',
      shortName: 'en',
    },
    {
      id: '61',
      code: 'en-IE',
      name: 'English',
      nativeName: 'English',
      region: 'Ireland',
      nativeRegion: 'Ireland',
      shortName: 'en',
    },
    {
      id: '62',
      code: 'en-IM',
      name: 'English',
      nativeName: 'English',
      region: 'Isle of Man',
      nativeRegion: 'Isle of Man',
      shortName: 'en',
    },
    {
      id: '63',
      code: 'en-IL',
      name: 'English',
      nativeName: 'English',
      region: 'Israel',
      nativeRegion: 'Israel',
      shortName: 'en',
    },
    {
      id: '64',
      code: 'en-JM',
      name: 'English',
      nativeName: 'English',
      region: 'Jamaica',
      nativeRegion: 'Jamaica',
      shortName: 'en',
    },
    {
      id: '65',
      code: 'en-JE',
      name: 'English',
      nativeName: 'English',
      region: 'Jersey',
      nativeRegion: 'Jersey',
      shortName: 'en',
    },
    {
      id: '66',
      code: 'en-KE',
      name: 'English',
      nativeName: 'English',
      region: 'Kenya',
      nativeRegion: 'Kenya',
      shortName: 'en',
    },
    {
      id: '67',
      code: 'en-KI',
      name: 'English',
      nativeName: 'English',
      region: 'Kiribati',
      nativeRegion: 'Kiribati',
      shortName: 'en',
    },
    {
      id: '68',
      code: 'en-LS',
      name: 'English',
      nativeName: 'English',
      region: 'Lesotho',
      nativeRegion: 'Lesotho',
      shortName: 'en',
    },
    {
      id: '69',
      code: 'en-LR',
      name: 'English',
      nativeName: 'English',
      region: 'Liberia',
      nativeRegion: 'Liberia',
      shortName: 'en',
    },
    {
      id: '70',
      code: 'en-MO',
      name: 'English',
      nativeName: 'English',
      region: 'Macao SAR',
      nativeRegion: 'Macao SAR',
      shortName: 'en',
    },
    {
      id: '71',
      code: 'en-MG',
      name: 'English',
      nativeName: 'English',
      region: 'Madagascar',
      nativeRegion: 'Madagascar',
      shortName: 'en',
    },
    {
      id: '72',
      code: 'en-MW',
      name: 'English',
      nativeName: 'English',
      region: 'Malawi',
      nativeRegion: 'Malawi',
      shortName: 'en',
    },
    {
      id: '73',
      code: 'en-MY',
      name: 'English',
      nativeName: 'English',
      region: 'Malaysia',
      nativeRegion: 'Malaysia',
      shortName: 'en',
    },
    {
      id: '74',
      code: 'en-MT',
      name: 'English',
      nativeName: 'English',
      region: 'Malta',
      nativeRegion: 'Malta',
      shortName: 'en',
    },
    {
      id: '75',
      code: 'en-MH',
      name: 'English',
      nativeName: 'English',
      region: 'Marshall Islands',
      nativeRegion: 'Marshall Islands',
      shortName: 'en',
    },
    {
      id: '76',
      code: 'en-MU',
      name: 'English',
      nativeName: 'English',
      region: 'Mauritius',
      nativeRegion: 'Mauritius',
      shortName: 'en',
    },
    {
      id: '77',
      code: 'en-FM',
      name: 'English',
      nativeName: 'English',
      region: 'Micronesia',
      nativeRegion: 'Micronesia',
      shortName: 'en',
    },
    {
      id: '78',
      code: 'en-MS',
      name: 'English',
      nativeName: 'English',
      region: 'Montserrat',
      nativeRegion: 'Montserrat',
      shortName: 'en',
    },
    {
      id: '79',
      code: 'en-NA',
      name: 'English',
      nativeName: 'English',
      region: 'Namibia',
      nativeRegion: 'Namibia',
      shortName: 'en',
    },
    {
      id: '80',
      code: 'en-NR',
      name: 'English',
      nativeName: 'English',
      region: 'Nauru',
      nativeRegion: 'Nauru',
      shortName: 'en',
    },
    {
      id: '81',
      code: 'en-NL',
      name: 'English',
      nativeName: 'English',
      region: 'Netherlands',
      nativeRegion: 'Netherlands',
      shortName: 'en',
    },
    {
      id: '82',
      code: 'en-NZ',
      name: 'English',
      nativeName: 'English',
      region: 'New Zealand',
      nativeRegion: 'New Zealand',
      shortName: 'en',
    },
    {
      id: '83',
      code: 'en-NG',
      name: 'English',
      nativeName: 'English',
      region: 'Nigeria',
      nativeRegion: 'Nigeria',
      shortName: 'en',
    },
    {
      id: '84',
      code: 'en-NU',
      name: 'English',
      nativeName: 'English',
      region: 'Niue',
      nativeRegion: 'Niue',
      shortName: 'en',
    },
    {
      id: '85',
      code: 'en-NF',
      name: 'English',
      nativeName: 'English',
      region: 'Norfolk Island',
      nativeRegion: 'Norfolk Island',
      shortName: 'en',
    },
    {
      id: '86',
      code: 'en-MP',
      name: 'English',
      nativeName: 'English',
      region: 'Northern Mariana Islands',
      nativeRegion: 'Northern Mariana Islands',
      shortName: 'en',
    },
    {
      id: '87',
      code: 'en-PK',
      name: 'English',
      nativeName: 'English',
      region: 'Pakistan',
      nativeRegion: 'Pakistan',
      shortName: 'en',
    },
    {
      id: '88',
      code: 'en-PW',
      name: 'English',
      nativeName: 'English',
      region: 'Palau',
      nativeRegion: 'Palau',
      shortName: 'en',
    },
    {
      id: '89',
      code: 'en-PG',
      name: 'English',
      nativeName: 'English',
      region: 'Papua New Guinea',
      nativeRegion: 'Papua New Guinea',
      shortName: 'en',
    },
    {
      id: '90',
      code: 'en-PH',
      name: 'English',
      nativeName: 'English',
      region: 'Philippines',
      nativeRegion: 'Philippines',
      shortName: 'en',
    },
    {
      id: '91',
      code: 'en-PN',
      name: 'English',
      nativeName: 'English',
      region: 'Pitcairn Islands',
      nativeRegion: 'Pitcairn Islands',
      shortName: 'en',
    },
    {
      id: '92',
      code: 'en-PR',
      name: 'English',
      nativeName: 'English',
      region: 'Puerto Rico',
      nativeRegion: 'Puerto Rico',
      shortName: 'en',
    },
    {
      id: '93',
      code: 'en-RW',
      name: 'English',
      nativeName: 'English',
      region: 'Rwanda',
      nativeRegion: 'Rwanda',
      shortName: 'en',
    },
    {
      id: '94',
      code: 'en-WS',
      name: 'English',
      nativeName: 'English',
      region: 'Samoa',
      nativeRegion: 'Samoa',
      shortName: 'en',
    },
    {
      id: '95',
      code: 'en-SC',
      name: 'English',
      nativeName: 'English',
      region: 'Seychelles',
      nativeRegion: 'Seychelles',
      shortName: 'en',
    },
    {
      id: '96',
      code: 'en-SL',
      name: 'English',
      nativeName: 'English',
      region: 'Sierra Leone',
      nativeRegion: 'Sierra Leone',
      shortName: 'en',
    },
    {
      id: '97',
      code: 'en-SG',
      name: 'English',
      nativeName: 'English',
      region: 'Singapore',
      nativeRegion: 'Singapore',
      shortName: 'en',
    },
    {
      id: '98',
      code: 'en-SX',
      name: 'English',
      nativeName: 'English',
      region: 'Sint Maarten',
      nativeRegion: 'Sint Maarten',
      shortName: 'en',
    },
    {
      id: '99',
      code: 'en-SI',
      name: 'English',
      nativeName: 'English',
      region: 'Slovenia',
      nativeRegion: 'Slovenia',
      shortName: 'en',
    },
    {
      id: '100',
      code: 'en-SB',
      name: 'English',
      nativeName: 'English',
      region: 'Solomon Islands',
      nativeRegion: 'Solomon Islands',
      shortName: 'en',
    },
    {
      id: '101',
      code: 'en-ZA',
      name: 'English',
      nativeName: 'English',
      region: 'South Africa',
      nativeRegion: 'South Africa',
      shortName: 'en',
    },
    {
      id: '102',
      code: 'en-SS',
      name: 'English',
      nativeName: 'English',
      region: 'South Sudan',
      nativeRegion: 'South Sudan',
      shortName: 'en',
    },
    {
      id: '103',
      code: 'en-SH',
      name: 'English',
      nativeName: 'English',
      region: 'St Helena, Ascension, Tristan da Cunha',
      nativeRegion: 'St Helena, Ascension, Tristan da Cunha',
      shortName: 'en',
    },
    {
      id: '104',
      code: 'en-KN',
      name: 'English',
      nativeName: 'English',
      region: 'St. Kitts & Nevis',
      nativeRegion: 'St. Kitts & Nevis',
      shortName: 'en',
    },
    {
      id: '105',
      code: 'en-LC',
      name: 'English',
      nativeName: 'English',
      region: 'St. Lucia',
      nativeRegion: 'St. Lucia',
      shortName: 'en',
    },
    {
      id: '106',
      code: 'en-VC',
      name: 'English',
      nativeName: 'English',
      region: 'St. Vincent & Grenadines',
      nativeRegion: 'St. Vincent & Grenadines',
      shortName: 'en',
    },
    {
      id: '107',
      code: 'en-SD',
      name: 'English',
      nativeName: 'English',
      region: 'Sudan',
      nativeRegion: 'Sudan',
      shortName: 'en',
    },
    {
      id: '108',
      code: 'en-SE',
      name: 'English',
      nativeName: 'English',
      region: 'Sweden',
      nativeRegion: 'Sweden',
      shortName: 'en',
    },
    {
      id: '109',
      code: 'en-CH',
      name: 'English',
      nativeName: 'English',
      region: 'Switzerland',
      nativeRegion: 'Switzerland',
      shortName: 'en',
    },
    {
      id: '110',
      code: 'en-TZ',
      name: 'English',
      nativeName: 'English',
      region: 'Tanzania',
      nativeRegion: 'Tanzania',
      shortName: 'en',
    },
    {
      id: '111',
      code: 'en-TK',
      name: 'English',
      nativeName: 'English',
      region: 'Tokelau',
      nativeRegion: 'Tokelau',
      shortName: 'en',
    },
    {
      id: '112',
      code: 'en-TO',
      name: 'English',
      nativeName: 'English',
      region: 'Tonga',
      nativeRegion: 'Tonga',
      shortName: 'en',
    },
    {
      id: '113',
      code: 'en-TT',
      name: 'English',
      nativeName: 'English',
      region: 'Trinidad & Tobago',
      nativeRegion: 'Trinidad & Tobago',
      shortName: 'en',
    },
    {
      id: '114',
      code: 'en-TC',
      name: 'English',
      nativeName: 'English',
      region: 'Turks & Caicos Islands',
      nativeRegion: 'Turks & Caicos Islands',
      shortName: 'en',
    },
    {
      id: '115',
      code: 'en-TV',
      name: 'English',
      nativeName: 'English',
      region: 'Tuvalu',
      nativeRegion: 'Tuvalu',
      shortName: 'en',
    },
    {
      id: '116',
      code: 'en-UM',
      name: 'English',
      nativeName: 'English',
      region: 'U.S. Outlying Islands',
      nativeRegion: 'U.S. Outlying Islands',
      shortName: 'en',
    },
    {
      id: '117',
      code: 'en-VI',
      name: 'English',
      nativeName: 'English',
      region: 'U.S. Virgin Islands',
      nativeRegion: 'U.S. Virgin Islands',
      shortName: 'en',
    },
    {
      id: '118',
      code: 'en-UG',
      name: 'English',
      nativeName: 'English',
      region: 'Uganda',
      nativeRegion: 'Uganda',
      shortName: 'en',
    },
    {
      id: '119',
      code: 'en-AE',
      name: 'English',
      nativeName: 'English',
      region: 'United Arab Emirates',
      nativeRegion: 'United Arab Emirates',
      shortName: 'en',
    },
    {
      id: '120',
      code: 'en-GB',
      name: 'English',
      nativeName: 'English',
      region: 'United Kingdom',
      nativeRegion: 'United Kingdom',
      shortName: 'en',
    },
    {
      id: '121',
      code: 'en-US',
      name: 'English',
      nativeName: 'English',
      region: 'United States',
      nativeRegion: 'United States',
      shortName: 'en',
    },
    {
      id: '122',
      code: 'en-VU',
      name: 'English',
      nativeName: 'English',
      region: 'Vanuatu',
      nativeRegion: 'Vanuatu',
      shortName: 'en',
    },
    {
      id: '123',
      code: 'en-001',
      name: 'English',
      nativeName: 'English',
      region: 'World',
      nativeRegion: 'World',
      shortName: 'en',
    },
    {
      id: '124',
      code: 'en-ZM',
      name: 'English',
      nativeName: 'English',
      region: 'Zambia',
      nativeRegion: 'Zambia',
      shortName: 'en',
    },
    {
      id: '125',
      code: 'en-ZW',
      name: 'English',
      nativeName: 'English',
      region: 'Zimbabwe',
      nativeRegion: 'Zimbabwe',
      shortName: 'en',
    },
    {
      id: '126',
      code: 'es-AR',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Argentina',
      nativeRegion: 'Argentina',
      shortName: 'es',
    },
    {
      id: '127',
      code: 'es-BZ',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Belize',
      nativeRegion: 'Belice',
      shortName: 'es',
    },
    {
      id: '128',
      code: 'es-BO',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Bolivia',
      nativeRegion: 'Bolivia',
      shortName: 'es',
    },
    {
      id: '129',
      code: 'es-BR',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Brazil',
      nativeRegion: 'Brasil',
      shortName: 'es',
    },
    {
      id: '130',
      code: 'es-CL',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Chile',
      nativeRegion: 'Chile',
      shortName: 'es',
    },
    {
      id: '131',
      code: 'es-CO',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Colombia',
      nativeRegion: 'Colombia',
      shortName: 'es',
    },
    {
      id: '132',
      code: 'es-CR',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Costa Rica',
      nativeRegion: 'Costa Rica',
      shortName: 'es',
    },
    {
      id: '133',
      code: 'es-CU',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Cuba',
      nativeRegion: 'Cuba',
      shortName: 'es',
    },
    {
      id: '134',
      code: 'es-EC',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Ecuador',
      nativeRegion: 'Ecuador',
      shortName: 'es',
    },
    {
      id: '135',
      code: 'es-SV',
      name: 'Spanish',
      nativeName: 'español',
      region: 'El Salvador',
      nativeRegion: 'El Salvador',
      shortName: 'es',
    },
    {
      id: '136',
      code: 'es-ES',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Spain',
      nativeRegion: 'España, alfabetización internacional',
      shortName: 'es',
    },
    {
      id: '137',
      code: 'es-US',
      name: 'Spanish',
      nativeName: 'español',
      region: 'United States',
      nativeRegion: 'Estados Unidos',
      shortName: 'es',
    },
    {
      id: '138',
      code: 'es-PH',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Philippines',
      nativeRegion: 'Filipinas',
      shortName: 'es',
    },
    {
      id: '139',
      code: 'es-GT',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Guatemala',
      nativeRegion: 'Guatemala',
      shortName: 'es',
    },
    {
      id: '140',
      code: 'es-GQ',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Equatorial Guinea',
      nativeRegion: 'Guinea Ecuatorial',
      shortName: 'es',
    },
    {
      id: '141',
      code: 'es-HN',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Honduras',
      nativeRegion: 'Honduras',
      shortName: 'es',
    },
    {
      id: '142',
      code: 'es-MX',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Mexico',
      nativeRegion: 'México',
      shortName: 'es',
    },
    {
      id: '143',
      code: 'es-NI',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Nicaragua',
      nativeRegion: 'Nicaragua',
      shortName: 'es',
    },
    {
      id: '144',
      code: 'es-PA',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Panama',
      nativeRegion: 'Panamá',
      shortName: 'es',
    },
    {
      id: '145',
      code: 'es-PY',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Paraguay',
      nativeRegion: 'Paraguay',
      shortName: 'es',
    },
    {
      id: '146',
      code: 'es-PE',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Peru',
      nativeRegion: 'Perú',
      shortName: 'es',
    },
    {
      id: '147',
      code: 'es-PR',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Puerto Rico',
      nativeRegion: 'Puerto Rico',
      shortName: 'es',
    },
    {
      id: '148',
      code: 'es-DO',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Dominican Republic',
      nativeRegion: 'República Dominicana',
      shortName: 'es',
    },
    {
      id: '149',
      code: 'es-UY',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Uruguay',
      nativeRegion: 'Uruguay',
      shortName: 'es',
    },
    {
      id: '150',
      code: 'es-VE',
      name: 'Spanish',
      nativeName: 'español',
      region: 'Venezuela',
      nativeRegion: 'Venezuela',
      shortName: 'es',
    },
    {
      id: '151',
      code: 'eu-ES',
      name: 'Basque',
      nativeName: 'euskara',
      region: 'Spain',
      nativeRegion: 'euskara',
      shortName: 'eu',
    },
    {
      id: '152',
      code: 'fil-PH',
      name: 'Filipino',
      nativeName: 'Filipino',
      region: 'Philippines',
      nativeRegion: 'Pilipinas',
      shortName: 'fil',
    },
    {
      id: '153',
      code: 'fr-DZ',
      name: 'French',
      nativeName: 'français',
      region: 'Algeria',
      nativeRegion: 'Algérie',
      shortName: 'fr',
    },
    {
      id: '154',
      code: 'fr-BE',
      name: 'French',
      nativeName: 'français',
      region: 'Belgium',
      nativeRegion: 'Belgique',
      shortName: 'fr',
    },
    {
      id: '155',
      code: 'fr-BJ',
      name: 'French',
      nativeName: 'français',
      region: 'Benin',
      nativeRegion: 'Bénin',
      shortName: 'fr',
    },
    {
      id: '156',
      code: 'fr-BF',
      name: 'French',
      nativeName: 'français',
      region: 'Burkina Faso',
      nativeRegion: 'Burkina Faso',
      shortName: 'fr',
    },
    {
      id: '157',
      code: 'fr-BI',
      name: 'French',
      nativeName: 'français',
      region: 'Burundi',
      nativeRegion: 'Burundi',
      shortName: 'fr',
    },
    {
      id: '158',
      code: 'fr-CA',
      name: 'French',
      nativeName: 'français',
      region: 'Canada',
      nativeRegion: 'Canada',
      shortName: 'fr',
    },
    {
      id: '159',
      code: 'fr-029',
      name: 'French',
      nativeName: 'français',
      region: 'Caribbean',
      nativeRegion: 'Caraïbes',
      shortName: 'fr',
    },
    {
      id: '160',
      code: 'fr-KM',
      name: 'French',
      nativeName: 'français',
      region: 'Comoros',
      nativeRegion: 'Comores',
      shortName: 'fr',
    },
    {
      id: '161',
      code: 'fr-CG',
      name: 'French',
      nativeName: 'français',
      region: 'Congo',
      nativeRegion: 'Congo',
      shortName: 'fr',
    },
    {
      id: '162',
      code: 'fr-DJ',
      name: 'French',
      nativeName: 'français',
      region: 'Djibouti',
      nativeRegion: 'Djibouti',
      shortName: 'fr',
    },
    {
      id: '163',
      code: 'fr-FR',
      name: 'French',
      nativeName: 'français',
      region: 'France',
      nativeRegion: 'France',
      shortName: 'fr',
    },
    {
      id: '164',
      code: 'fr-GA',
      name: 'French',
      nativeName: 'français',
      region: 'Gabon',
      nativeRegion: 'Gabon',
      shortName: 'fr',
    },
    {
      id: '165',
      code: 'fr-GP',
      name: 'French',
      nativeName: 'français',
      region: 'Guadeloupe',
      nativeRegion: 'Guadeloupe',
      shortName: 'fr',
    },
    {
      id: '166',
      code: 'fr-GQ',
      name: 'French',
      nativeName: 'français',
      region: 'Equatorial Guinea',
      nativeRegion: 'Guinée équatoriale',
      shortName: 'fr',
    },
    {
      id: '167',
      code: 'fr-GN',
      name: 'French',
      nativeName: 'français',
      region: 'Guinea',
      nativeRegion: 'Guinée',
      shortName: 'fr',
    },
    {
      id: '168',
      code: 'fr-GF',
      name: 'French',
      nativeName: 'français',
      region: 'French Guiana',
      nativeRegion: 'Guyane française',
      shortName: 'fr',
    },
    {
      id: '169',
      code: 'fr-LU',
      name: 'French',
      nativeName: 'français',
      region: 'Luxembourg',
      nativeRegion: 'Luxembourg',
      shortName: 'fr',
    },
    {
      id: '170',
      code: 'fr-MG',
      name: 'French',
      nativeName: 'français',
      region: 'Madagascar',
      nativeRegion: 'Madagascar',
      shortName: 'fr',
    },
    {
      id: '171',
      code: 'fr-MQ',
      name: 'French',
      nativeName: 'français',
      region: 'Martinique',
      nativeRegion: 'Martinique',
      shortName: 'fr',
    },
    {
      id: '172',
      code: 'fr-MU',
      name: 'French',
      nativeName: 'français',
      region: 'Mauritius',
      nativeRegion: 'Maurice',
      shortName: 'fr',
    },
    {
      id: '173',
      code: 'fr-MR',
      name: 'French',
      nativeName: 'français',
      region: 'Mauritania',
      nativeRegion: 'Mauritanie',
      shortName: 'fr',
    },
    {
      id: '174',
      code: 'fr-YT',
      name: 'French',
      nativeName: 'français',
      region: 'Mayotte',
      nativeRegion: 'Mayotte',
      shortName: 'fr',
    },
    {
      id: '175',
      code: 'fr-MC',
      name: 'French',
      nativeName: 'français',
      region: 'Monaco',
      nativeRegion: 'Monaco',
      shortName: 'fr',
    },
    {
      id: '176',
      code: 'fr-NE',
      name: 'French',
      nativeName: 'français',
      region: 'Niger',
      nativeRegion: 'Niger',
      shortName: 'fr',
    },
    {
      id: '177',
      code: 'fr-NC',
      name: 'French',
      nativeName: 'français',
      region: 'New Caledonia',
      nativeRegion: 'Nouvelle-Calédonie',
      shortName: 'fr',
    },
    {
      id: '178',
      code: 'fr-PF',
      name: 'French',
      nativeName: 'français',
      region: 'French Polynesia',
      nativeRegion: 'Polynésie française',
      shortName: 'fr',
    },
    {
      id: '179',
      code: 'fr-CF',
      name: 'French',
      nativeName: 'français',
      region: 'Central African Republic',
      nativeRegion: 'République centrafricaine',
      shortName: 'fr',
    },
    {
      id: '180',
      code: 'fr-RW',
      name: 'French',
      nativeName: 'français',
      region: 'Rwanda',
      nativeRegion: 'Rwanda',
      shortName: 'fr',
    },
    {
      id: '181',
      code: 'fr-BL',
      name: 'French',
      nativeName: 'français',
      region: 'Saint Barthélemy',
      nativeRegion: 'Saint-Barthélemy',
      shortName: 'fr',
    },
    {
      id: '182',
      code: 'fr-MF',
      name: 'French',
      nativeName: 'français',
      region: 'Saint Martin',
      nativeRegion: 'Saint-Martin',
      shortName: 'fr',
    },
    {
      id: '183',
      code: 'fr-PM',
      name: 'French',
      nativeName: 'français',
      region: 'Saint Pierre and Miquelon',
      nativeRegion: 'Saint-Pierre-et-Miquelon',
      shortName: 'fr',
    },
    {
      id: '184',
      code: 'fr-SC',
      name: 'French',
      nativeName: 'français',
      region: 'Seychelles',
      nativeRegion: 'Seychelles',
      shortName: 'fr',
    },
    {
      id: '185',
      code: 'fr-CH',
      name: 'French',
      nativeName: 'français',
      region: 'Switzerland',
      nativeRegion: 'Suisse',
      shortName: 'fr',
    },
    {
      id: '186',
      code: 'fr-SY',
      name: 'French',
      nativeName: 'français',
      region: 'Syria',
      nativeRegion: 'Syrie',
      shortName: 'fr',
    },
    {
      id: '187',
      code: 'fr-TD',
      name: 'French',
      nativeName: 'français',
      region: 'Chad',
      nativeRegion: 'Tchad',
      shortName: 'fr',
    },
    {
      id: '188',
      code: 'fr-TG',
      name: 'French',
      nativeName: 'français',
      region: 'Togo',
      nativeRegion: 'Togo',
      shortName: 'fr',
    },
    {
      id: '189',
      code: 'fr-TN',
      name: 'French',
      nativeName: 'français',
      region: 'Tunisia',
      nativeRegion: 'Tunisie',
      shortName: 'fr',
    },
    {
      id: '190',
      code: 'fr-VU',
      name: 'French',
      nativeName: 'français',
      region: 'Vanuatu',
      nativeRegion: 'Vanuatu',
      shortName: 'fr',
    },
    {
      id: '191',
      code: 'fr-WF',
      name: 'French',
      nativeName: 'français',
      region: 'Wallis and Futuna',
      nativeRegion: 'Wallis-et-Futuna',
      shortName: 'fr',
    },
    {
      id: '192',
      code: 'ga-GB',
      name: 'Irish',
      nativeName: 'Gaeilge',
      region: 'United Kingdom',
      nativeRegion: 'an Ríocht Aontaithe',
      shortName: 'ga',
    },
    {
      id: '193',
      code: 'ga-IE',
      name: 'Irish',
      nativeName: 'Gaeilge',
      region: 'Ireland',
      nativeRegion: 'Éire',
      shortName: 'ga',
    },
    {
      id: '194',
      code: 'gd-GB',
      name: 'Scottish Gaelic',
      nativeName: 'Gàidhlig',
      region: 'United Kingdom',
      nativeRegion: 'An Rìoghachd Aonaichte',
      shortName: 'gd',
    },
    {
      id: '195',
      code: 'gl-ES',
      name: 'Galician',
      nativeName: 'galego',
      region: 'Spain',
      nativeRegion: 'galego',
      shortName: 'gl',
    },
    {
      id: '196',
      code: 'ha-Latn-GH',
      name: 'Hausa',
      nativeName: 'Hausa',
      region: 'Ghana',
      nativeRegion: 'Gana',
      shortName: 'ha',
    },
    {
      id: '197',
      code: 'ha-Latn-NG',
      name: 'Hausa',
      nativeName: 'Hausa',
      region: 'Nigeria',
      nativeRegion: 'Najeriya',
      shortName: 'ha',
    },
    {
      id: '198',
      code: 'ha-Latn-NE',
      name: 'Hausa',
      nativeName: 'Hausa',
      region: 'Niger',
      nativeRegion: 'Nijar',
      shortName: 'ha',
    },
    {
      id: '199',
      code: 'hr-BA',
      name: 'Croatian',
      nativeName: 'hrvatski',
      region: 'Bosnia and Herzegovina',
      nativeRegion: 'Bosna i Hercegovina',
      shortName: 'hr',
    },
    {
      id: '200',
      code: 'hr-HR',
      name: 'Croatian',
      nativeName: 'hrvatski',
      region: 'Croatia',
      nativeRegion: 'Hrvatska',
      shortName: 'hr',
    },
    {
      id: '201',
      code: 'id-ID',
      name: 'Indonesian',
      nativeName: 'Indonesia',
      region: 'Indonesia',
      nativeRegion: 'Indonesia',
      shortName: 'id',
    },
    {
      id: '202',
      code: 'is-IS',
      name: 'Icelandic',
      nativeName: 'íslenska',
      region: 'Iceland',
      nativeRegion: 'Ísland',
      shortName: 'is',
    },
    {
      id: '203',
      code: 'it-VA',
      name: 'Italian',
      nativeName: 'italiano',
      region: 'Vatican City',
      nativeRegion: 'Città del Vaticano',
      shortName: 'it',
    },
    {
      id: '204',
      code: 'it-IT',
      name: 'Italian',
      nativeName: 'italiano',
      region: 'Italy',
      nativeRegion: 'Italia',
      shortName: 'it',
    },
    {
      id: '205',
      code: 'it-SM',
      name: 'Italian',
      nativeName: 'italiano',
      region: 'San Marino',
      nativeRegion: 'San Marino',
      shortName: 'it',
    },
    {
      id: '206',
      code: 'it-CH',
      name: 'Italian',
      nativeName: 'italiano',
      region: 'Switzerland',
      nativeRegion: 'Svizzera',
      shortName: 'it',
    },
    {
      id: '207',
      code: 'sw-CD',
      name: 'Swahili',
      nativeName: 'Kiswahili',
      region: 'Democratic Republic of the Congo',
      nativeRegion: 'Jamhuri ya Kidemokrasia ya Kongo',
      shortName: 'sw',
    },
    {
      id: '208',
      code: 'sw-KE',
      name: 'Swahili',
      nativeName: 'Kiswahili',
      region: 'Kenya',
      nativeRegion: 'Kenya',
      shortName: 'sw',
    },
    {
      id: '209',
      code: 'sw-TZ',
      name: 'Swahili',
      nativeName: 'Kiswahili',
      region: 'Tanzania',
      nativeRegion: 'Tanzania',
      shortName: 'sw',
    },
    {
      id: '210',
      code: 'sw-UG',
      name: 'Swahili',
      nativeName: 'Kiswahili',
      region: 'Uganda',
      nativeRegion: 'Uganda',
      shortName: 'sw',
    },
    {
      id: '211',
      code: 'lv-LV',
      name: 'Latvian',
      nativeName: 'latviešu',
      region: 'Latvia',
      nativeRegion: 'Latvija',
      shortName: 'lv',
    },
    {
      id: '212',
      code: 'lb-LU',
      name: 'Luxembourgish',
      nativeName: 'Lëtzebuergesch',
      region: 'Luxembourg',
      nativeRegion: 'Lëtzebuerg',
      shortName: 'lb',
    },
    {
      id: '213',
      code: 'lt-LT',
      name: 'Lithuanian',
      nativeName: 'lietuvių',
      region: 'Lithuania',
      nativeRegion: 'Lietuva',
      shortName: 'lt',
    },
    {
      id: '214',
      code: 'hu-HU',
      name: 'Hungarian',
      nativeName: 'magyar',
      region: 'Hungary',
      nativeRegion: 'Magyarország',
      shortName: 'hu',
    },
    {
      id: '215',
      code: 'mt-MT',
      name: 'Maltese',
      nativeName: 'Malti',
      region: 'Malta',
      nativeRegion: 'Malta',
      shortName: 'mt',
    },
    {
      id: '216',
      code: 'ms-BN',
      name: 'Malay',
      nativeName: 'Melayu',
      region: 'Brunei',
      nativeRegion: 'Brunei',
      shortName: 'ms',
    },
    {
      id: '217',
      code: 'ms-ID',
      name: 'Malay',
      nativeName: 'Melayu',
      region: 'Indonesia',
      nativeRegion: 'Indonesia',
      shortName: 'ms',
    },
    {
      id: '218',
      code: 'ms-MY',
      name: 'Malay',
      nativeName: 'Melayu',
      region: 'Malaysia',
      nativeRegion: 'Malaysia',
      shortName: 'ms',
    },
    {
      id: '219',
      code: 'ms-SG',
      name: 'Malay',
      nativeName: 'Melayu',
      region: 'Singapore',
      nativeRegion: 'Singapura',
      shortName: 'ms',
    },
    {
      id: '220',
      code: 'nl-AW',
      name: 'Dutch',
      nativeName: 'Nederlands',
      region: 'Aruba',
      nativeRegion: 'Aruba',
      shortName: 'nl',
    },
    {
      id: '221',
      code: 'nl-BE',
      name: 'Dutch',
      nativeName: 'Nederlands',
      region: 'Belgium',
      nativeRegion: 'België',
      shortName: 'nl',
    },
    {
      id: '222',
      code: 'nl-BQ',
      name: 'Dutch',
      nativeName: 'Nederlands',
      region: 'Caribbean Netherlands',
      nativeRegion: 'Bonaire, Sint Eustatius en Saba',
      shortName: 'nl',
    },
    {
      id: '223',
      code: 'nl-CW',
      name: 'Dutch',
      nativeName: 'Nederlands',
      region: 'Curaçao',
      nativeRegion: 'Curaçao',
      shortName: 'nl',
    },
    {
      id: '224',
      code: 'nl-NL',
      name: 'Dutch',
      nativeName: 'Nederlands',
      region: 'Netherlands',
      nativeRegion: 'Nederland',
      shortName: 'nl',
    },
    {
      id: '225',
      code: 'nl-SX',
      name: 'Dutch',
      nativeName: 'Nederlands',
      region: 'Sint Maarten',
      nativeRegion: 'Sint-Maarten',
      shortName: 'nl',
    },
    {
      id: '226',
      code: 'nl-SR',
      name: 'Dutch',
      nativeName: 'Nederlands',
      region: 'Suriname',
      nativeRegion: 'Suriname',
      shortName: 'nl',
    },
    {
      id: '227',
      code: 'nb-NO',
      name: 'Norwegian Bokmål',
      nativeName: 'norsk bokmål',
      region: 'Norway',
      nativeRegion: 'Norge',
      shortName: 'nb',
    },
    {
      id: '228',
      code: 'nn-NO',
      name: 'Norwegian Nynorsk',
      nativeName: 'norsk nynorsk',
      region: 'Norway',
      nativeRegion: 'Noreg',
      shortName: 'nn',
    },
    {
      id: '229',
      code: 'uz-Latn-UZ',
      name: 'Uzbek',
      nativeName: 'oʻzbek',
      region: 'Uzbekistan',
      nativeRegion: 'Oʻzbekiston',
      shortName: 'uz',
    },
    {
      id: '230',
      code: 'pl-PL',
      name: 'Polish',
      nativeName: 'polski',
      region: 'Poland',
      nativeRegion: 'Polska',
      shortName: 'pl',
    },
    {
      id: '231',
      code: 'pt-AO',
      name: 'Portuguese',
      nativeName: 'português',
      region: 'Angola',
      nativeRegion: 'Angola',
      shortName: 'pt',
    },
    {
      id: '232',
      code: 'pt-BR',
      name: 'Portuguese',
      nativeName: 'português',
      region: 'Brazil',
      nativeRegion: 'Brasil',
      shortName: 'pt',
    },
    {
      id: '233',
      code: 'pt-CV',
      name: 'Portuguese',
      nativeName: 'português',
      region: 'Cape Verde',
      nativeRegion: 'Cabo Verde',
      shortName: 'pt',
    },
    {
      id: '234',
      code: 'pt-GQ',
      name: 'Portuguese',
      nativeName: 'português',
      region: 'Equatorial Guinea',
      nativeRegion: 'Guiné Equatorial',
      shortName: 'pt',
    },
    {
      id: '235',
      code: 'pt-GW',
      name: 'Portuguese',
      nativeName: 'português',
      region: 'Guinea-Bissau',
      nativeRegion: 'Guiné-Bissau',
      shortName: 'pt',
    },
    {
      id: '236',
      code: 'pt-LU',
      name: 'Portuguese',
      nativeName: 'português',
      region: 'Luxembourg',
      nativeRegion: 'Luxemburgo',
      shortName: 'pt',
    },
    {
      id: '237',
      code: 'pt-MZ',
      name: 'Portuguese',
      nativeName: 'português',
      region: 'Mozambique',
      nativeRegion: 'Moçambique',
      shortName: 'pt',
    },
    {
      id: '238',
      code: 'pt-PT',
      name: 'Portuguese',
      nativeName: 'português',
      region: 'Portugal',
      nativeRegion: 'Portugal',
      shortName: 'pt',
    },
    {
      id: '239',
      code: 'pt-MO',
      name: 'Portuguese',
      nativeName: 'português',
      region: 'Macao SAR',
      nativeRegion: 'RAE de Macau',
      shortName: 'pt',
    },
    {
      id: '240',
      code: 'pt-ST',
      name: 'Portuguese',
      nativeName: 'português',
      region: 'São Tomé and Príncipe',
      nativeRegion: 'São Tomé e Príncipe',
      shortName: 'pt',
    },
    {
      id: '241',
      code: 'pt-CH',
      name: 'Portuguese',
      nativeName: 'português',
      region: 'Switzerland',
      nativeRegion: 'Suíça',
      shortName: 'pt',
    },
    {
      id: '242',
      code: 'pt-TL',
      name: 'Portuguese',
      nativeName: 'português',
      region: 'Timor-Leste',
      nativeRegion: 'Timor-Leste',
      shortName: 'pt',
    },
    {
      id: '243',
      code: 'ro-MD',
      name: 'Romanian',
      nativeName: 'română',
      region: 'Moldova',
      nativeRegion: 'Republica Moldova',
      shortName: 'ro',
    },
    {
      id: '244',
      code: 'ro-RO',
      name: 'Romanian',
      nativeName: 'română',
      region: 'Romania',
      nativeRegion: 'România',
      shortName: 'ro',
    },
    {
      id: '245',
      code: 'quz-BO',
      name: 'Quechua',
      nativeName: 'Runasimi',
      region: 'Bolivia',
      nativeRegion: 'Bolivia',
      shortName: 'quz',
    },
    {
      id: '246',
      code: 'quz-EC',
      name: 'Quechua',
      nativeName: 'Runasimi',
      region: 'Ecuador',
      nativeRegion: 'Ecuador',
      shortName: 'quz',
    },
    {
      id: '247',
      code: 'quz-PE',
      name: 'Quechua',
      nativeName: 'Runasimi',
      region: 'Peru',
      nativeRegion: 'Perú',
      shortName: 'quz',
    },
    {
      id: '248',
      code: 'sq-XK',
      name: 'Albanian',
      nativeName: 'shqip',
      region: 'Kosovo',
      nativeRegion: 'Kosovë',
      shortName: 'sq',
    },
    {
      id: '249',
      code: 'sq-MK',
      name: 'Albanian',
      nativeName: 'shqip',
      region: 'North Macedonia',
      nativeRegion: 'Maqedonia e Veriut',
      shortName: 'sq',
    },
    {
      id: '250',
      code: 'sq-AL',
      name: 'Albanian',
      nativeName: 'shqip',
      region: 'Albania',
      nativeRegion: 'Shqipëri',
      shortName: 'sq',
    },
    {
      id: '251',
      code: 'sk-SK',
      name: 'Slovak',
      nativeName: 'slovenčina',
      region: 'Slovakia',
      nativeRegion: 'Slovensko',
      shortName: 'sk',
    },
    {
      id: '252',
      code: 'sl-SI',
      name: 'Slovenian',
      nativeName: 'slovenščina',
      region: 'Slovenia',
      nativeRegion: 'Slovenija',
      shortName: 'sl',
    },
    {
      id: '253',
      code: 'sr-Latn-CS',
      name: 'Serbian',
      nativeName: 'srpski',
      region: 'Serbia and Montenegro',
      nativeRegion: 'Srbija i Crna Gora (Bivša)',
      shortName: 'sr',
    },
    {
      id: '254',
      code: 'fi-FI',
      name: 'Finnish',
      nativeName: 'suomi',
      region: 'Finland',
      nativeRegion: 'Suomi',
      shortName: 'fi',
    },
    {
      id: '255',
      code: 'sv-AX',
      name: 'Swedish',
      nativeName: 'svenska',
      region: 'Åland Islands',
      nativeRegion: 'Åland',
      shortName: 'sv',
    },
    {
      id: '256',
      code: 'sv-FI',
      name: 'Swedish',
      nativeName: 'svenska',
      region: 'Finland',
      nativeRegion: 'Finland',
      shortName: 'sv',
    },
    {
      id: '257',
      code: 'sv-SE',
      name: 'Swedish',
      nativeName: 'svenska',
      region: 'Sweden',
      nativeRegion: 'Sverige',
      shortName: 'sv',
    },
    {
      id: '258',
      code: 'mi-NZ',
      name: 'Māori',
      nativeName: 'te reo Māori',
      region: 'New Zealand',
      nativeRegion: 'Aotearoa',
      shortName: 'mi',
    },
    {
      id: '259',
      code: 'vi-VN',
      name: 'Vietnamese',
      nativeName: 'Tiếng Việt',
      region: 'Vietnam',
      nativeRegion: 'Việt Nam',
      shortName: 'vi',
    },
    {
      id: '260',
      code: 'tr-CY',
      name: 'Turkish',
      nativeName: 'Türkçe',
      region: 'Cyprus',
      nativeRegion: 'Kıbrıs',
      shortName: 'tr',
    },
    {
      id: '261',
      code: 'tr-TR',
      name: 'Turkish',
      nativeName: 'Türkçe',
      region: 'Turkey',
      nativeRegion: 'Türkiye',
      shortName: 'tr',
    },
    {
      id: '262',
      code: 'tk-TM',
      name: 'Turkmen',
      nativeName: 'türkmen dili',
      region: 'Turkmenistan',
      nativeRegion: 'Türkmenistan',
      shortName: 'tk',
    },
    {
      id: '263',
      code: 'ca-ES-valencia',
      name: 'Valencian',
      nativeName: 'valencià',
      region: 'Spain',
      nativeRegion: 'Espanya',
      shortName: 'ca',
    },
    {
      id: '264',
      code: 'el-GR',
      name: 'Greek',
      nativeName: 'Ελληνικά',
      region: 'Greece',
      nativeRegion: 'Ελλάδα',
      shortName: 'el',
    },
    {
      id: '265',
      code: 'el-CY',
      name: 'Greek',
      nativeName: 'Ελληνικά',
      region: 'Cyprus',
      nativeRegion: 'Κύπρος',
      shortName: 'el',
    },
    {
      id: '266',
      code: 'be-BY',
      name: 'Belarusian',
      nativeName: 'беларуская',
      region: 'Belarus',
      nativeRegion: 'Беларусь',
      shortName: 'be',
    },
    {
      id: '267',
      code: 'bg-BG',
      name: 'Bulgarian',
      nativeName: 'български',
      region: 'Bulgaria',
      nativeRegion: 'България',
      shortName: 'bg',
    },
    {
      id: '268',
      code: 'ky-KG',
      name: 'Kyrgyz',
      nativeName: 'кыргызча',
      region: 'Kyrgyzstan',
      nativeRegion: 'Кыргызстан',
      shortName: 'ky',
    },
    {
      id: '269',
      code: 'kk-KZ',
      name: 'Kazakh',
      nativeName: 'қазақ тілі',
      region: 'Kazakhstan',
      nativeRegion: 'Қазақстан',
      shortName: 'kk',
    },
    {
      id: '270',
      code: 'mk-MK',
      name: 'Macedonian',
      nativeName: 'македонски',
      region: 'North Macedonia',
      nativeRegion: 'Северна Македонија',
      shortName: 'mk',
    },
    {
      id: '271',
      code: 'mn-MN',
      name: 'Mongolian',
      nativeName: 'монгол',
      region: 'Mongolia',
      nativeRegion: 'Монгол',
      shortName: 'mn',
    },
    {
      id: '272',
      code: 'ru-BY',
      name: 'Russian',
      nativeName: 'русский',
      region: 'Belarus',
      nativeRegion: 'Беларусь',
      shortName: 'ru',
    },
    {
      id: '273',
      code: 'ru-KZ',
      name: 'Russian',
      nativeName: 'русский',
      region: 'Kazakhstan',
      nativeRegion: 'Казахстан',
      shortName: 'ru',
    },
    {
      id: '274',
      code: 'ru-KG',
      name: 'Russian',
      nativeName: 'русский',
      region: 'Kyrgyzstan',
      nativeRegion: 'Киргизия',
      shortName: 'ru',
    },
    {
      id: '275',
      code: 'ru-MD',
      name: 'Russian',
      nativeName: 'русский',
      region: 'Moldova',
      nativeRegion: 'Молдова',
      shortName: 'ru',
    },
    {
      id: '276',
      code: 'ru-RU',
      name: 'Russian',
      nativeName: 'русский',
      region: 'Russia',
      nativeRegion: 'Россия',
      shortName: 'ru',
    },
    {
      id: '277',
      code: 'ru-UA',
      name: 'Russian',
      nativeName: 'русский',
      region: 'Ukraine',
      nativeRegion: 'Украина',
      shortName: 'ru',
    },
    {
      id: '278',
      code: 'sr-Cyrl-BA',
      name: 'Serbian',
      nativeName: 'српски',
      region: 'Bosnia and Herzegovina',
      nativeRegion: 'Босна и Херцеговина',
      shortName: 'sr',
    },
    {
      id: '279',
      code: 'sr-Cyrl-XK',
      name: 'Serbian',
      nativeName: 'српски',
      region: 'Kosovo',
      nativeRegion: 'Косово',
      shortName: 'sr',
    },
    {
      id: '280',
      code: 'tt-RU',
      name: 'Tatar',
      nativeName: 'татар',
      region: 'Russia',
      nativeRegion: 'Россия',
      shortName: 'tt',
    },
    {
      id: '281',
      code: 'uk-UA',
      name: 'Ukrainian',
      nativeName: 'українська',
      region: 'Ukraine',
      nativeRegion: 'Україна',
      shortName: 'uk',
    },
    {
      id: '282',
      code: 'hy-AM',
      name: 'Armenian',
      nativeName: 'հայերեն',
      region: 'Armenia',
      nativeRegion: 'Հայաստան',
      shortName: 'hy',
    },
    {
      id: '283',
      code: 'ka-GE',
      name: 'Georgian',
      nativeName: 'ქართული',
      region: 'Georgia',
      nativeRegion: 'საქართველო',
      shortName: 'ka',
    },
    {
      id: '284',
      code: 'he-IL',
      name: 'Hebrew',
      nativeName: 'עברית',
      region: 'Israel',
      nativeRegion: 'ישראל',
      shortName: 'he',
    },
    {
      id: '285',
      code: 'ur-IN',
      name: 'Urdu',
      nativeName: 'اردو',
      region: 'India',
      nativeRegion: 'بھارت',
      shortName: 'ur',
    },
    {
      id: '286',
      code: 'ur-PK',
      name: 'Urdu',
      nativeName: 'اردو',
      region: 'Pakistan',
      nativeRegion: 'پاکستان',
      shortName: 'ur',
    },
    {
      id: '287',
      code: 'ar-ER',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Eritrea',
      nativeRegion: 'إريتريا',
      shortName: 'ar',
    },
    {
      id: '288',
      code: 'ar-IL',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Israel',
      nativeRegion: 'إسرائيل',
      shortName: 'ar',
    },
    {
      id: '289',
      code: 'ar-JO',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Jordan',
      nativeRegion: 'الأردن',
      shortName: 'ar',
    },
    {
      id: '290',
      code: 'ar-AE',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'United Arab Emirates',
      nativeRegion: 'الإمارات العربية المتحدة',
      shortName: 'ar',
    },
    {
      id: '291',
      code: 'ar-BH',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Bahrain',
      nativeRegion: 'البحرين',
      shortName: 'ar',
    },
    {
      id: '292',
      code: 'ar-DZ',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Algeria',
      nativeRegion: 'الجزائر',
      shortName: 'ar',
    },
    {
      id: '293',
      code: 'ar-PS',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Palestine',
      nativeRegion: 'السلطة الفلسطينية',
      shortName: 'ar',
    },
    {
      id: '294',
      code: 'ar-SD',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Sudan',
      nativeRegion: 'السودان',
      shortName: 'ar',
    },
    {
      id: '295',
      code: 'ar-SO',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Somalia',
      nativeRegion: 'الصومال',
      shortName: 'ar',
    },
    {
      id: '296',
      code: 'ar-001',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'World',
      nativeRegion: 'العالم',
      shortName: 'ar',
    },
    {
      id: '297',
      code: 'ar-IQ',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Iraq',
      nativeRegion: 'العراق',
      shortName: 'ar',
    },
    {
      id: '298',
      code: 'ar-KW',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Kuwait',
      nativeRegion: 'الكويت',
      shortName: 'ar',
    },
    {
      id: '299',
      code: 'ar-SA',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Saudi Arabia',
      nativeRegion: 'المملكة العربية السعودية',
      shortName: 'ar',
    },
    {
      id: '300',
      code: 'ar-MA',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Morocco',
      nativeRegion: 'المملكة المغربية',
      shortName: 'ar',
    },
    {
      id: '301',
      code: 'ar-YE',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Yemen',
      nativeRegion: 'اليمن',
      shortName: 'ar',
    },
    {
      id: '302',
      code: 'ar-TD',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Chad',
      nativeRegion: 'تشاد',
      shortName: 'ar',
    },
    {
      id: '303',
      code: 'ar-TN',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Tunisia',
      nativeRegion: 'تونس',
      shortName: 'ar',
    },
    {
      id: '304',
      code: 'ar-KM',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Comoros',
      nativeRegion: 'جزر القمر',
      shortName: 'ar',
    },
    {
      id: '305',
      code: 'ar-SS',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'South Sudan',
      nativeRegion: 'جنوب السودان',
      shortName: 'ar',
    },
    {
      id: '306',
      code: 'ar-DJ',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Djibouti',
      nativeRegion: 'جيبوتي',
      shortName: 'ar',
    },
    {
      id: '307',
      code: 'ar-SY',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Syria',
      nativeRegion: 'سوريا',
      shortName: 'ar',
    },
    {
      id: '308',
      code: 'ar-OM',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Oman',
      nativeRegion: 'عمان',
      shortName: 'ar',
    },
    {
      id: '309',
      code: 'ar-QA',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Qatar',
      nativeRegion: 'قطر',
      shortName: 'ar',
    },
    {
      id: '310',
      code: 'ar-LB',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Lebanon',
      nativeRegion: 'لبنان',
      shortName: 'ar',
    },
    {
      id: '311',
      code: 'ar-LY',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Libya',
      nativeRegion: 'ليبيا',
      shortName: 'ar',
    },
    {
      id: '312',
      code: 'ar-EG',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Egypt',
      nativeRegion: 'مصر',
      shortName: 'ar',
    },
    {
      id: '313',
      code: 'ar-MR',
      name: 'Arabic',
      nativeName: 'العربية',
      region: 'Mauritania',
      nativeRegion: 'موريتانيا',
      shortName: 'ar',
    },
    {
      id: '314',
      code: 'sd-Arab-PK',
      name: 'Sindhi',
      nativeName: 'سنڌي',
      region: 'Pakistan',
      nativeRegion: 'پاکستان',
      shortName: 'sd',
    },
    {
      id: '315',
      code: 'fa-AF',
      name: 'Persian',
      nativeName: 'فارسی',
      region: 'Afghanistan',
      nativeRegion: 'افغانستان',
      shortName: 'fa',
    },
    {
      id: '316',
      code: 'fa-IR',
      name: 'Persian',
      nativeName: 'فارسی',
      region: 'Iran',
      nativeRegion: 'ایران',
      shortName: 'fa',
    },
    {
      id: '317',
      code: 'ug-CN',
      name: 'Uyghur',
      nativeName: 'ئۇيغۇرچە',
      region: 'China',
      nativeRegion: 'جۇڭخۇا خەلق جۇمھۇرىيىتى',
      shortName: 'ug',
    },
    {
      id: '318',
      code: 'kok-IN',
      name: 'Konkani',
      nativeName: 'कोंकणी',
      region: 'India',
      nativeRegion: 'भारत',
      shortName: 'kok',
    },
    {
      id: '319',
      code: 'ne-NP',
      name: 'Nepali',
      nativeName: 'नेपाली',
      region: 'Nepal',
      nativeRegion: 'नेपाल',
      shortName: 'ne',
    },
    {
      id: '320',
      code: 'ne-IN',
      name: 'Nepali',
      nativeName: 'नेपाली',
      region: 'India',
      nativeRegion: 'भारत',
      shortName: 'ne',
    },
    {
      id: '321',
      code: 'mr-IN',
      name: 'Marathi',
      nativeName: 'मराठी',
      region: 'India',
      nativeRegion: 'भारत',
      shortName: 'mr',
    },
    {
      id: '322',
      code: 'hi-IN',
      name: 'Hindi',
      nativeName: 'हिन्दी',
      region: 'India',
      nativeRegion: 'भारत',
      shortName: 'hi',
    },
    {
      id: '323',
      code: 'as-IN',
      name: 'Assamese',
      nativeName: 'অসমীয়া',
      region: 'India',
      nativeRegion: 'ভাৰত',
      shortName: 'as',
    },
    {
      id: '324',
      code: 'bn-BD',
      name: 'Bengali',
      nativeName: 'বাংলা',
      region: 'Bangladesh',
      nativeRegion: 'বাংলাদেশ',
      shortName: 'bn',
    },
    {
      id: '325',
      code: 'bn-IN',
      name: 'Bengali',
      nativeName: 'বাংলা',
      region: 'India',
      nativeRegion: 'ভারত',
      shortName: 'bn',
    },
    {
      id: '326',
      code: 'pa-IN',
      name: 'Punjabi',
      nativeName: 'ਪੰਜਾਬੀ',
      region: 'India',
      nativeRegion: 'ਭਾਰਤ',
      shortName: 'pa',
    },
    {
      id: '327',
      code: 'gu-IN',
      name: 'Gujarati',
      nativeName: 'ગુજરાતી',
      region: 'India',
      nativeRegion: 'ભારત',
      shortName: 'gu',
    },
    {
      id: '328',
      code: 'or-IN',
      name: 'Odia',
      nativeName: 'ଓଡ଼ିଆ',
      region: 'India',
      nativeRegion: 'ଭାରତ',
      shortName: 'or',
    },
    {
      id: '329',
      code: 'ta-IN',
      name: 'Tamil',
      nativeName: 'தமிழ்',
      region: 'India',
      nativeRegion: 'இந்தியா',
      shortName: 'ta',
    },
    {
      id: '330',
      code: 'ta-SG',
      name: 'Tamil',
      nativeName: 'தமிழ்',
      region: 'Singapore',
      nativeRegion: 'சிங்கப்பூர்',
      shortName: 'ta',
    },
    {
      id: '331',
      code: 'ta-MY',
      name: 'Tamil',
      nativeName: 'தமிழ்',
      region: 'Malaysia',
      nativeRegion: 'மலேசியா',
      shortName: 'ta',
    },
    {
      id: '332',
      code: 'te-IN',
      name: 'Telugu',
      nativeName: 'తెలుగు',
      region: 'India',
      nativeRegion: 'భారతదేశం',
      shortName: 'te',
    },
    {
      id: '333',
      code: 'kn-IN',
      name: 'Kannada',
      nativeName: 'ಕನ್ನಡ',
      region: 'India',
      nativeRegion: 'ಭಾರತ',
      shortName: 'kn',
    },
    {
      id: '334',
      code: 'ml-IN',
      name: 'Malayalam',
      nativeName: 'മലയാളം',
      region: 'India',
      nativeRegion: 'ഇന്ത്യ',
      shortName: 'ml',
    },
    {
      id: '335',
      code: 'si-LK',
      name: 'Sinhala',
      nativeName: 'සිංහල',
      region: 'Sri Lanka',
      nativeRegion: 'ශ්‍රී ලංකාව',
      shortName: 'si',
    },
    {
      id: '336',
      code: 'th-TH',
      name: 'Thai',
      nativeName: 'ไทย',
      region: 'Thailand',
      nativeRegion: 'ไทย',
      shortName: 'th',
    },
    {
      id: '337',
      code: 'lo-LA',
      name: 'Lao',
      nativeName: 'ລາວ',
      region: 'Laos',
      nativeRegion: 'ລາວ',
      shortName: 'lo',
    },
    {
      id: '338',
      code: 'km-KH',
      name: 'Khmer',
      nativeName: 'ខ្មែរ',
      region: 'Cambodia',
      nativeRegion: 'កម្ពុជា',
      shortName: 'km',
    },
    {
      id: '339',
      code: 'am-ET',
      name: 'Amharic',
      nativeName: 'አማርኛ',
      region: 'Ethiopia',
      nativeRegion: 'ኢትዮጵያ',
      shortName: 'am',
    },
    {
      id: '340',
      code: 'zh-Hans-HK',
      name: 'Chinese',
      nativeName: '中文',
      region: 'Hong Kong SAR',
      nativeRegion: '香港特别行政区',
      shortName: 'zh',
    },
    {
      id: '341',
      code: 'zh-Hans-MO',
      name: 'Chinese',
      nativeName: '中文',
      region: 'Macao SAR',
      nativeRegion: '澳门特别行政区',
      shortName: 'zh',
    },
    {
      id: '342',
      code: 'zh-HK',
      name: 'Chinese',
      nativeName: '中文',
      region: 'Hong Kong SAR',
      nativeRegion: '香港特別行政區',
      shortName: 'zh',
    },
    {
      id: '343',
      code: 'zh-SG',
      name: 'Chinese',
      nativeName: '中文',
      region: 'Singapore',
      nativeRegion: '新加坡',
      shortName: 'zh',
    },
    {
      id: '344',
      code: 'zh-TW',
      name: 'Chinese',
      nativeName: '中文',
      region: 'Taiwan',
      nativeRegion: '台灣',
      shortName: 'zh',
    },
    {
      id: '345',
      code: 'zh-CN',
      name: 'Chinese',
      nativeName: '中文',
      region: 'China',
      nativeRegion: '中国',
      shortName: 'zh',
    },
    {
      id: '346',
      code: 'zh-MO',
      name: 'Chinese',
      nativeName: '中文',
      region: 'Macao SAR',
      nativeRegion: '澳門特別行政區',
      shortName: 'zh',
    },
    {
      id: '347',
      code: 'ja-JP',
      name: 'Japanese',
      nativeName: '日本語',
      region: 'Japan',
      nativeRegion: '日本',
      shortName: 'ja',
    },
    {
      id: '348',
      code: 'ko-KP',
      name: 'Korean',
      nativeName: '한국어',
      region: 'North Korea',
      nativeRegion: '조선민주주의인민공화국',
      shortName: 'ko',
    },
    {
      id: '349',
      code: 'ko-KR',
      name: 'Korean',
      nativeName: '한국어',
      region: 'South Korea',
      nativeRegion: '대한민국',
      shortName: 'ko',
    },
  ]

  const uniqueLanguages = computed<UniqueLanguage[]>(() => {
    const languageMap = new Map()

    languages.forEach((item) => {
      if (!languageMap.has(item.name)) {
        languageMap.set(item.name, {
          id: item.id,
          name: item.name,
          nativeName: item.nativeName,
          shortName: item.shortName,
        })
      }
    })

    return Array.from(languageMap.values())
  })
  return {
    languages,
    uniqueLanguages,
  }
}
