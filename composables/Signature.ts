// extensions/Signature.ts
import { Node, mergeAttributes } from '@tiptap/core'

export const Signature = Node.create({
  name: 'signature',

  group: 'block',
  content: 'block+', // allow inline text inside
  atom: false, // ← remove atom to allow editing
  selectable: true,

  addAttributes() {
    return {
      content: {
        default: null,
      },
      dash: {
        default: true, // true = show "-- ", false = hide
        parseHTML: (element) => element.getAttribute('data-dash') !== 'false',
        renderHTML: (attributes) => {
          return {
            'data-dash': attributes.dash,
          }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'signature',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'signature',
      mergeAttributes(HTMLAttributes),
      HTMLAttributes.content,
    ]
  },

  addNodeView() {
    return ({ node }) => {
      const dom = document.createElement('div')
      dom.setAttribute('data-signature', 'true')
      dom.style.marginTop = '1em'

      const shouldShowDash = node.attrs.dash

      if (shouldShowDash) {
        const dashLine = document.createElement('div')
        dashLine.textContent = '-- '
        dashLine.style.color = '#666'
        dashLine.style.marginBottom = '4px'
        dom.appendChild(dashLine)
      }

      const contentDiv = document.createElement('div')
      contentDiv.innerHTML = node.attrs.content

      dom.appendChild(contentDiv)

      return { dom }
    }
  },
})
