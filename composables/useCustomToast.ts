import { POSITION, useToast } from 'vue-toastification'
import CustomToast from '~/components/source/hub/emails/CustomToast.vue'

interface SaToast {
  message: string
  position?: POSITION
  onUndo?: () => void
}

export function useCustomToast() {
  const toast = useToast()

  const saToast = ({
    message,
    position = POSITION.BOTTOM_LEFT,
    onUndo,
  }: SaToast): void => {
    toast(
      {
        component: CustomToast,
        props: {
          message,
          onUndo,
        },
      },
      {
        timeout: 5000,
        position: position,
        closeOnClick: false,
        toastClassName:
          '!bg-transparent !shadow-none !p-0 !rounded-[4px] !my-0',
        bodyClassName: '!p-0',
      },
    )
  }

  return { saToast, toast }
}
