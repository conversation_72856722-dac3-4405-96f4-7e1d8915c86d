// extensions/RawHTML.ts
import { Node, mergeAttributes } from '@tiptap/core'

export const RawHTML = Node.create({
  name: 'rawHTML',

  group: 'block',

  atom: true,

  addAttributes() {
    return {
      html: {
        default: '',
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'raw-html-block',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['raw-html-block', mergeAttributes(HTMLAttributes), 0]
  },

  addNodeView() {
    return ({ node }) => {
      const dom = document.createElement('div')
      dom.innerHTML = node.attrs.html
      return {
        dom,
      }
    }
  },
})
