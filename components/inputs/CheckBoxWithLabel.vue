<template>
  <label
    :for="checkboxId"
    class="inline-flex items-center space-x-2"
    :class="[
      disabled
        ? 'cursor-default opacity-60 pointer-events-none'
        : 'cursor-pointer',
    ]"
  >
    <!-- Checkbox box -->
    <span class="relative flex-shrink-0 min-w-4 w-4 h-4 mt-[-2px]">
      <input
        ref="checkboxRef"
        :id="checkboxId"
        type="checkbox"
        v-model="model"
        :disabled="disabled"
        class="appearance-none size-full rounded-sm border-2 transition-colors duration-200 p-0"
        :class="disabled ? 'cursor-default' : 'cursor-pointer'"
        :style="checkboxStyle"
        role="checkbox"
        :aria-checked="indeterminate ? 'mixed' : model"
      />

      <!-- Icon layer -->
      <span
        v-if="model || indeterminate"
        class="absolute inset-0 size-full flex items-center justify-center pointer-events-none mt-[1px]"
      >
        <fa
          v-if="!indeterminate"
          :icon="['fas', 'check']"
          class="w-2.5 h-2.5 text-white"
        />
        <fa v-else :icon="['fas', 'minus']" class="w-2.5 h-2.5 text-white" />
      </span>
    </span>

    <!-- Text label -->
    <span :class="labelClass">{{ label }}</span>
  </label>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'

const props = withDefaults(
  defineProps<{
    label: string
    id?: string
    disabled?: boolean
    indeterminate?: boolean
    checkColor?: string
    borderColor?: string
    labelClass?: string
  }>(),
  {
    disabled: false,
    indeterminate: false,
    checkColor: '#007aff',
    borderColor: '#707070',
    labelClass: 'text-[#333333]',
  },
)

const model = defineModel<boolean>({ default: false })
const checkboxId = props.id ?? `checkbox-${crypto.randomUUID()}`
const checkboxRef = ref<HTMLInputElement | null>(null)

watch(
  () => props.indeterminate,
  (val) => {
    if (checkboxRef.value) checkboxRef.value.indeterminate = val
  },
  { immediate: true },
)

const checkboxStyle = computed(() => ({
  borderColor: model.value ? props.checkColor : props.borderColor,
  backgroundColor: model.value ? props.checkColor : 'transparent',
}))
</script>
