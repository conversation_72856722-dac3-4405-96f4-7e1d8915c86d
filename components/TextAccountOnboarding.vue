<script setup lang="ts">
import { useStore } from 'vuex'
import { TEXT_AUTH } from '~/constants/urls'

const store = useStore()
const nuxtApp = useNuxtApp()
const route = useRoute()
const { fetch } = useFetched()

const saveProcess = ref<boolean>(false)
const toggleCheckAll = ref<boolean>(false)
const showHideContent = ref<boolean>(true)
const searchTextAccount = ref<string>('')
const socialFeedLength = computed(
  () => store.getters['socialFeed/socialFeedLength'],
)
const globalColorPanel = computed(() => store.state.globalColorPanel)
const allTextAccountLists = computed({
  get() {
    const searchText = searchTextAccount.value.toLowerCase()

    const searchTextAccountLists = JSON.parse(
      JSON.stringify(store.state.header.getFbAuthData),
    )

    searchTextAccountLists.items = (searchTextAccountLists.items || []).filter(
      (item) =>
        item.name?.toLowerCase().startsWith(searchText) ||
        item.username?.toLowerCase().startsWith(searchText),
    )

    return searchTextAccountLists
  },
  set(value) {
    store.commit('header/SET_FB_AUTH_DATA', value)
  },
})
const provider = computed(() => store.state.header.providerName)
onMounted(() => {
  const allChecked = allTextAccountLists.value.items.every((item) => item.added)
  toggleCheckAll.value = allChecked
})

const socialStatusChange = (id: number) => {
  checkIndividualList
  allTextAccountLists.value.items.map((item) => {
    if (item.id === id) {
      item.added = !item.added
      checkIndividualList(id)
    }
    return item
  })
}
const checkIndividualList = (id: string) => {
  const allChecked = allTextAccountLists.value.items.every((item) => item.added)
  toggleCheckAll.value = allChecked
}
const checkUncheckAll = () => {
  toggleCheckAll.value = !toggleCheckAll.value
  allTextAccountLists.value.items.forEach((item) => {
    item.added = toggleCheckAll.value
  })
}
const expandCollapse = ref<boolean>(false)
const toggleExpandCollapse = () => {
  showHideContent.value = false
  setTimeout(() => {
    expandCollapse.value = !expandCollapse.value
  }, 500)
  setTimeout(() => {
    showHideContent.value = true
  }, 600)
}
const connectToFb = async (type: string) => {
  const fbData = {
    profile_id: '',
    ids: [],
    state: '',
    excludeDirectMessageIds: [],
  }
  allTextAccountLists.value.items.forEach((item: { id: number }) => {
    if (item.added) {
      fbData.ids.push(item.id)
    }
  })
  // directMessages.value.items.forEach((item) => {
  //   if (item.excludeDirectMessage) {
  //     fbData.excludeDirectMessageIds.push(item.id)
  //   }
  // })
  fbData.profile_id = allTextAccountLists.value.profile_id
  fbData.state = route.params.token
  saveProcess.value = true
  nuxtApp.$toast('clear')
  if (fbData.ids && fbData.ids.length > 0) {
    try {
      const res = await fetch(`${type}ringcentral/`, {
        body: fbData,
        method: 'PUT',
      })
      if (res.success) {
        if (route.name === 'guest-token') {
          store.commit('guest/SET_CHECK_REQUEST', true)
          store.commit('header/SET_TEXT_ACCOUNT_ONBOARDING', false)
          saveProcess.value = false
        }
        // load feed data
        if (socialFeedLength.value === 0) {
          store.commit('socialFeed/SET_SHOW_ARCHIVE_SYSYTEM_SETTINGS', true)
        } else {
          store.commit('socialFeed/SET_SHOW_ARCHIVE_SYSYTEM_SETTINGS', false)
        }
        if (route.name !== 'guest-token') {
          await fatchSocialFeeds()
          await getAllLatestData()
          await getAllSocialArticle()
        }
        store.dispatch('header/setFbAuthData', allTextAccountLists.value)
        store.dispatch('header/setArchiveFbData', true)
        nuxtApp.$toast('success', {
          message: res.message,
          className: 'toasted-bg-archive',
        })
        store.commit('header/SET_TEXT_ACCOUNT_ONBOARDING', false)
        saveProcess.value = false
        setTimeout(() => {
          store.commit('socialFeed/SHOW_ADD_FEED_MODAL', true)
          store.commit('header/SHOW_ADD_FEED_FINISH_BTN')
        }, 500)
      } else {
        saveProcess.value = false
        store.dispatch('header/setArchiveFbData', false)
        nuxtApp.$toast('error', {
          message: res.message,
          className: 'toasted-bg-alert',
        })
      }
    } catch (err) {
      saveProcess.value = false
      store.dispatch('header/setArchiveFbData', false)
      nuxtApp.$toast('error', {
        message: 'Something went wrong, please try again later',
        className: 'toasted-bg-alert',
      })
    }
  } else {
    nuxtApp.$toast('error', {
      message: 'Need to add atleast one feed',
      className: 'toasted-bg-alert',
    })
    saveProcess.value = false
  }
}
const getAllLatestData = () => store.dispatch('home/getAllLatestData')
const getAllSocialArticle = () => store.dispatch('home/getAllSocialArticle')
const fatchSocialFeeds = () => store.dispatch('socialFeed/fatchSocialFeeds')
</script>

<template>
  <div
    class="md:h-[96%] h-[90%] overflow-hidden transition-all duration-500 ease-in-out z-[11] bg-white rounded-2xl absolute md:top-1/2 top-[76px] left-1/2 transform -translate-x-1/2 md:-translate-y-1/2"
    :class="expandCollapse ? 'w-[98%]' : 'md:min-w-[648px] md:w-1/3 w-[98%]'"
    :style="{ '--color': globalColorPanel.backgroundColor }"
  >
    <div class="flex flex-col h-full">
      <div class="px-6 py-4 border-b-[2px] border-[#F1F2F6]">
        <h1 class="text-lg font-semibold text-[#505050]">
          Text Account Onboarding
        </h1>
      </div>
      <div class="px-6 pt-[18px]">
        <div class="flex items-center space-x-6 pl-2">
          <div
            class="w-9 h-9 flex justify-center items-center relative cursor-pointer hover:bg-[#F1F2F6] rounded-full"
            @click="checkUncheckAll"
          >
            <SharedIconCheckBoxIcon
              v-if="toggleCheckAll"
              class="w-5 h-5"
              :color="globalColorPanel.backgroundColor"
            />
            <!-- <img src="/images/icon/checkbox_image.svg" alt="checkbox_image" /> -->
            <img
              v-else
              class="w-4 h-4"
              src="/images/icon/uncheckbox_image.svg"
              alt="uncheckbox_image"
            />
          </div>
          <div
            class="flex-grow flex items-center space-x-2 h-[36px] bg-[#F1F2F6] px-[26px] py-2 rounded-full"
          >
            <ClientOnly>
              <fa class="size-4 text-[#525252]" :icon="['fas', 'search']" />
            </ClientOnly>
            <input
              type="text"
              class="text-[#707070] w-full bg-[#F1F2F6] border-none outline-none"
              placeholder="Search"
              v-model="searchTextAccount"
            />
          </div>
          <div
            class="w-9 h-9 flex justify-center items-center cursor-pointer hover:bg-[#F1F2F6] rounded-full"
            @click="toggleExpandCollapse"
          >
            <img
              v-if="!expandCollapse"
              src="/images/icon/expand_image.svg"
              alt="expand_image"
            />
            <img
              v-else
              src="/images/icon/collapse_image.svg"
              alt="collapse_image"
            />
          </div>
        </div>
      </div>
      <div class="flex-grow pt-6 overflow-hidden">
        <div class="h-full overflow-y-auto custom-scroll !overflow-x-hidden">
          <div
            v-if="
              allTextAccountLists &&
              allTextAccountLists.items &&
              allTextAccountLists.items.length > 0
            "
            class="px-4 grid gap-y-2 transition-all duration-500 ease-in-out"
            :class="[
              expandCollapse
                ? '2xl:grid-cols-4 xl:grid-cols-3 md:grid-cols-2 grid-cols-1 md:gap-x-2'
                : 'grid-cols-1',
              showHideContent ? 'opacity-100' : 'opacity-0',
            ]"
          >
            <label
              v-for="item in allTextAccountLists.items"
              :key="item.id"
              class="flex items-center space-x-6 pl-6 pr-2.5 py-1.5 rounded-lg cursor-pointer"
              :class="item.added ? 'bg-[#E3EFFF]' : 'bg-white'"
              :for="`${item.id}_${item.name}`"
            >
              <div class="w-4 h-4 relative">
                <input
                  :ref="`${item.id}_${item.name}`"
                  :id="`${item.id}_${item.name}`"
                  type="checkbox"
                  :checked="item.added ? true : false"
                  class="appearance-none w-4 h-4 border border-[#707070] rounded-sm toggle-check-1"
                  :class="item.added ? '' : 'border border-[#707070]'"
                  @click="socialStatusChange(item.id)"
                />
                <ClientOnly>
                  <fa
                    class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                    :icon="['fas', 'check']"
                  />
                </ClientOnly>
              </div>
              <div class="flex space-x-3">
                <div class="flex space-x-4 items-center">
                  <img
                    class="w-10 h-10 rounded-full"
                    :src="item.profile_image_url"
                    alt="text_profile1"
                  />
                  <div class="flex flex-col space-y-2">
                    <p
                      v-if="item.name || item.username"
                      class="text-lg font-semibold text-[#333333]"
                    >
                      {{ item.name || item.username }}
                    </p>
                    <div>
                      <p
                        v-if="item.username"
                        class="text-[#525252] line-clamp-1"
                      >
                        {{ item.username }}
                      </p>
                      <p
                        v-if="item.message"
                        class="text-[#525252] line-clamp-1"
                      >
                        {{ item.message }}
                      </p>
                      <p v-else class="text-[#525252] line-clamp-1">
                        {{ item.type }}
                      </p>
                    </div>
                  </div>
                </div>
                <p
                  v-if="item.time"
                  class="text-sm text-[#525252] whitespace-nowrap"
                >
                  {{ item.time }}
                </p>
              </div>
            </label>
          </div>
          <div
            v-else-if="provider === 'ringcentral'"
            class="w-full h-full flex justify-center items-center"
          >
            <p class="text-ash-dark text-lg">
              There is no text under this account
            </p>
          </div>
        </div>
      </div>
      <div class="flex space-x-4 items-center pt-6 px-4">
        <p class="text-[#333333]">Archive future new contact?</p>
        <div
          class="relative inline-block w-9 mr-2 align-middle select-none transition-all duration-800 ease-in-out"
        >
          <input
            id="createAlertAccess"
            checked
            type="checkbox"
            name="toggle"
            class="toggle-checkbox-button absolute block rounded-full bg-[#393e46] appearance-none cursor-pointer"
          />
          <label
            for="createAlertAccess"
            class="toggle-label block overflow-hidden h-5 rounded-full transition-all duration-800 ease-in-out bg-[#393e46] cursor-pointer"
          ></label>
        </div>
      </div>
      <div class="flex justify-center items-center space-x-4 pt-[30px] pb-4">
        <button
          class="bg-white w-[104px] h-[39px] text-[var(--color)] border-2 border-[var(--color)] rounded-full"
          @click="store.commit('header/SET_TEXT_ACCOUNT_ONBOARDING', false)"
        >
          Cancel
        </button>
        <button
          :disabled="saveProcess"
          class="bg-[var(--color)] relative w-[104px] h-[39px] text-[#FFFFFF] rounded-full flex items-center justify-around"
          @click="connectToFb(TEXT_AUTH)"
        >
          <span>Connect</span>
          <ClientOnly>
            <fa
              v-if="saveProcess"
              class="absolute right-[4px] text-white font-bold animate-spin"
              :icon="['fas', 'spinner']"
            />
          </ClientOnly>
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.toggle-check-1 {
  &:checked {
    @apply bg-[var(--color)];
  }
  &:checked + .check-1 {
    @apply opacity-100;
    color: white;
  }
}
.toggle-btn-wrapper {
  @apply relative inline-block w-9 min-w-9  mr-2 align-middle select-none transition duration-200 ease-in;
}

.toggle-input {
  @apply outline-none focus:outline-none toggle-checkbox absolute block rounded-full bg-white appearance-none cursor-pointer;
}

.toggle-input-label {
  @apply block overflow-hidden h-5 rounded-full bg-ash-default transition-all
                    duration-800
                    ease-in-out cursor-pointer;
}

.toggle-checkbox {
  width: 16px;
  height: 16px;
  border: 0px;
  top: 2px;
  left: 2px;
  transition: all 0.5s ease-in-out;
  background-color: #ffffff;
  &:checked {
    @apply right-0;
    left: 18px;
    transition: all 0.5s ease-in-out;
    background-color: #ffffff;
  }
  &:checked + .toggle-label {
    transition: all 0.5s ease-in-out;
    @apply bg-yellow-primary;
  }
}

.toggle-checkbox-button {
  width: 16px;
  height: 16px;
  border: 0px;
  top: 2px;
  left: 2px;
  transition: all 0.5s ease-in-out;
  background-color: #ffffff;
  &:checked {
    @apply right-0;
    left: 18px;
    transition: all 0.5s ease-in-out;
    background-color: #ffffff;
  }
  &:checked + .toggle-label {
    @apply bg-[var(--color)];
    transition: all 0.5s ease-in-out;
  }
}
</style>
