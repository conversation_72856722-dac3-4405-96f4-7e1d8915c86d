<script setup lang="ts">
defineProps<{
  isOpen: boolean
}>()

const emit = defineEmits<{
  exit: []
  save: []
  discard: []
}>()
</script>

<template>
  <Transition name="page" mode="out-in">
    <div
      v-if="isOpen"
      role="dialog"
      aria-modal="true"
      aria-labelledby="discard-warning-title"
      class="fixed inset-0 z-[999]"
    >
      <div
        class="bg-white w-full max-w-[678px] rounded-2xl fixed z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
      >
        <div class="border-b-[2px] border-[#F1F2F6]">
          <div class="flex justify-between items-center px-6 py-3.5">
            <p
              id="discard-modal-title"
              class="text-lg font-semibold text-[#505050]"
            >
              You have made changes to your settings
            </p>
            <SharedIconHubEmailsCrossIcon
              @click="emit('exit')"
              class="w-4 h-4 cursor-pointer"
            />
          </div>
        </div>
        <div class="border-b-[2px] border-[#F1F2F6] px-6 py-5 text-[#333333]">
          Would you like to save or discard your changes?
        </div>

        <div class="flex justify-end items-center space-x-2 px-6 py-3.5">
          <button
            @click="emit('discard')"
            class="rounded-full font-semibold text-[#4A71D4] border-[1px] border-[#4A71D4] min-w-[108px] h-[35px] px-6"
          >
            Discard
          </button>

          <button
            @click="emit('save')"
            class="rounded-full font-semibold text-[#FFFFFF] bg-[#4A71D4] min-w-[108px] h-[35px] px-6"
          >
            Save
          </button>
        </div>
      </div>
      <div
        class="absolute inset-0 transition-opacity bg-gray-600 opacity-75"
      ></div>
    </div>
  </Transition>
</template>

<style scoped></style>
