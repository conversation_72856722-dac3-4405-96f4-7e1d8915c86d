<script setup lang="ts">
interface Props {
  icons?: Array<{ id: number; label: string; value: string | Component }>
  setSelectedIcon?: Function
}

const props = withDefaults(defineProps<Props>(), {
  icons: [],
  setSelectedIcon: (value: string | Component, label: string) => {},
})
</script>

<template>
  <div
    class="h-[70px] min-h-[70px] w-[36] min-w-[36] px-1 py-1 flex flex-col space-y-1.5 align-box absolute -top-[110px] -left-[2px]"
  >
    <div
      v-for="icon in icons"
      :key="icon.id"
      @click="setSelectedIcon(icon.value, icon.label)"
      class="px-2 py-1.5 hover:bg-[#F1F2F6] rounded cursor-pointer"
    >
      <component :is="icon.value"></component>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.align-box {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
</style>
