<script setup lang="ts">
import { useStore } from 'vuex'
import FolderIcon from '~/components/shared/icon/hub/microsoft/FolderIcon.vue'
import type { OutlookFolder } from '~/types/hubEmailsSettings'
import SearchableDropdown from '../../microsoft/settings/file/SearchableDropdown.vue'

const store = useStore()

const isFolderSelected = ref(false)
const folderName = ref('')
const emlFileCount = ref(0)
const selectedEmlFiles = ref<File[]>([])
const fileInput = ref<HTMLInputElement | null>(null)
const selectedOption = ref<OutlookFolder | null>(null)

const handleBrowseFolder = () => {
  fileInput.value?.click()
}

const resetState = () => {
  isFolderSelected.value = false
  folderName.value = ''
  emlFileCount.value = 0
  selectedEmlFiles.value = []
}

const handleFolderChange = (e: Event) => {
  const target = e.target as HTMLInputElement
  const files = target.files
  if (files && files.length > 0) {
    const firstFilePath = (files[0] as any).webkitRelativePath as string
    folderName.value = firstFilePath.split('/')[0]

    const filtered = Array.from(files).filter((file) =>
      file.name.endsWith('.eml'),
    )

    if (filtered.length === 0) {
      resetState()
      return
    }

    selectedEmlFiles.value = filtered
    emlFileCount.value = filtered.length
    isFolderSelected.value = true
  } else {
    resetState()
  }
}

const closeImportModal = () => {
  store.commit('outlook/SET_IMPORT_EMAIL_MODAL', false)
  resetState()
}

const importEmail = () => {
  if (!selectedOption.value) return
  store.commit('outlook/IMPORT_FILE', {
    folder: folderName.value,
    filesCount: emlFileCount.value,
    destination: selectedOption.value?.title,
    files: selectedEmlFiles.value,
  })
  closeImportModal()
}

const removeFolderSelection = () => {
  resetState()
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}
</script>

<template>
  <div
    class="bg-white w-full max-w-[678px] rounded-2xl fixed z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
  >
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="flex justify-between items-center px-6 py-3.5">
        <p class="text-lg font-semibold text-[#505050]">Import email</p>
        <SharedIconHubEmailsCrossIcon
          @click="closeImportModal"
          class="w-4 h-4 cursor-pointer"
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6] px-6 py-4 text-[#333333]">
      <div class="flex items-center justify-between">
        <p class="text-base leading-[21px] font-semibold text-[#333333]">
          {{ !isFolderSelected ? 'No folder selected yet' : 'Selected folder' }}
        </p>
        <button
          :disabled="isFolderSelected"
          class="rounded-full text-base font-semibold leading-[21px] text-[#FFFFFF] py-[7px] px-6"
          :class="[isFolderSelected ? 'bg-[#C2C2C2]' : 'bg-[#4A71D4]']"
          @click="handleBrowseFolder"
        >
          Browse
        </button>
        <input
          ref="fileInput"
          class="hidden"
          type="file"
          webkitdirectory
          directory
          multiple
          accept=".eml"
          @change="handleFolderChange"
        />
      </div>
      <div
        v-if="isFolderSelected"
        class="h-[51px] w-[200px] bg-[#E3EFFF] mt-2 rounded-lg flex items-center justify-between px-4"
      >
        <div class="flex items-center space-x-2">
          <FolderIcon class="text-[#4a71d4] w-7" />
          <div class="space-y-0.5 text-[#4A71D4]">
            <p class="text-sm leading-[19px]">{{ folderName }}</p>
            <p class="text-xs leading-4">
              Selected {{ emlFileCount }}
              {{ emlFileCount > 1 ? 'files' : 'file' }}
            </p>
          </div>
        </div>
        <button class="rounded-full size-5" @click="removeFolderSelection">
          <ClientOnly>
            <fa class="text-[#525252] size-full" :icon="['fas', 'times']" />
          </ClientOnly>
        </button>
      </div>
      <div class="mt-[22px] text-base leading-[21px] text-[#333333]">
        <p class="font-semibold">Choose destination</p>
        <p class="pt-[14px]">Folder</p>
      </div>
      <SearchableDropdown v-model="selectedOption" :showArrow="false" />
    </div>
    <div class="flex justify-end items-center space-x-2 px-6 py-4">
      <button
        @click="closeImportModal"
        class="rounded-full font-semibold text-[#4A71D4] border-[1px] border-[#4A71D4] w-[104px] h-[35px] px-6"
      >
        Cancel
      </button>

      <button
        :disabled="!isFolderSelected || !selectedOption"
        class="rounded-full font-semibold text-[#FFFFFF] w-[104px] h-[35px] px-6"
        :class="[
          !isFolderSelected || !selectedOption
            ? 'bg-[#C2C2C2]'
            : 'bg-[#4A71D4]',
        ]"
        @click="importEmail"
      >
        Import
      </button>
    </div>
  </div>
</template>

<style scoped></style>
