<script setup lang="ts">
import Color from '@tiptap/extension-color'
import FontFamily from '@tiptap/extension-font-family'
import Highlight from '@tiptap/extension-highlight'
import Image from '@tiptap/extension-image'
import Link from '@tiptap/extension-link'
import Placeholder from '@tiptap/extension-placeholder'
import TextAlign from '@tiptap/extension-text-align'
import TextStyle from '@tiptap/extension-text-style'
import Underline from '@tiptap/extension-underline'
import StarterKit from '@tiptap/starter-kit'
import { Editor } from '@tiptap/vue-3'
import useVuelidate from '@vuelidate/core'
import { required } from '@vuelidate/validators'
import { useStore } from 'vuex'
import { FontSize } from '~/composables/FontSize'
import { CustomIndent } from '~/composables/tiptap-extension-indent'
import type { DefaultSignature, Signature } from '~/types/hubEmailsSettings'
import SignatureEditor from './SignatureEditor.vue'

const emit = defineEmits<{
  defaultSignatureChange: [data: DefaultSignature]
  updateSignature: [data: Signature | null]
  updateSignatureName: []
  createNewSignature: []
  deleteSignature: [string[]]
  isError: [boolean]
}>()

const store = useStore()

const signatureOptions = ref<Signature[]>([])

const defaultSignature = ref<DefaultSignature>({
  newMessageSignatureId: null,
  replyForwardMessageSignatureId: null,
})

const currentSignature = ref<Signature>({
  label: '',
  value: '',
})
const newMessageSignature = ref<Signature | null>(null)
const replyForwardMessageSignature = ref<Signature | null>(null)

const showNewSignatureInput = ref(false)
const showSignatureRenameInput = ref(false)
const isCurrentSignatureDropdownOpen = ref(false)
const isNewMessageSignatureDropdownOpen = ref(false)
const isReplyMessageSignatureDropdownOpen = ref(false)

const handleCurrentSignatureChange = (option: Signature) => {
  currentSignature.value = { ...option }
  editor.value?.commands.setContent(currentSignature.value.value)
}

const handleNewMessageSignatureChange = () => {
  if (newMessageSignature.value && newMessageSignature.value?.id) {
    defaultSignature.value.newMessageSignatureId = newMessageSignature.value?.id
  } else {
    defaultSignature.value.newMessageSignatureId = null
  }
}

const handleReplyMessageSignatureChange = (option: any) => {
  if (
    replyForwardMessageSignature.value &&
    replyForwardMessageSignature.value?.id
  ) {
    defaultSignature.value.replyForwardMessageSignatureId =
      replyForwardMessageSignature.value?.id
  } else {
    defaultSignature.value.replyForwardMessageSignatureId = null
  }
}

const signatureInput = ref<HTMLInputElement | null>(null)

const handleShowRenameSignatureInput = () => {
  showSignatureRenameInput.value = true
  nextTick(() => {
    signatureInput.value?.focus()
  })
  emit('updateSignatureName')
}

const handleShowNewSignatureInput = () => {
  currentSignature.value = {
    label: '',
    value: '',
  }
  editor.value?.commands.setContent('')
  showNewSignatureInput.value = true
  nextTick(() => {
    signatureInput.value?.focus()
  })
  emit('createNewSignature')
}

const deletedSignatures = ref<string[]>([])

const handleDeleteSignature = () => {
  if (currentSignature.value && currentSignature.value?.id) {
    deletedSignatures.value.push(currentSignature.value.id)
    signatureOptions.value = signatureOptions.value.filter(
      (option) => option.id !== currentSignature.value?.id,
    )
    if (signatureOptions.value.length > 0) {
      currentSignature.value = {
        ...signatureOptions.value[0],
      }
      editor.value?.commands.setContent(currentSignature.value.value)
    } else {
      currentSignature.value = {
        label: '',
        value: '',
      }
      editor.value?.commands.setContent('')
      showNewSignatureInput.value = true
    }
    emit('deleteSignature', deletedSignatures.value)
  }
}

const rules = {
  currentSignature: {
    label: { required },
  },
}

const v$ = useVuelidate(rules, { currentSignature })

const handleSignatureInputBlur = () => {
  v$.value.currentSignature.label.$touch()
  emit('isError', v$.value.currentSignature.label.$invalid)
}

watch(
  () => currentSignature.value.label,
  () => {
    if (v$.value.currentSignature.label.$dirty) {
      emit('isError', v$.value.currentSignature.label.$invalid)
    }
  },
)

watch(
  currentSignature,
  () => {
    emit('updateSignature', currentSignature.value)
  },
  { deep: true },
)

watch(
  defaultSignature,
  (newVal) => {
    emit('defaultSignatureChange', newVal)
  },
  { deep: true },
)

const initializeData = () => {
  const signatureOptionsData = store.state.outlook.signatureOptions
  if (signatureOptionsData && signatureOptionsData.length > 0) {
    signatureOptions.value = JSON.parse(JSON.stringify(signatureOptionsData))
    currentSignature.value = {
      ...signatureOptionsData[0],
    }
    editor.value?.commands.setContent(currentSignature.value.value)
    showNewSignatureInput.value = false
  } else {
    signatureOptions.value = []
    currentSignature.value = {
      label: '',
      value: '',
    }
    editor.value?.commands.setContent('')
    showNewSignatureInput.value = true
    emit('createNewSignature')
  }
  if (store.state.outlook.defaultSignature) {
    defaultSignature.value = {
      ...store.state.outlook.defaultSignature,
    }

    const findNewMessageSignature = signatureOptions.value.find(
      (option) => option.id === defaultSignature.value.newMessageSignatureId,
    )
    if (findNewMessageSignature) {
      newMessageSignature.value = findNewMessageSignature
    } else {
      newMessageSignature.value = null
    }
    const findReplyForwardMessageSignature = signatureOptions.value.find(
      (option) =>
        option.id === defaultSignature.value.replyForwardMessageSignatureId,
    )
    if (findReplyForwardMessageSignature) {
      replyForwardMessageSignature.value = findReplyForwardMessageSignature
    } else {
      replyForwardMessageSignature.value = null
    }
  }

  showSignatureRenameInput.value = false
  deletedSignatures.value = []
  v$.value.$reset()
}

defineExpose({
  initializeData,
})

const editor = ref<Editor>()
const isEmpty = ref<boolean>(true)
onMounted(async () => {
  editor.value = new Editor({
    onUpdate({ editor }) {
      // will be called every time content changes
      console.log('Editor content updated')
      console.log('Is empty:', editor.isEmpty)
      isEmpty.value = editor.isEmpty
      currentSignature.value.value = editor.getHTML()
      if (!currentSignature.value.label) {
        handleSignatureInputBlur()
      }
    },
    // onSelectionUpdate({ editor }) {
    //   nextTick(() => {
    //     const { from, to } = editor.state.selection
    //     isTextSelected.value = from !== to && !editor.state.selection.empty
    //   })
    // },
    extensions: [
      Placeholder.configure({
        placeholder: 'Add a signature here',
      }),
      StarterKit.configure({
        blockquote: {
          HTMLAttributes: {
            class: 'border-l-2 border-gray-300 pl-2',
          },
        },
        bulletList: {
          HTMLAttributes: {
            class: 'list-disc ml-5',
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: 'list-decimal ml-7',
          },
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      TextStyle.configure({ mergeNestedSpanStyles: true }),
      Color,
      Highlight.configure({
        multicolor: true,
      }),
      FontFamily.configure({
        types: ['textStyle'],
      }),
      Underline,
      CustomIndent.configure({
        types: ['heading', 'paragraph'],
        minIndent: 0,
        maxIndent: 1000,
        indentLevel: 20,
      }),
      FontSize,
      Link.configure({
        openOnClick: true,
        autolink: true,
        HTMLAttributes: {
          class: 'tiptap-link',
          style: 'color: #2563eb; text-decoration: underline; cursor: pointer;',
        },
      }), // Prevents auto-opening links
      Image,
    ],
    content: '',
    editorProps: {
      attributes: {
        class:
          'w-full h-auto no-scrollbar resize-none textarea-style outline-none border-none bg-white text-[#333333]',
      },
    },
  })
  await nextTick()
  initializeData()
})

onUnmounted(() => {
  emit('isError', false)
  emit('deleteSignature', [])
  emit('updateSignature', null)
  emit('defaultSignatureChange', {
    newMessageSignatureId: null,
    replyForwardMessageSignatureId: null,
  })
})
</script>

<template>
  <div class="w-full">
    <p
      class="px-6 py-4 text-xl leading-[26px] font-semibold text-[#333333] border-b border-[#F1F2F6]"
    >
      Signatures
    </p>
    <div class="px-6 pt-3.5">
      <p class="text-base text-[#525252] font-semibold leading-[21px]">
        Email signature
      </p>
      <p class="text-[#707070] text-base leading-[21px] pt-4">
        You can add and modify signatures that can be added to your emails. You
        can also choose which signature to add by default to your new emails and
        replies.
      </p>
      <p class="pt-[18px] text-base text-[#525252] leading-[21px]">
        Create and edit signatures
      </p>
      <button
        :disabled="deletedSignatures.length > 0"
        class="flex items-center space-x-2 text-[#4A71D4] font-semibold ml-5 my-4"
        :class="[deletedSignatures.length > 0 ? 'opacity-60' : '']"
        @click="handleShowNewSignatureInput"
      >
        <fa :icon="['fas', 'plus']" /><span>New signature</span>
      </button>
      <div class="w-full flex items-center">
        <div
          v-if="showNewSignatureInput || showSignatureRenameInput"
          class="w-full"
        >
          <input
            ref="signatureInput"
            :disabled="deletedSignatures.length > 0"
            v-model="currentSignature.label"
            type="text"
            placeholder="Edit signature name"
            class="text-[#707070] text-base w-full bg-[#F1F2F6] focus:text-[#333333] focus:bg-[#E3EFFF] rounded-full outline-none border-none py-[7px] px-4 h-[35px]"
            @blur="handleSignatureInputBlur"
          />
          <p
            v-if="
              v$.currentSignature.label.$error &&
              v$.currentSignature.label.required.$invalid &&
              deletedSignatures.length === 0
            "
            class="text-sm text-red-500 mt-1 pl-4"
          >
            The signature name field is required
          </p>
        </div>
        <div v-else class="flex items-center space-x-2 w-full">
          <BaseDropsDown
            class="rounded-full w-full dropdown"
            :options="signatureOptions"
            v-model="currentSignature"
            labelKey="label"
            id-key="id"
            :dorpdownPlaceholder="false"
            menuWidth="100%"
            :menuHeight="35"
            dropdownWidth="100%"
            :dropdownMaxHeight="290"
            menuTextColor="#333333"
            dropsdownTextColor="#333333"
            hoverColor="#4A71D4"
            hoverTextColor="#FFFFFF"
            :showSelectedIcon="true"
            :menuBgColor="
              isCurrentSignatureDropdownOpen ? '#E3EFFF' : '#F1F2F6'
            "
            triggerBtnClass="!py-3 text-[#333333] text-base !font-normal"
            @open="
              (isOpen: boolean) => (isCurrentSignatureDropdownOpen = isOpen)
            "
            @change="handleCurrentSignatureChange"
          />
          <button
            :disabled="deletedSignatures.length > 0"
            class="w-[104px] h-[35px] rounded-full text-[#333333] bg-[#F1F2F6] font-semibold"
            :class="[deletedSignatures.length > 0 ? 'opacity-60' : '']"
            @click="handleShowRenameSignatureInput"
          >
            Rename
          </button>
          <button
            class="w-[104px] h-[35px] rounded-full text-[#333333] bg-[#F1F2F6] font-semibold"
            @click="handleDeleteSignature"
          >
            Delete
          </button>
        </div>
      </div>
      <SignatureEditor
        v-if="currentSignature"
        :editor="editor"
        :is-empty="isEmpty"
      />
      <p class="text-base text-[#525252] leading-[21px] pt-[22px]">
        Select default signatures
      </p>
      <div
        class="flex items-center space-x-2 text-base text-[#525252] leading-[21px] mt-2"
      >
        <label>For New Messages:</label>
        <BaseDropsDown
          class="rounded-full w-min"
          :options="signatureOptions"
          v-model="newMessageSignature"
          labelKey="label"
          id-key="id"
          :dorpdownPlaceholder="true"
          :menuWidth="400"
          :menuHeight="35"
          :dropdownWidth="400"
          :dropdownMaxHeight="290"
          menuTextColor="#333333"
          dropsdownTextColor="#333333"
          hoverColor="#4A71D4"
          hoverTextColor="#FFFFFF"
          placeholder="(No signature)"
          :menuBgColor="
            isNewMessageSignatureDropdownOpen ? '#E3EFFF' : '#F1F2F6'
          "
          triggerBtnClass="!py-3 text-[#333333] text-base !font-normal"
          @open="
            (isOpen: boolean) => (isNewMessageSignatureDropdownOpen = isOpen)
          "
          @change="handleNewMessageSignatureChange"
        />
      </div>
      <div
        class="flex items-center space-x-2 text-base text-[#525252] leading-[21px] mt-4"
      >
        <label>For Replies/Forwards:</label>
        <BaseDropsDown
          class="rounded-full w-min"
          :options="signatureOptions"
          v-model="replyForwardMessageSignature"
          labelKey="label"
          id-key="id"
          :dorpdownPlaceholder="true"
          :menuWidth="400"
          :menuHeight="35"
          :dropdownWidth="400"
          :dropdownMaxHeight="290"
          menuTextColor="#333333"
          dropsdownTextColor="#333333"
          hoverColor="#4A71D4"
          hoverTextColor="#FFFFFF"
          placeholder="(No signature)"
          :menuBgColor="
            isReplyMessageSignatureDropdownOpen ? '#E3EFFF' : '#F1F2F6'
          "
          triggerBtnClass="!py-3 text-[#333333] text-base !font-normal"
          @open="
            (isOpen: boolean) => (isReplyMessageSignatureDropdownOpen = isOpen)
          "
          @change="handleReplyMessageSignatureChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
