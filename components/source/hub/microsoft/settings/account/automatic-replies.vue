<script setup lang="ts">
import { format, parse, parseISO } from 'date-fns'
import { useStore } from 'vuex'
import DatePickerModal from './DatePickerModal.vue'
import TextEditor from './TextEditor.vue'

import Color from '@tiptap/extension-color'
import FontFamily from '@tiptap/extension-font-family'
import Highlight from '@tiptap/extension-highlight'
import Image from '@tiptap/extension-image'
import Link from '@tiptap/extension-link'
import Placeholder from '@tiptap/extension-placeholder'
import TextAlign from '@tiptap/extension-text-align'
import TextStyle from '@tiptap/extension-text-style'
import Underline from '@tiptap/extension-underline'
import StarterKit from '@tiptap/starter-kit'
import { Editor } from '@tiptap/vue-3'
import { FontSize } from '~/composables/FontSize'
import { CustomIndent } from '~/composables/tiptap-extension-indent'

const store = useStore()

const emit = defineEmits<{
  autoRepliesChange: [data: any]
}>()

const autoRepliesData = computed(() => store.state.outlook.autoReplies)

const autoReplies = ref({
  autoRepliesOn: false,
  timePeriodOn: false,
  startTime: '2025-06-15T06:00:00Z',
  endTime: '2025-06-16T06:00:00Z',
  blockCalendar: false,
  declineInvitations: false,
  declineAndCancelMeetings: false,
  message: '',
  sendRepliesOn: false,
})

const startDate = ref(new Date())
const endDate = ref(new Date())
const isStartTimeDropdownOpen = ref(false)
const isEndTimeDropdownOpen = ref(false)
const isOpenStartDatePicker = ref(false)
const isOpenEndDatePicker = ref(false)
const isDatePickerDisabled = computed(
  () => !autoReplies.value.timePeriodOn || !autoReplies.value.autoRepliesOn,
)

const startDateIsToday = computed(() => {
  return startDate.value.toDateString() === new Date().toDateString()
})
const endDateIsToday = computed(() => {
  return endDate.value.toDateString() === new Date().toDateString()
})

const moveStartDateToToday = () => {
  startDate.value = new Date()
}
const moveEndDateToToday = () => {
  endDate.value = new Date()
}

const generateTimeSlots = () => {
  const times = []
  let id = 1
  for (let hour = 0; hour < 24; hour++) {
    for (let min = 0; min < 60; min += 30) {
      let period = hour < 12 ? 'AM' : 'PM'
      let displayHour = hour % 12
      if (displayHour === 0) displayHour = 12
      let displayMin = min.toString().padStart(2, '0')
      let label = `${displayHour}:${displayMin} ${period}`
      times.push({
        id: id.toString(),
        label,
        value: label,
      })
      id++
    }
  }
  return times
}

const timeSlots = computed(() => generateTimeSlots())

const startTime = ref(timeSlots.value[0] || null)
const endTime = ref(timeSlots.value[0] || null)

const handleStartDateChange = () => {
  const datePart = format(startDate.value, 'yyyy-MM-dd')

  const combined = parse(
    `${datePart} ${startTime.value.label}`,
    'yyyy-MM-dd hh:mm a',
    new Date(),
  )
  autoReplies.value.startTime = combined.toISOString()
}

const handleEndDateChange = () => {
  console.log(' end changed')
  const datePart = format(endDate.value, 'yyyy-MM-dd')

  const combined = parse(
    `${datePart} ${endTime.value.label}`,
    'yyyy-MM-dd hh:mm a',
    new Date(),
  )
  autoReplies.value.endTime = combined.toISOString()
}

const handleToggle = () => {
  autoReplies.value.autoRepliesOn = !autoReplies.value.autoRepliesOn
}

watch(
  autoReplies,
  (newVal) => {
    emit('autoRepliesChange', newVal)
  },
  { deep: true },
)

const initializeData = () => {
  if (autoRepliesData.value) {
    autoReplies.value = JSON.parse(JSON.stringify(autoRepliesData.value))

    startDate.value = parseISO(autoReplies.value.startTime)
    endDate.value = parseISO(autoReplies.value.endTime)

    const startTimeFormatted = format(startDate.value, 'h:mm a')
    const endTimeFormatted = format(endDate.value, 'h:mm a')

    const startTimeValue = timeSlots.value.find(
      (time) => time.label === startTimeFormatted,
    )

    const endTimeValue = timeSlots.value.find(
      (time) => time.label === endTimeFormatted,
    )

    if (startTimeValue) {
      startTime.value = startTimeValue
    } else {
      console.warn('Start time not found in timeSlots:', startTimeFormatted)
      startTime.value = timeSlots.value[0]
    }
    if (endTimeValue) {
      endTime.value = endTimeValue
    } else {
      console.warn('End time not found in timeSlots:', endTimeFormatted)
      endTime.value = timeSlots.value[0]
    }
    editor.value?.commands.setContent(autoReplies.value.message)
  }
}

defineExpose({
  initializeData,
})

const editor = ref<Editor>()
const isEmpty = ref<boolean>(true)
onMounted(async () => {
  editor.value = new Editor({
    onUpdate({ editor }) {
      // will be called every time content changes
      console.log('Editor content updated')
      console.log('Is empty:', editor.isEmpty)
      isEmpty.value = editor.isEmpty
      autoReplies.value.message = editor.getHTML()
    },
    // onSelectionUpdate({ editor }) {
    //   nextTick(() => {
    //     const { from, to } = editor.state.selection
    //     isTextSelected.value = from !== to && !editor.state.selection.empty
    //   })
    // },
    extensions: [
      Placeholder.configure({
        placeholder: 'Add a message here',
      }),
      StarterKit.configure({
        blockquote: {
          HTMLAttributes: {
            class: 'border-l-2 border-gray-300 pl-2',
          },
        },
        bulletList: {
          HTMLAttributes: {
            class: 'list-disc ml-5',
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: 'list-decimal ml-7',
          },
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      TextStyle.configure({ mergeNestedSpanStyles: true }),
      Color,
      Highlight.configure({
        multicolor: true,
      }),
      FontFamily.configure({
        types: ['textStyle'],
      }),
      Underline,
      CustomIndent.configure({
        types: ['heading', 'paragraph'],
        minIndent: 0,
        maxIndent: 1000,
        indentLevel: 20,
      }),
      FontSize,
      Link.configure({
        openOnClick: true,
        autolink: true,
        HTMLAttributes: {
          class: 'tiptap-link',
          style: 'color: #2563eb; text-decoration: underline; cursor: pointer;',
        },
      }), // Prevents auto-opening links
      Image,
    ],
    content: '',
    editorProps: {
      attributes: {
        class:
          'w-full h-auto no-scrollbar resize-none textarea-style outline-none border-none bg-white text-[#333333]',
      },
    },
  })
  await nextTick()
  initializeData()
})
</script>

<template>
  <div class="w-full">
    <p
      class="px-6 py-4 text-xl leading-[26px] font-semibold text-[#333333] border-b border-[#F1F2F6]"
    >
      Automatic replies
    </p>
    <div class="px-6 mt-[33px]">
      <p class="max-w-[770px] w-full text-base leading-[21px] text-[#707070]">
        Use automatic replies to let others know you're on vacation or aren't
        available to respond to email. You can set your replies to start and end
        at a specific time. Otherwise, they'll continue until you turn them off.
      </p>
      <div class="mt-5">
        <div class="flex items-center space-x-4">
          <InputsToggleInput
            :id="10"
            :select="autoReplies.autoRepliesOn"
            uncheckedLabelBgColor="#FFFFFF"
            uncheckedBorderColor="#707070"
            @toggle-select="handleToggle"
          />
          <p class="text-[#333333] text-base">Turn on automatic replies</p>
        </div>
        <InputsCheckBoxWithLabel
          :disabled="!autoReplies.autoRepliesOn"
          v-model="autoReplies.timePeriodOn"
          checkColor="#4A71D4"
          label="Send replies only during a time period"
          labelClass="text-base text-[#333333]"
          class="mt-[22px]"
        />
        <div class="flex flex-col w-full pl-6">
          <div class="pt-6 flex items-center gap-2">
            <p
              class="text-[#333333] pr-4 w-[91px]"
              :class="[autoReplies.timePeriodOn ? '' : 'opacity-50']"
            >
              Start time:
            </p>
            <DatePickerModal
              v-model="startDate"
              :disabled="isDatePickerDisabled"
              :triggerBtnClass="`${isOpenStartDatePicker && !isDatePickerDisabled ? 'bg-[#E3EFFF] rounded-full' : !isDatePickerDisabled ? 'bg-[#F1F2F6] rounded-full' : ''} w-[158px] py-[7px]`"
              @change="handleStartDateChange"
              @open="(isOpen: boolean) => (isOpenStartDatePicker = isOpen)"
            >
              <template #footer>
                <div class="flex justify-end p-5">
                  <button
                    :disabled="startDateIsToday"
                    class="text-sm"
                    :class="[startDateIsToday ? 'opacity-50' : '']"
                    @click.stop="moveStartDateToToday"
                  >
                    Today
                  </button>
                </div>
              </template>
            </DatePickerModal>
            <BaseDropsDown
              class="border-b-2 border-[#F1F2F6] w-min"
              :class="[isStartTimeDropdownOpen ? 'rounded-full' : '']"
              :options="timeSlots"
              v-model="startTime"
              labelKey="label"
              id-key="id"
              :dorpdownPlaceholder="false"
              :menuWidth="142"
              :menuHeight="35"
              :dropdownWidth="104"
              :dropdownMaxHeight="290"
              menuTextColor="#333333"
              dropsdownTextColor="#333333"
              hoverColor="#4A71D4"
              hoverTextColor="#FFFFFF"
              mailLabelKey=""
              :menuBgColor="!isStartTimeDropdownOpen ? '#FFFFFF' : '#E3EFFF'"
              triggerBtnClass="!py-3 text-[#333333] text-base !font-normal"
              :disabled="
                !autoReplies.timePeriodOn || !autoReplies.autoRepliesOn
              "
              @open="(isOpen: boolean) => (isStartTimeDropdownOpen = isOpen)"
              @change="handleStartDateChange"
            />
          </div>
          <div class="pt-4 flex items-center gap-2">
            <p
              class="text-[#333333] pr-4 w-[91px]"
              :class="[autoReplies.timePeriodOn ? '' : 'opacity-50']"
            >
              End time:
            </p>
            <DatePickerModal
              v-model="endDate"
              :disabled="isDatePickerDisabled"
              :triggerBtnClass="`${isOpenEndDatePicker && !isDatePickerDisabled ? 'bg-[#E3EFFF] rounded-full' : !isDatePickerDisabled ? 'bg-[#F1F2F6] rounded-full' : ''} w-[158px] py-[7px]`"
              @change="handleEndDateChange"
              @open="(isOpen: boolean) => (isOpenEndDatePicker = isOpen)"
            >
              <template #footer>
                <div class="flex justify-end p-5">
                  <button
                    :disabled="endDateIsToday"
                    class="text-sm"
                    :class="[endDateIsToday ? 'opacity-50' : '']"
                    @click.stop="moveEndDateToToday"
                  >
                    Today
                  </button>
                </div>
              </template>
            </DatePickerModal>
            <BaseDropsDown
              class="border-b-2 border-[#F1F2F6] w-min"
              :class="[isEndTimeDropdownOpen ? 'rounded-full' : '']"
              :options="timeSlots"
              v-model="endTime"
              labelKey="label"
              id-key="id"
              :dorpdownPlaceholder="false"
              :menuWidth="142"
              :menuHeight="35"
              :dropdownWidth="104"
              :dropdownMaxHeight="290"
              menuTextColor="#333333"
              dropsdownTextColor="#333333"
              hoverColor="#4A71D4"
              hoverTextColor="#FFFFFF"
              mailLabelKey=""
              :menuBgColor="!isEndTimeDropdownOpen ? '#FFFFFF' : '#E3EFFF'"
              triggerBtnClass="!py-3 text-[#333333] text-base !font-normal"
              :disabled="
                !autoReplies.timePeriodOn || !autoReplies.autoRepliesOn
              "
              @open="(isOpen: boolean) => (isEndTimeDropdownOpen = isOpen)"
              @change="handleEndDateChange"
            />
          </div>
          <template v-if="autoReplies.timePeriodOn">
            <InputsCheckBoxWithLabel
              :disabled="!autoReplies.autoRepliesOn"
              v-model="autoReplies.blockCalendar"
              checkColor="#4A71D4"
              label="Block my calendar for this period"
              labelClass="text-base text-[#333333]"
              class="mt-[22px] w-min whitespace-nowrap"
            />
            <InputsCheckBoxWithLabel
              :disabled="!autoReplies.autoRepliesOn"
              v-model="autoReplies.declineInvitations"
              checkColor="#4A71D4"
              label="Automatically decline new invitations for events that occur during this period"
              labelClass="text-base text-[#333333]"
              class="mt-3.5 w-min whitespace-nowrap"
            />
            <div class="flex items-center space-x-2 mt-3.5">
              <InputsCheckBoxWithLabel
                :disabled="!autoReplies.autoRepliesOn"
                v-model="autoReplies.declineAndCancelMeetings"
                checkColor="#4A71D4"
                label="Decline and cancel my meetings during this period"
                labelClass="text-base text-[#333333]"
              />
              <span
                title="If you select this box, existing meetings you organized will be canceled and meetings you're invited to will be declined. To clear appointments from your calendar, select them from the list below."
              >
                <SharedIconInfoRounded class="text-[#434343]" />
              </span>
            </div>
          </template>
        </div>
        <TextEditor
          v-if="autoReplies.autoRepliesOn"
          :editor="editor"
          :is-empty="isEmpty"
        />
        <InputsCheckBoxWithLabel
          :disabled="!autoReplies.autoRepliesOn"
          v-model="autoReplies.sendRepliesOn"
          checkColor="#4A71D4"
          label="Send replies only to contacts"
          labelClass="text-base text-[#333333]"
          class="mt-[22px]"
        />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
