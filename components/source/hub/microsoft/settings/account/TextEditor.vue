<script setup lang="ts">
import { Editor } from '@tiptap/vue-3'

interface Props {
  editor?: Editor | null
  isEmpty?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  editor: null,
  isEmpty: true,
})
</script>

<template>
  <div
    class="relative text-editor w-full h-[200px] p-4 pt-0 border border-[#C2C2C2]/60 rounded-lg mt-6 overflow-y-auto"
  >
    <SourceHubMicrosoftSettingsAccountInputModifier
      :editor="editor"
      class="sticky bg-white z-1 w-full h-[32px] pt-2"
    />
    <SourceHubMicrosoftTextInput
      class="mt-3.5 h-[calc(100%-46px)] overflow-y-auto custom-scroll"
      :editor="editor"
      :is-empty="isEmpty"
    />
  </div>
</template>

<style scoped></style>
