<script setup lang="ts">
import { format } from 'date-fns'

const props = withDefaults(
  defineProps<{
    triggerBtnClass?: string
    dropdownClass?: string
    disabled?: boolean
  }>(),
  {
    triggerBtnClass: 'w-[158px] py-[7px]',
    dropdownClass: 'top-full left-0 mt-2',
    disabled: false,
  },
)

const emit = defineEmits<{
  change: [date: Date]
  open: [isOpen: boolean]
}>()

const model = defineModel<Date | null>({ default: new Date() })

const calendarContainerRef = ref<HTMLElement | null>(null)
const isOpen = ref(false)

const displayDate = computed(() =>
  model.value ? format(model.value, 'M/d/yyyy') : 'Select Date',
)

const toggleCalendar = () => {
  if (props.disabled) return
  isOpen.value = !isOpen.value
  emit('open', isOpen.value)
}

const handleClickOutsideOfCalendar = (event: Event) => {
  if (
    calendarContainerRef.value &&
    !calendarContainerRef.value.contains(event.target as Node) &&
    isOpen.value
  ) {
    isOpen.value = false
  }
  emit('open', isOpen.value)
}

onMounted(() => {
  document.addEventListener('click', handleClickOutsideOfCalendar)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutsideOfCalendar)
})
</script>
<template>
  <div ref="calendarContainerRef" class="relative">
    <button
      @click.stop="toggleCalendar"
      :disabled="disabled"
      class="px-4 text-base leading-5 flex items-center justify-between border-b-2 border-[#F1F2F6]"
      :class="[triggerBtnClass]"
    >
      <span :class="[!disabled ? '' : 'opacity-80']">{{ displayDate }}</span>
      <slot name="calenderIcon">
        <SharedIconCalendar
          class="h-4 text-[#525252]"
          :class="[!disabled ? '' : 'opacity-40']"
        />
      </slot>
    </button>
    <div
      v-if="isOpen && !disabled"
      class="picker-modal absolute bg-white shadow-2xl p-2 rounded-lg z-10"
      :class="[dropdownClass]"
    >
      <VDatePicker
        :model-value="model"
        borderless
        @update:model-value="
          (date: Date) => {
            model = date
            emit('change', date)
          }
        "
      />
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.vc-monthly .is-not-in-month *) {
  opacity: 0.5;
}
.date-select-box > .picker-modal {
  box-shadow: 0px 0px 8px #2228313d;
  margin-top: 0px;
}
.date-select-box > .date-display-button {
  height: 27px;
}
</style>
