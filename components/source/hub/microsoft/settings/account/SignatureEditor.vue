<script setup lang="ts">
import { Editor } from '@tiptap/vue-3'

interface Props {
  editor?: Editor | null
  isEmpty?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  editor: null,
  isEmpty: true,
})
</script>

<template>
  <div
    class="relative text-editor w-full h-[280px] border border-[#C2C2C2]/60 px-4 rounded-lg mt-4 overflow-hidden"
  >
    <SourceHubMicrosoftTextInput
      class="mt-3.5 h-[calc(100%-50px)] overflow-y-auto custom-scroll"
      :editor="editor"
      :is-empty="isEmpty"
    />
    <SourceHubMicrosoftSettingsAccountInputModifier
      :editor="editor"
      class="sticky bg-white z-1 w-full h-[32px]"
    />
  </div>
</template>

<style scoped></style>
