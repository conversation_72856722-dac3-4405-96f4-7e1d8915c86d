<script setup lang="ts">
import type { Language } from '~/types/hubEmailsSettings'

const { languages } = useWorldLanguages()

const selectedOption = defineModel<Language | null>({ default: null })
const dropdownRef = ref<HTMLDivElement | null>(null)
const searchInput = ref<HTMLInputElement | null>(null)

const isOpen = ref(false)
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
  if (isOpen.value) {
    nextTick(() => {
      searchInput.value?.focus()
    })
  }
}
const handleSelectOption = (option: Language) => {
  selectedOption.value = option
  isOpen.value = false
}

const handleClickOutside = (event: Event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div ref="dropdownRef" class="w-full relative">
    <button
      class="w-full rounded-full py-[7px] px-4 text-base font-normal leading-[21px] text-[#333333] flex items-center justify-between gap-2"
      :class="isOpen ? 'bg-[#E3EFFF]' : 'bg-[#F1F2F6]'"
      @click="toggleDropdown"
    >
      <span v-if="selectedOption"
        >{{ selectedOption.name }} ({{ selectedOption.region }})</span
      >
      <span v-else>Choose Language</span>
      <ClientOnly>
        <fa
          class="text-[#4A71D4] text-xl transition-all duration-300 ease-in-out"
          :class="[isOpen ? 'rotate-180' : '']"
          :icon="['fas', 'caret-down']"
        />
      </ClientOnly>
    </button>

    <div
      v-if="isOpen"
      class="absolute z-10 w-full shadow-xl rounded-lg overflow-hidden"
    >
      <div
        class="w-full bg-white rounded-lg max-h-[300px] overflow-y-auto custom-scroll pb-1"
      >
        <div
          v-for="option in languages"
          :key="option.id"
          class="flex items-center px-4 py-[7px] text-base leading-[21px] hover:bg-[#4A71D4] text-[#525252] hover:text-white cursor-pointer gap-2"
          @click="handleSelectOption(option)"
        >
          <span>{{ option.name }}</span>
          <span>({{ option.region }})</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
