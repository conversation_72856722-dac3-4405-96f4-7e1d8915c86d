<script setup lang="ts">
import { useStore } from 'vuex'
import type { Search } from '~/types/hubEmailsSettings'

interface SearchScopeOptions {
  label: string
  value: string
  description: string
}

const emit = defineEmits<{
  changeSearch: [search: Search]
}>()

const store = useStore()

const searchScopeOptions = ref<SearchScopeOptions[]>([
  {
    label: 'All folders when searching from the inbox',
    value: 'all_folders_inbox',
    description: '',
  },
  {
    label: 'All folders',
    value: 'all_folders',
    description: '',
  },
  {
    label: 'Current folder',
    value: 'current_folder',
    description: '',
  },
])

const search = ref<Search>({
  searchScope: 'all_folders_inbox',
  includeDeletedItems: false,
  showTopThreeRelevantResults: false,
})

watch(
  search,
  () => {
    emit('changeSearch', search.value)
  },
  { deep: true },
)

const initializeSearchData = () => {
  const searchData = store.state.outlook.search as Search
  search.value = { ...searchData }
}

defineExpose({
  initializeSearchData,
})

onMounted(async () => {
  await nextTick()
  initializeSearchData()
})
</script>

<template>
  <div class="w-full">
    <p
      class="px-6 py-4 text-xl leading-[26px] font-semibold text-[#333333] border-b border-[#F1F2F6]"
    >
      Search
    </p>
    <div class="px-6">
      <div class="py-4 space-y-3.5 border-b-2 border-[#F1F2F6]">
        <p class="text-base leading-[21px] text-[#333333] font-semibold">
          Search scope
        </p>
        <InputsRadioOptionGroup
          v-model="search.searchScope"
          :options="searchScopeOptions"
          labelClass="font-normal"
          class="!space-y-3.5"
        />
      </div>
      <div class="flex flex-col py-4 space-y-3.5 border-b-2 border-[#F1F2F6]">
        <p class="text-base leading-[21px] text-[#333333] font-semibold">
          Search results
        </p>
        <InputsCheckBoxWithLabel
          v-model="search.includeDeletedItems"
          checkColor="#4A71D4"
          label="Include deleted items"
          labelClass="text-base text-[#333333] leading-[21px]"
          class="w-min whitespace-nowrap"
        />
        <InputsCheckBoxWithLabel
          v-model="search.showTopThreeRelevantResults"
          checkColor="#4A71D4"
          label="Show top three most relevant results"
          labelClass="text-base text-[#333333] leading-[21px]"
          class="w-min whitespace-nowrap"
        />
      </div>
      <div class="py-4">
        <p class="text-base leading-[21px] text-[#333333] font-semibold">
          Search history and suggestions
        </p>
        <p class="pt-3.5 text-base leading-[21px] text-[#333333]">
          To see search details or take action on your search history or
          suggested people, go to your Privacy and data settings.
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
