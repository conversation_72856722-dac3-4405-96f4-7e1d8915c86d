<script setup lang="ts">
import type { Timezone } from '~/types/hubEmailsSettings'

const props = defineProps<{
  allTimesZone: Timezone[]
}>()

const selectedOption = defineModel<Timezone | null>({ default: null })
const dropdownRef = ref<HTMLDivElement | null>(null)
const searchInput = ref<HTMLInputElement | null>(null)
const showSearchInput = ref(false)
const isInputFocused = ref(false)
const isOpen = ref(false)
const searchText = ref('')

const toggleDropdown = () => {
  isOpen.value = !isOpen.value
  showSearchInput.value = false

  if (isOpen.value) {
    nextTick(() => {
      searchInput.value?.focus()
    })
    searchText.value = ''
  }
}
const handleSelectOption = (option: any) => {
  selectedOption.value = option
  isOpen.value = false
  showSearchInput.value = false
  searchText.value = ''
}
const handleShowSearchInput = () => {
  showSearchInput.value = true
  nextTick(() => {
    searchInput.value?.focus()
  })
}
const filteredTimeZones = computed(() => {
  if (!searchText.value) {
    return props.allTimesZone
  }
  return props.allTimesZone.filter((tz: any) =>
    tz.text.toLowerCase().includes(searchText.value.toLowerCase()),
  )
})

const handleClickOutside = (event: Event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false
    searchText.value = ''
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div ref="dropdownRef" class="w-full relative">
    <div
      class="w-full rounded-full px-4 text-base font-normal leading-[21px] text-[#333333] flex items-center justify-between gap-2"
      :class="[
        isOpen || isInputFocused ? 'bg-[#E3EFFF]' : 'bg-[#F1F2F6]',
        showSearchInput ? 'py-1' : 'py-[7px]',
      ]"
    >
      <input
        v-if="showSearchInput"
        ref="searchInput"
        v-model="searchText"
        type="text"
        class="w-full rounded-sm text-base leading-[21px] text-[#333333] outline-none border-none bg-[#F1F2F6] focus:bg-[#E3EFFF]"
        placeholder="Search for a time zone"
        @focus="isInputFocused = true"
        @blur="isInputFocused = false"
      />
      <p v-else @click="handleShowSearchInput" class="w-full">
        {{ selectedOption ? selectedOption.text : 'Choose Time Zone' }}
      </p>
      <button @click="toggleDropdown">
        <ClientOnly>
          <fa
            class="text-[#4A71D4] text-xl transition-all duration-300 ease-in-out"
            :class="[isOpen ? 'rotate-180' : '']"
            :icon="['fas', 'caret-down']"
          />
        </ClientOnly>
      </button>
    </div>

    <div
      v-if="searchText"
      class="absolute z-10 w-[320px] shadow-xl rounded-lg overflow-hidden"
    >
      <div
        class="w-full bg-white rounded-lg max-h-[240px] overflow-y-auto custom-scroll pb-1"
      >
        <template v-if="filteredTimeZones.length === 0">
          <div class="px-4 py-2 text-base leading-[21px] text-[#525252]">
            No results found
          </div>
        </template>
        <template v-else>
          <div
            v-for="option in filteredTimeZones"
            :key="option.id"
            class="flex items-center px-4 py-[7px] text-base leading-[21px] hover:bg-[#4A71D4] text-[#525252] hover:text-white cursor-pointer gap-2"
            @click="handleSelectOption(option)"
          >
            <span>{{ option.text }}</span>
          </div>
        </template>
      </div>
    </div>

    <div
      v-if="isOpen"
      class="absolute z-10 w-full shadow-xl rounded-lg overflow-hidden"
    >
      <div
        class="w-full bg-white rounded-lg max-h-[300px] overflow-y-auto custom-scroll pb-1"
      >
        <div
          v-for="option in allTimesZone"
          :key="option.id"
          class="flex items-center px-4 py-[7px] text-base leading-[21px] hover:bg-[#4A71D4] text-[#525252] hover:text-white cursor-pointer gap-2"
          @click="handleSelectOption(option)"
        >
          <span>{{ option.text }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
