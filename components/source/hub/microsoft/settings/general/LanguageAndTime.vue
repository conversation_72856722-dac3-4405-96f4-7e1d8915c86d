<script setup lang="ts">
import { useStore } from 'vuex'
import type {
  DateFormat,
  Language,
  LanguageAndTime,
  TimeFormat,
  Timezone,
} from '~/types/hubEmailsSettings'
import LanguageDropdown from './LanguageDropdown.vue'
import TimezoneDropdown from './TimezoneDropdown.vue'

const emit = defineEmits<{
  changeLanguageAndTime: [languageAndTime: LanguageAndTime]
  changeTimezones: [timezones: string[]]
}>()

const store = useStore()

const { languages } = useWorldLanguages()
const allDate = computed<DateFormat[]>(() => store.state.system.allDate)
const timeFormats = computed<TimeFormat[]>(() => store.state.system.timeFormats)
const allTimesZone = computed<Timezone[]>(() => store.state.system.allTimesZone)

const isDateFormatDropdownOpen = ref(false)
const isTimeFormatDropdownOpen = ref(false)

const currentLanguage = ref<Language | null>(null)
const currentDateFormat = ref<DateFormat | null>(null)
const currentTimeFormat = ref<TimeFormat | null>(null)
const currentTimeZone = ref<Timezone | null>(null)

const languageAndTime = ref<LanguageAndTime>({
  language: 'en-US',
  dateFormat: 'MM-dd-yyyy',
  timeFormat: 'HH:mm',
  askToUpdateTimeZone: true,
  timeZone: 'US/Arizona',
})

const handleLanguageChange = (option: Language | null) => {
  languageAndTime.value.language = option?.code || 'en-US'
}

const handleDateFormatChange = (option: DateFormat | null) => {
  languageAndTime.value.dateFormat = option?.value || 'MM-dd-yyyy'
}

const handleTimeFormatChange = (option: TimeFormat | null) => {
  languageAndTime.value.timeFormat = option?.value || 'HH:mm'
}

const handleTimezoneChange = (option: Timezone | null) => {
  languageAndTime.value.timeZone = option?.value || 'US/Arizona'
}

const initializeLanguageTimeData = () => {
  const languageAndTimeSettings = store.state.outlook
    .languageAndTime as LanguageAndTime
  languageAndTime.value = { ...languageAndTimeSettings }
  const findCurrentLanguage = languages.findIndex(
    (lang) => lang.code === languageAndTime.value.language,
  )
  if (findCurrentLanguage !== -1) {
    currentLanguage.value = languages[findCurrentLanguage]
  }
  const findCurrentDateFormat = allDate.value.findIndex(
    (date) => date.value === languageAndTime.value.dateFormat,
  )
  if (findCurrentDateFormat !== -1) {
    currentDateFormat.value = allDate.value[findCurrentDateFormat]
  }
  const findCurrentTimeFormat = timeFormats.value.findIndex(
    (time) => time.value === languageAndTime.value.timeFormat,
  )
  if (findCurrentTimeFormat !== -1) {
    currentTimeFormat.value = timeFormats.value[findCurrentTimeFormat]
  }
  const findCurrentTimeZone = allTimesZone.value.findIndex(
    (tz) => tz.value === languageAndTime.value.timeZone,
  )
  if (findCurrentTimeZone !== -1) {
    currentTimeZone.value = allTimesZone.value[findCurrentTimeZone]
  }
  const otherTimeZonesData = store.state.outlook.otherTimeZones as string[]
  otherTimezonesLocal.value = otherTimeZonesData.map((tz: string) => {
    const findOtherTimeZone = allTimesZone.value.find(
      (tz2) => tz2.value === tz,
    )!
    return {
      ...findOtherTimeZone,
      id: new Date().getTime() + Math.random() * 1000,
    }
  })
}

const otherTimezonesLocal = ref<Timezone[]>([])
const otherTimeZones = ref<string[]>([])

const addTimezone = () => {
  if (!currentTimeZone.value) return
  const newId = new Date().getTime()
  otherTimezonesLocal.value.push({
    ...currentTimeZone.value,
    id: newId,
  })
}

watch(
  otherTimezonesLocal,
  (newVal) => {
    otherTimeZones.value = newVal.map((tz) => tz.value)
  },
  { deep: true },
)

watch(
  otherTimeZones,
  () => {
    emit('changeTimezones', otherTimeZones.value)
  },
  { deep: true },
)

const removeTimezone = (timezoneId: number) => {
  otherTimezonesLocal.value = otherTimezonesLocal.value.filter(
    (tz) => tz.id !== timezoneId,
  )
}

const updateOtherTimezone = (
  timezoneId: number,
  newTimezone: Timezone | null,
) => {
  const index = otherTimezonesLocal.value.findIndex(
    (tz) => tz.id === timezoneId,
  )
  if (index !== -1 && newTimezone) {
    otherTimezonesLocal.value[index] = {
      ...otherTimezonesLocal.value[index],
      text: newTimezone.text,
      value: newTimezone.value,
    }
  }
}

defineExpose({
  initializeLanguageTimeData,
})

onMounted(async () => {
  await nextTick()
  initializeLanguageTimeData()
})

watch(
  languageAndTime,
  () => {
    emit('changeLanguageAndTime', languageAndTime.value)
  },
  { deep: true },
)
</script>

<template>
  <div class="w-full">
    <p
      class="px-6 py-4 text-xl leading-[26px] font-semibold text-[#333333] border-b border-[#F1F2F6]"
    >
      Language and time
    </p>
    <div class="px-6 pt-4 space-y-3.5">
      <div class="space-y-2">
        <p class="text-base leading-[21px] text-[#525252]">
          Language (Country/Region)
        </p>
        <LanguageDropdown
          v-model="currentLanguage"
          @update:model-value="handleLanguageChange"
        />
      </div>
      <div class="space-y-2">
        <p class="text-base leading-[21px] text-[#525252]">
          Date format (for example, September 1, 2025 is displayed as follows)
        </p>
        <BaseDropsDown
          class="rounded-full w-full dropdown"
          :options="allDate"
          v-model="currentDateFormat"
          labelKey="text"
          id-key="id"
          :dorpdownPlaceholder="false"
          menuWidth="100%"
          :menuHeight="35"
          dropdownWidth="100%"
          :dropdownMaxHeight="290"
          menuTextColor="#333333"
          dropsdownTextColor="#333333"
          hoverColor="#4A71D4"
          hoverTextColor="#FFFFFF"
          :menuBgColor="isDateFormatDropdownOpen ? '#E3EFFF' : '#F1F2F6'"
          triggerBtnClass="!py-3 text-[#333333] text-base !font-normal"
          @open="(isOpen: boolean) => (isDateFormatDropdownOpen = isOpen)"
          @change="handleDateFormatChange"
        />
      </div>
      <div class="space-y-2">
        <p class="text-base leading-[21px] text-[#525252]">Time format</p>
        <BaseDropsDown
          class="rounded-full w-full dropdown"
          :options="timeFormats"
          v-model="currentTimeFormat"
          labelKey="text"
          id-key="text"
          :dorpdownPlaceholder="false"
          menuWidth="100%"
          :menuHeight="35"
          dropdownWidth="100%"
          :dropdownMaxHeight="290"
          menuTextColor="#333333"
          dropsdownTextColor="#333333"
          hoverColor="#4A71D4"
          hoverTextColor="#FFFFFF"
          :menuBgColor="isTimeFormatDropdownOpen ? '#E3EFFF' : '#F1F2F6'"
          triggerBtnClass="!py-3 text-[#333333] text-base !font-normal"
          @open="(isOpen: boolean) => (isTimeFormatDropdownOpen = isOpen)"
          @change="handleTimeFormatChange"
        />
      </div>
      <div class="h-px w-full bg-[#F1F2F6]" />
    </div>
    <div class="px-6 py-4">
      <p class="text-base leading-[21px] font-semibold text-[#333333]">
        Time zone
      </p>
      <InputsCheckBoxWithLabel
        v-model="languageAndTime.askToUpdateTimeZone"
        checkColor="#4A71D4"
        label="When I travel across time zones, ask me if I want to update my time zone"
        labelClass="text-base text-[#333333] leading-[21px]"
        class="mt-3.5"
      />
      <div class="space-y-2 mt-[22px]">
        <p class="text-base leading-[21px] text-[#525252]">
          Display my calendar in time zone:
        </p>
        <TimezoneDropdown
          v-model="currentTimeZone"
          :allTimesZone="allTimesZone"
          @update:model-value="handleTimezoneChange"
        />
      </div>
      <div v-if="otherTimezonesLocal.length > 0" class="space-y-2 mt-[22px]">
        <p class="text-base leading-[21px] text-[#525252]">Other time zone:</p>
        <div
          v-for="timezone in otherTimezonesLocal"
          :key="timezone.id"
          class="space-y-2"
        >
          <TimezoneDropdown
            :allTimesZone="allTimesZone"
            :model-value="timezone"
            @update:model-value="
              (newTimezone) => updateOtherTimezone(timezone.id, newTimezone)
            "
          />
          <button
            class="flex items-center space-x-2 px-6 mt-4 text-[#525252] hover:text-red-500 transition-colors"
            @click="removeTimezone(timezone.id)"
          >
            <SharedIconEditorDelete /> <span>Remove</span>
          </button>
        </div>
      </div>
      <button
        class="bg-[#F1F2F6] text-[#525252] rounded-full px-6 mt-4 text-base leading-5 py-2 font-semibold hover:bg-[#E3EFFF] transition-colors"
        @click="addTimezone"
      >
        <ClientOnly>
          <fa class="text-[#525252] text-base mr-1" :icon="['fas', 'plus']" />
        </ClientOnly>
        Add time zone
      </button>
    </div>
  </div>
</template>

<style scoped></style>
