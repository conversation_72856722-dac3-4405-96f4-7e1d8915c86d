<script setup lang="ts">
import { useStore } from 'vuex'
import type { History, LanguageAndTime } from '~/types/hubEmailsSettings'

const props = defineProps<{
  history: History
}>()

const store = useStore()
const showStatus = ref(false)
const showMenu = ref(false)
const dropdownRef = ref<HTMLDivElement | null>(null)
const languageAndTime = computed<LanguageAndTime>(
  () => store.state.outlook.languageAndTime,
)

const formattedHistoryDate = computed(() => {
  const { timeZone, dateFormat, timeFormat } = languageAndTime.value

  const formatString = isLessThanWeek(props.history.date, timeZone)
    ? `EEE ${timeFormat}`
    : `${dateFormat} ${timeFormat}`

  return zonedDateTime(props.history.date, timeZone, formatString)
})

const handleRemoveHistory = () => {
  store.commit('outlook/REMOVE_A_IMPORT_FILE_HISTORY', props.history)
  showMenu.value = false
}

const handleClickOutside = (event: Event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    showMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div>
    <div
      class="grid grid-cols-2 p-4 rounded-md shadow-[0_0_2px_rgba(0,0,0,0.12),_0_2px_4px_rgba(0,0,0,0.14)] z-1 bg-white"
    >
      <div class="">
        <p class="text-[#333333] text-base">{{ history.folder }}</p>
        <p class="text-[#333333] text-sm space-x-1">
          <span>{{ formattedHistoryDate }}</span>
          <span>|</span>
          <span>{{ history.filesCount }} files</span>
        </p>
      </div>
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <SharedIconHubMicrosoftImportFileDefaultFolder
            class="size-7 text-[#333333]"
          />
          <span class="text-[#333333] text-sm">{{ history.destination }}</span>
        </div>
        <div class="flex items-center gap-2">
          <div ref="dropdownRef" class="relative">
            <button
              class="rounded-full size-[22px]"
              @click="showMenu = !showMenu"
            >
              <ClientOnly>
                <fa :icon="['fas', 'ellipsis']" />
              </ClientOnly>
            </button>
            <div
              v-if="showMenu"
              class="absolute right-0 top-full mt-1 w-52 bg-white rounded-lg shadow-[0_0_2px_rgba(0,0,0,0.12),_0_2px_4px_rgba(0,0,0,0.14)]"
            >
              <ul class="text-base text-[#333333]">
                <li
                  class="px-4 py-2 hover:bg-[#F1F2F6] cursor-pointer flex items-center gap-2"
                  @click="handleRemoveHistory"
                >
                  <SharedIconEditorDelete />
                  <span>Remove from history</span>
                </li>
              </ul>
            </div>
          </div>
          <button
            class="rounded-full size-[22px]"
            @click="showStatus = !showStatus"
          >
            <ClientOnly>
              <fa
                :icon="['fas', 'chevron-down']"
                class="transition-all duration-300 ease-in-out"
                :class="[showStatus ? 'rotate-180' : '']"
              />
            </ClientOnly>
          </button>
        </div>
      </div>
    </div>
    <div
      v-if="showStatus"
      class="p-4 rounded-md shadow-[0_0_2px_rgba(0,0,0,0.12),_0_2px_4px_rgba(0,0,0,0.14)] z-2 bg-white"
    >
      <p class="text-[#333333] text-sm">{{ history.status }}</p>
    </div>
  </div>
</template>

<style scoped></style>
