<script setup lang="ts">
import { useStore } from 'vuex'
import type { LocalImportData } from '~/types/hubEmailsSettings'

const store = useStore()

const localData = computed<LocalImportData | null>(
  () => store.state.outlook.importFileHistory.localData,
)

// Animated counter state
const animateNum = ref(0)
const currentFileName = computed(() => {
  const files = localData.value?.files || []
  return files[animateNum.value]?.name || 'file.eml'
})

onMounted(async () => {
  await nextTick()
  if (!localData.value) return

  const interval = setInterval(() => {
    if (animateNum.value < (localData.value?.filesCount ?? 0) - 1) {
      animateNum.value += 1
    } else {
      clearInterval(interval)
    }
  }, 800)
})
</script>

<template>
  <div
    class="relative px-6 py-4 rounded-md flex items-center gap-5 shadow-[0_0_2px_rgba(0,0,0,0.12),_0_2px_4px_rgba(0,0,0,0.14)] bg-white"
  >
    <SharedIconHubMicrosoftImportFile class="size-10 text-[#333333]" />
    <div class="space-y-1">
      <p class="text-[#333333] text-sm">
        ({{ animateNum + 1 }}/{{ localData?.filesCount }}) importing
        {{ currentFileName }}
      </p>
      <div class="flex items-center gap-2 text-[#333333]">
        <SharedIconHubMicrosoftImportFileDefaultFolder
          class="size-6 text-[#333333]"
        />
        <span>{{ localData?.folder }} to</span>
        <SharedIconHubMicrosoftImportFileDefaultFolder
          class="size-6 text-[#333333]"
        />
        <span>{{ localData?.destination }}</span>
      </div>
    </div>
    <div class="loader absolute left-0 bottom-0 h-px w-full bg-[#4A71D4]"></div>
  </div>
</template>

<style scoped>
.loader {
  animation: loader-grow 2s ease-in-out infinite;
  transform-origin: left;
}

@keyframes loader-grow {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleX(1);
  }
}
</style>
