<script setup lang="ts">
import ArchiveIcon from '~/components/shared/icon/hub/microsoft/import-file/Archive.vue'
import DefaultFolderIcon from '~/components/shared/icon/hub/microsoft/import-file/DefaultFolder.vue'
import DeletedItemsIcon from '~/components/shared/icon/hub/microsoft/import-file/DeletedItems.vue'
import DraftsIcon from '~/components/shared/icon/hub/microsoft/import-file/Drafts.vue'
import InboxIcon from '~/components/shared/icon/hub/microsoft/import-file/Inbox.vue'
import JunkEmailIcon from '~/components/shared/icon/hub/microsoft/import-file/JunkEmail.vue'
import SentItemsIcon from '~/components/shared/icon/hub/microsoft/import-file/SentItems.vue'
import type { OutlookFolder } from '~/types/hubEmailsSettings'

interface IconMap {
  [key: string]: string | Component
}

const iconsMap: IconMap = {
  inbox: InboxIcon,
  'junk-email': JunkEmailIcon,
  drafts: DraftsIcon,
  'sent-items': SentItemsIcon,
  'deleted-items': DeletedItemsIcon,
  archive: ArchiveIcon,
  default: DefaultFolderIcon,
}

interface DropsDownProps {
  showArrow?: boolean
  placeHolder?: string
  zIndex?: number
}
const props = withDefaults(defineProps<DropsDownProps>(), {
  showArrow: true,
  placeHolder: 'Choose folder',
  zIndex: 10,
})
const emit = defineEmits(['change', 'activate'])
const searchTerm = ref('')
const options = ref<OutlookFolder[]>([
  {
    id: '1',
    title: 'Inbox',
    iconKey: 'inbox',
  },
  {
    id: '2',
    title: 'Junk Email',
    iconKey: 'junk-email',
  },
  {
    id: '3',
    title: 'Drafts',
    iconKey: 'drafts',
  },
  {
    id: '4',
    title: 'Sent Items',
    iconKey: 'sent-items',
  },
  {
    id: '5',
    title: 'Deleted Items',
    iconKey: 'deleted-items',
  },
  {
    id: '6',
    title: 'Archive',
    iconKey: 'archive',
  },
  {
    id: '7',
    title: 'Conversation History',
    iconKey: 'default',
  },
  {
    id: '8',
    title: 'Product',
    iconKey: 'default',
  },
])

const searchableOptions = computed(() => {
  return options.value.filter((option) =>
    option.title.toLowerCase().includes(searchTerm.value.toLowerCase()),
  )
})

const selectedOption = defineModel<OutlookFolder | null>({ default: null })
const dropdownRef = ref<HTMLDivElement | null>(null)
const searchInput = ref<HTMLInputElement | null>(null)
const createFolderInput = ref<HTMLInputElement | null>(null)

const createFolderInputText = ref('')
const showCreateFolderInput = ref(false)

const isOpen = ref(false)
const toggleDropdown = () => {
  isOpen.value = !isOpen.value

  if (isOpen.value) {
    emit('activate')
    nextTick(() => {
      searchInput.value?.focus()
    })
  }
}
const handleSelectOption = (option: OutlookFolder) => {
  selectedOption.value = option
  isOpen.value = false
  emit('change', selectedOption.value)
}

const handleShowCreateFolderInput = () => {
  showCreateFolderInput.value = true
  nextTick(() => {
    createFolderInput.value?.focus()
  })
}

const handleCreateFolder = () => {
  if (createFolderInputText.value.trim() === '') return
  const newOption: OutlookFolder = {
    id: new Date().getTime().toString(),
    title: createFolderInputText.value,
    iconKey: 'default',
  }
  options.value.push(newOption)
  handleSelectOption(newOption)
  createFolderInputText.value = ''
  showCreateFolderInput.value = false
}

const handleClickOutside = (event: Event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div ref="dropdownRef" class="w-full relative">
    <button
      class="w-full rounded-full h-10 mt-4 text-base font-semibold leading-[21px] text-[#333333] flex items-center justify-center gap-2"
      :class="isOpen ? 'bg-[#E3EFFF]' : 'bg-[#F1F2F6]'"
      @click="toggleDropdown"
    >
      <div class="flex space-x-2 items-center">
        <component
          v-if="selectedOption"
          :is="iconsMap[selectedOption.iconKey]"
          class="w-5 text-[#525252] icon"
        />
        <span>{{ selectedOption ? selectedOption.title : placeHolder }}</span>
      </div>
      <ClientOnly v-if="showArrow">
        <fa
          class="text-[#333333] w-3 transition-all duration-300 ease-in-out"
          :class="[isOpen ? 'rotate-180' : '']"
          :icon="['fas', 'chevron-down']"
        />
      </ClientOnly>
      <ClientOnly v-else-if="!showArrow">
        <fa
          class="text-xl text-[#4A71D4] transition-all duration-300 ease-in-out"
          :class="[isOpen ? 'rotate-180' : '']"
          :icon="['fas', 'caret-down']"
        />
      </ClientOnly>
    </button>

    <div
      v-if="isOpen"
      class="absolute z-10 w-full shadow-xl rounded-lg overflow-hidden"
      :style="{ zIndex: zIndex }"
    >
      <div
        class="w-full bg-white rounded-lg max-h-[300px] overflow-y-auto custom-scroll pb-1"
      >
        <div class="p-1 relative">
          <ClientOnly>
            <fa
              class="text-[#707070] absolute top-1/2 -translate-y-1/2 left-6"
              :icon="['fas', 'magnifying-glass']"
            />
          </ClientOnly>
          <input
            ref="searchInput"
            v-model="searchTerm"
            type="text"
            class="w-full pl-11 py-1.5 rounded-sm text-base leading-[21px] text-[#333333] outline-none border-none focus:ring-1"
            placeholder="Search for a folder"
          />
        </div>
        <div
          v-for="option in searchableOptions"
          :key="option.id"
          class="flex items-center px-6 py-[7px] hover:bg-[#4A71D4] text-[#525252] hover:text-white cursor-pointer gap-2 group"
          @click="handleSelectOption(option)"
        >
          <component
            :is="iconsMap[option.iconKey]"
            class="w-5 text-[#707070] group-hover:text-white"
          />
          <span>{{ option.title }}</span>
        </div>
        <div class="h-0.5 bg-[#F1F2F6]" />
        <div v-if="showCreateFolderInput" class="px-6 flex items-center gap-2">
          <DefaultFolderIcon class="w-5 min-w-5 text-[#707070]" />
          <input
            ref="createFolderInput"
            v-model="createFolderInputText"
            type="text"
            class="w-full py-2 rounded-sm text-base leading-[21px] text-[#333333] outline-none border-none"
            placeholder="New folder name"
          />
          <button
            class="px-4 py-1.5 rounded-full text-base leading-[21px] text-[#333333]"
            @click="handleCreateFolder"
          >
            Save
          </button>
        </div>
        <button
          v-else
          class="w-full pl-11 py-2 text-base leading-[21px] text-[#525252] text-start hover:bg-[#4A71D4] hover:text-white"
          @click.stop="handleShowCreateFolderInput"
        >
          Create a folder
        </button>
        <div class="h-0.5 bg-[#F1F2F6]" />
        <button
          class="w-full pl-11 py-2 text-base leading-[21px] text-[#525252] text-start hover:bg-[#4A71D4] hover:text-white"
        >
          Move to a different folder...
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.folder-wrapper > button {
  margin-top: 0;
  justify-content: space-between;
  color: #707070;
  font-weight: normal;
  height: 35px;
  padding-inline: 16px;
}
.folder-wrapper > button > div > .icon {
  color: #707070;
}
</style>
