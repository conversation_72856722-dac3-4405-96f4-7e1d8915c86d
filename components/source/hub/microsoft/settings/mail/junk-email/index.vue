<script setup lang="ts">
import { useStore } from 'vuex'
import type {
  EmittedSenderList,
  JunkMail,
  SenderList,
  SenderOption,
} from '~/types/hubEmailsSettings'
import CommonSenderComp from './CommonSenderComp.vue'
import JunkMailRadioGroup from './JunkMailRadioGroup.vue'

interface JunkMailOption {
  label: string
  value: string
  description: string
}

const emit = defineEmits<{
  changeJunkMail: [junkMail: JunkMail]
  changeSenderList: [senderList: EmittedSenderList]
}>()

const store = useStore()

const junkMailOptions = ref<JunkMailOption[]>([
  {
    label: 'Standard',
    value: 'standard',
    description:
      'Mail identified as possible junk will be automatically moved to the Junk Email folder',
  },
  {
    label: 'Strict',
    value: 'strict',
    description: 'Individually decide who is allowed to send you mail',
  },
])

const junkMail = ref<JunkMail>({
  incomingMailHandleType: 'standard',
  blockAttachments: false,
  trustEmailFromContacts: false,
})

const senderList = ref<SenderList>({
  safeSenders: [],
  blockedSenders: [],
  safeMailingLists: [],
})
const senderOption: SenderOption[] = [
  {
    label: 'Safe senders and domains',
    value: 'safe_sender',
  },
  {
    label: 'Blocked senders and domains',
    value: 'blocked_sender',
  },
  {
    label: 'Safe mailing lists',
    value: 'safe_mailing_list',
  },
]
const selectedSender = ref<SenderOption['value']>('safe_sender')

watch(
  junkMail,
  () => {
    emit('changeJunkMail', junkMail.value)
  },
  { deep: true },
)
watch(
  senderList,
  () => {
    emit('changeSenderList', {
      senderList: senderList.value,
      selectedSender: selectedSender.value,
    })
  },
  { deep: true },
)
const initJunkMail = () => {
  const savedJunkMail = store.state.emails.junkMail as JunkMail
  const savedSenderList = store.state.emails.senderList as SenderList
  junkMail.value = JSON.parse(JSON.stringify(savedJunkMail))
  senderList.value = JSON.parse(JSON.stringify(savedSenderList))
}

defineExpose({
  initJunkMail,
})
onMounted(async () => {
  await nextTick()
  initJunkMail()
})
</script>

<template>
  <div class="w-full h-full overflow-hidden flex flex-col">
    <div
      class="px-6 py-4 text-xl leading-[26px] font-semibold text-[#333333] border-b border-[#F1F2F6]"
    >
      <p>Junk email</p>
    </div>
    <div class="px-6 mt-3.5 pb-4 flex-grow overflow-y-auto">
      <p class="text-base leading-[21px] text-[#333333] font-semibold">
        Incoming mail handling
      </p>
      <p class="text-base leading-[21px] text-[#707070] pt-3.5">
        Select how Outlook should handle new messages that you receive.
      </p>

      <JunkMailRadioGroup
        v-model="junkMail.incomingMailHandleType"
        :options="junkMailOptions"
      />
      <div
        class="pt-[15px] pb-4 border-t border-[#F1F2F6] flex flex-col space-y-3.5"
      >
        <p class="text-base leading-[21px] text-[#333333] font-semibold">
          Security options
        </p>
        <InputsCheckBoxWithLabel
          v-model="junkMail.blockAttachments"
          checkColor="#4A71D4"
          label="Block attachments, pictures, and links from anyone not in my Safe senders and domains list"
          labelClass="text-base text-[#333333] leading-[21px]"
          class="w-min whitespace-nowrap"
        />
        <InputsCheckBoxWithLabel
          v-model="junkMail.trustEmailFromContacts"
          checkColor="#4A71D4"
          label="Trust email from my contacts"
          labelClass="text-base text-[#333333] leading-[21px]"
          class="w-min whitespace-nowrap"
        />
      </div>
      <div class="py-4 border-t border-[#F1F2F6]">
        <p class="text-base leading-[21px] text-[#333333] font-semibold">
          Senders
        </p>
        <div class="flex items-center space-x-2 mt-4">
          <button
            v-for="option in senderOption"
            :key="option.value"
            :class="{
              'bg-[#4A71D4] text-white font-semibold':
                selectedSender === option.value,
              'bg-transparent text-[#525252]': selectedSender !== option.value,
            }"
            @click="selectedSender = option.value"
            class="text-base leading-[21px] px-4 py-[7px] rounded-full"
          >
            {{ option.label }}
          </button>
        </div>
        <Transition name="page" mode="out-in">
          <CommonSenderComp
            v-if="selectedSender === 'safe_sender'"
            v-model="senderList.safeSenders"
            addButtonText="Add safe sender"
            placeholderText="Add safe sender"
            descriptionText="Don't move email from these senders to my Junk Email folder."
          />
          <CommonSenderComp
            v-else-if="selectedSender === 'blocked_sender'"
            v-model="senderList.blockedSenders"
            addButtonText="Add block sender"
            placeholderText="Add blocked sender"
            descriptionText="Blocking a sender or domain will stop their email from coming to your mailbox."
          />
          <CommonSenderComp
            v-else-if="selectedSender === 'safe_mailing_list'"
            v-model="senderList.safeMailingLists"
            addButtonText="Add safe mailing list"
            placeholderText="Add safe mailing list"
            descriptionText="Messages with mailing lists often have an address other than your email address on the To line. If you want to receive email from a mailing list, add the address of the list you trust to the box below."
          />
        </Transition>
      </div>
    </div>
  </div>
</template>
