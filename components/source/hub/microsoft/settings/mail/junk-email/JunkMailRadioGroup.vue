<!-- components/JunkMailRadioGroup.vue -->
<script setup lang="ts">
interface JunkMailOption {
  label: string
  value: string
  description: string
}

defineProps<{
  options: JunkMailOption[]
}>()

const modelValue = defineModel<string>()
</script>

<template>
  <div class="py-4 space-y-2">
    <div
      v-for="option in options"
      :key="option.value"
      class="border border-[#E3E3E3] rounded"
    >
      <div class="px-6 pt-3.5 pb-4 flex items-start space-x-4">
        <div>
          <input
            type="radio"
            :id="option.value"
            :value="option.value"
            v-model="modelValue"
            name="incomingMailHandleType"
            class="peer hidden z-3"
          />
          <label
            :for="option.value"
            class="w-4 h-4 mt-1 border-2 rounded-full flex items-center justify-center cursor-pointer"
            :class="{
              'border-[#4A71D4]': modelValue === option.value,
              'border-[#525252]': modelValue !== option.value,
            }"
          >
            <div
              class="w-2 h-2 rounded-full"
              :class="{
                'bg-[#4A71D4]': modelValue === option.value,
                'bg-transparent': modelValue !== option.value,
              }"
            />
          </label>
        </div>
        <label
          :for="option.value"
          class="cursor-pointer flex flex-col space-y-1.5 text-base leading-[21px]"
        >
          <p class="text-[#333333] font-semibold">{{ option.label }}</p>
          <p class="text-[#707070]">{{ option.description }}</p>
        </label>
      </div>
      <div
        v-if="modelValue === 'strict' && option.value === 'strict'"
        class="flex items-center space-x-4 px-6 py-3.5 text-[#525252] border-t border-[#E3E3E3] bg-[#F8F8F8]"
      >
        <SharedIconInfoFillRounded
          class="rounded-full text-[#707070]"
          stroke="none"
        />
        <p class="text-[#707070] w-full max-w-[930px]">
          This will use your Safe Senders and Domains list to decide who is
          allowed to send mail to your Inbox. All other messages will be
          delivered to Junk Email.
        </p>
      </div>
    </div>
  </div>
</template>
