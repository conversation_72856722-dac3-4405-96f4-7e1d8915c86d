<script setup lang="ts">
import type { SenderEmail } from '~/types/hubEmailsSettings'
import SenderInput from './SenderInput.vue'

const emit = defineEmits<{
  (e: 'delete', item: SenderEmail): void
  (e: 'edit', item: SenderEmail): void
}>()

const props = defineProps<{
  item: SenderEmail
  list: SenderEmail[]
  placeholderText: string
}>()

const { $toast } = useNuxtApp()

const showEditInput = ref(false)
const updatedEmail = ref(props.item.email)

const handleDelete = () => {
  emit('delete', props.item)
}

const handleShowEditInput = () => {
  showEditInput.value = true
}

const handleEditOk = () => {
  if (props.list.some((item) => item.email === updatedEmail.value)) {
    $toast('clear')
    $toast('error', {
      message: 'Email already exists',
      className: 'toasted-bg-alert',
    })
    console.log('Email already exists')
    return
  }
  const updatedEmailItem = {
    ...props.item,
    email: updatedEmail.value,
  }
  emit('edit', updatedEmailItem)
  showEditInput.value = false
}
</script>

<template>
  <li class="">
    <div
      v-if="!showEditInput"
      class="h-[51px] rounded flex items-center justify-between px-4 py-[15px] hover:bg-[#F1F2F6] group border-b-2 border-[#F1F2F6]"
    >
      <p class="text-[#333333] text-base leading-[21px]">
        {{ item.email }}
      </p>
      <div class="group-hover:flex items-center space-x-1 hidden">
        <button
          class="rounded-full p-2 hover:bg-[#E3EFFF]"
          @click="handleShowEditInput"
        >
          <SharedIconHubMicrosoftEditIcon color="#333333" />
        </button>
        <button
          class="rounded-full p-2 hover:bg-[#E3EFFF]"
          @click="handleDelete"
        >
          <SharedIconHubEmailsDeleteIcon color="#333333" />
        </button>
      </div>
    </div>
    <div v-else class="h-[51px] rounded w-full flex items-center">
      <SenderInput
        v-model="updatedEmail"
        :placeholder-text="placeholderText"
        @cancel="showEditInput = false"
        @ok="handleEditOk"
      />
    </div>
  </li>
</template>

<style scoped></style>
