<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core'
import { email, required } from '@vuelidate/validators'

defineProps<{
  placeholderText: string
}>()

const emit = defineEmits<{
  (e: 'cancel'): void
  (e: 'ok'): void
}>()

const model = defineModel<string>({ default: '' })
const inputRef = ref<HTMLInputElement | null>(null)

const form = ref({
  email: model,
})

const rules = {
  email: {
    required,
    email,
  },
}

const v$ = useVuelidate(rules, form)

onMounted(async () => {
  await nextTick()
  inputRef.value?.focus()
})

const onOk = async () => {
  const isValid = await v$.value.$validate()
  if (!isValid) return
  emit('ok')
}

const onCancel = () => {
  emit('cancel')
}
</script>

<template>
  <div class="w-full">
    <div class="flex items-center space-x-2">
      <input
        ref="inputRef"
        type="text"
        v-model="model"
        :placeholder="placeholderText"
        class="w-full h-[35px] rounded-full bg-[#E3EFFF] outline-none border-none px-4 text-base text-[#707070]"
      />

      <button
        class="border border-[#C2C2C2] text-[#525252] rounded-full text-base leading-[21px] font-semibold px-6 py-1.5"
        @click="onCancel"
      >
        Cancel
      </button>
      <button
        class="border border-[#4A71D4] bg-[#4A71D4] text-white rounded-full text-base leading-[21px] font-semibold px-6 py-1.5"
        @click="onOk"
      >
        Ok
      </button>
    </div>
    <template v-if="v$.email.$error">
      <p
        v-if="v$.email.required.$invalid"
        class="text-red-500 text-xs email-require pl-2"
      >
        Email is Required
      </p>
      <p
        v-else-if="v$.email.email.$invalid"
        class="text-red-500 text-xs email-invalid pl-2"
      >
        Email is Invalid
      </p>
    </template>
  </div>
</template>

<style scoped></style>
