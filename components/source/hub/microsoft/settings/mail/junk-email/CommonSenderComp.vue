<script setup lang="ts">
import type { SenderEmail } from '~/types/hubEmailsSettings'
import SenderInput from './SenderInput.vue'
import SenderListItem from './SenderListItem.vue'
const { $toast } = useNuxtApp()

defineProps<{
  addButtonText: string
  descriptionText: string
  placeholderText: string
}>()

const showAddInput = ref(false)
const list = defineModel<SenderEmail[]>({ default: [] })
const senderInputText = ref<string>('')

const handleInputOk = () => {
  if (list.value.some((item) => item.email === senderInputText.value)) {
    $toast('clear')
    $toast('error', {
      message: 'Email already exists',
      className: 'toasted-bg-alert',
    })
    return
  }
  const item = {
    id: crypto.randomUUID(),
    email: senderInputText.value,
  }
  list.value.unshift(item)
  senderInputText.value = ''
  showAddInput.value = false
}
const handleCancel = () => {
  senderInputText.value = ''
  showAddInput.value = false
}
const handleDelete = (item: SenderEmail) => {
  list.value = list.value.filter((i) => i.id !== item.id)
}
const handleEdit = (item: SenderEmail) => {
  list.value = list.value.map((i) => {
    if (i.id === item.id) {
      i.email = item.email
    }
    return i
  })
}
const searchText = ref<string>('')

const filteredList = computed(() => {
  if (!searchText.value) {
    return list.value
  }

  return [...list.value]
    .filter((item) =>
      item.email.toLowerCase().includes(searchText.value.toLowerCase()),
    )
    .sort((a, b) => a.email.localeCompare(b.email))
})
</script>

<template>
  <div class="mt-4">
    <div class="flex items-center justify-between mb-4">
      <button
        :disabled="showAddInput"
        class="border border-[#707070] rounded-full text-sm leading-[19px] font-semibold px-6 py-2 h-9 flex items-center space-x-[11px]"
        :class="[showAddInput ? 'opacity-60' : '']"
        @click="showAddInput = true"
      >
        <fa class="text-[#525252] text-base" :icon="['fas', 'plus']" />
        <span class="text-[#525252]">{{ addButtonText }}</span>
      </button>
      <div class="w-[280px] relative">
        <fa
          class="absolute top-1/2 -translate-y-1/2 left-6 text-[#707070]"
          :icon="['fas', 'search']"
        />
        <input
          type="text"
          v-model="searchText"
          placeholder="Search"
          class="w-full h-9 rounded-full bg-[#F1F2F6] outline-none border-none pl-12 pr-6 text-base text-[#707070] focus:bg-[#E3EFFF]"
        />
        <fa
          v-if="searchText"
          role="button"
          :icon="['fas', 'times']"
          class="absolute top-1/2 -translate-y-1/2 right-4 text-[#707070] text-xs rounded-full cursor-pointer"
          @click="searchText = ''"
        />
      </div>
    </div>
    <div
      class="text-[#525252] text-sm leading-[19px] px-4 py-[11px] bg-[#F8F8F8] rounded"
    >
      <p class="w-full max-w-[940px]">{{ descriptionText }}</p>
    </div>
    <p
      v-if="filteredList.length === 0 && !showAddInput"
      class="text-center pt-[22px] text-[#525252] text-sm leading-[19px]"
    >
      This list is empty.
    </p>
    <SenderInput
      v-if="showAddInput"
      v-model="senderInputText"
      :placeholder-text="placeholderText"
      class="mt-4"
      @cancel="handleCancel"
      @ok="handleInputOk"
    />

    <ul v-if="filteredList.length > 0" class="mt-4">
      <SenderListItem
        v-for="item in filteredList"
        :key="item.id"
        :item="item"
        :list="list"
        :placeholder-text="placeholderText"
        @delete="handleDelete"
        @edit="handleEdit"
      />
    </ul>
  </div>
</template>

<style scoped></style>
