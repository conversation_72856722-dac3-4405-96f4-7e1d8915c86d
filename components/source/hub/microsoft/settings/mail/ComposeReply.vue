<script setup lang="ts">
const emit = defineEmits(['set-message-format'])
const message_formatRef = ref<InstanceType<
  typeof SourceHubMicrosoftSettingsMailComposereplyMessageFormat
> | null>(null)
const copypaste_formatRef = ref<InstanceType<
  typeof SourceHubMicrosoftSettingsMailComposereplyCutCopyPaste
> | null>(null)
// Expose setInitialValue from the child
const setInitialValue = () => {
  console.log('Setting initial values for message format2')
  message_formatRef.value?.setInitialValue()
  copypaste_formatRef.value?.setInitialValue()
}

defineExpose({
  setInitialValue,
})
</script>

<template>
  <div class="w-full h-full overflow-hidden flex flex-col">
    <div
      class="px-6 py-4 text-xl leading-[26px] font-semibold text-[#333333] border-b border-[#F1F2F6]"
    >
      <p>Compose and reply</p>
    </div>
    <div class="px-6 mt-3.5 flex-grow overflow-y-auto">
      <SourceHubMicrosoftSettingsMailComposereplyMessageFormat
        ref="message_formatRef"
        @set-message-format="($event) => emit('set-message-format', $event)"
      />
      <SourceHubMicrosoftSettingsMailComposereplyCutCopyPaste
        ref="copypaste_formatRef"
        @set-copy-paste-format="($event) => emit('set-copy-paste-format', $event)"
      />
      <SourceHubMicrosoftSettingsMailComposereplyReplyOrReplyAll />
      <SourceHubMicrosoftSettingsMailComposereplyLinkPreview />
      <SourceHubMicrosoftSettingsMailComposereplyUndoSend />
      <SourceHubMicrosoftSettingsMailComposereplyCommasRecipients />
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
