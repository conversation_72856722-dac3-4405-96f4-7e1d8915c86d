<script setup lang="ts">
const recipientsChecked = ref<boolean>(true)
</script>

<template>
  <div class="mt-4 pb-4">
    <div class="flex flex-col space-y-4">
      <p class="text-[#333333] font-semibold">Commas to separate recipients</p>
      <p class="text-[#707070]">
        When you add recipients to a message, you can choose to use semicolons
        as well as commas as separators.
      </p>
    </div>
    <div class="mt-3">
      <label
        class="w-full relative flex space-x-2 items-start cursor-pointer"
        for="recipients"
      >
        <div class="w-4 h-4 relative mt-0.5">
          <input
            ref="recipients"
            id="recipients"
            type="checkbox"
            v-model="recipientsChecked"
            :checked="recipientsChecked ? true : false"
            class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
            :class="recipientsChecked ? '' : 'border border-[#333333]'"
          />
          <ClientOnly>
            <fa
              class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
              :icon="['fas', 'check']"
            />
          </ClientOnly>
        </div>
        <div class="inline-block text-[#333333]">
          <p>Also use commas as recipient separators.</p>
        </div>
      </label>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.toggle-check-1 {
  &:checked {
    @apply bg-[#4A71D4];
  }
  &:checked + .check-1 {
    @apply opacity-100;
    color: white;
  }
}
</style>
