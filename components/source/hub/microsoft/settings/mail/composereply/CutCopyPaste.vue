<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const emit = defineEmits(['set-copy-paste-format'])
const finalCopyPasteFormat = computed(
  () => store.state.emails.finalCopyPasteFormat,
)
interface PastingFromEmailsOptions {
  id: number
  text: string
}
const pastingFromEmailsOptions = ref<PastingFromEmailsOptions[]>([
  {
    id: 1,
    text: 'Keep source formatting',
  },
  {
    id: 2,
    text: 'Merge formatting',
  },
  {
    id: 3,
    text: 'Keep text only',
  },
])
const selectedPastingFromEmailsOption = ref({
  id: 1,
  text: 'Keep source formatting',
})
const pastingFromEmailsDropdown = ref<boolean>(false)

interface PastingFromAppsOptions {
  id: number
  text: string
}
const pastingFromAppsOptions = ref<PastingFromAppsOptions[]>([
  {
    id: 1,
    text: 'Keep source formatting',
  },
  {
    id: 2,
    text: 'Merge formatting',
  },
  {
    id: 3,
    text: 'Keep text only',
  },
])
const selectedPastingFromAppsOption = ref({
  id: 2,
  text: 'Merge formatting',
})

const copyPasteFormat = ref({
  selectedPastingFromEmailsOption: 'Keep source formatting',
  selectedPastingFromAppsOption: 'Merge formatting',
})

watch(
  () => ({
    selectedPastingFromEmailsOption: selectedPastingFromEmailsOption.value,
    selectedPastingFromAppsOption: selectedPastingFromAppsOption.value,
  }),
  (newValue) => {
    console.log('Message format updated:first', newValue)
    copyPasteFormat.value.selectedPastingFromEmailsOption =
      newValue.selectedPastingFromEmailsOption.text
    copyPasteFormat.value.selectedPastingFromAppsOption =
      newValue.selectedPastingFromAppsOption.text
    emit('set-copy-paste-format', copyPasteFormat.value)
    console.log('Message format updated:last', newValue, copyPasteFormat.value)
  },
  { deep: true },
)
const pastingFromAppsDropdown = ref<boolean>(false)

const setInitialValue = () => {
  copyPasteFormat.value = JSON.parse(JSON.stringify(finalCopyPasteFormat.value))

  console.log(
    'setInitialValue',
    copyPasteFormat.value,
    finalCopyPasteFormat.value,
  )
  if (copyPasteFormat.value) {
    selectedPastingFromEmailsOption.value =
      pastingFromEmailsOptions.value.find(
        (option) =>
          option.text === copyPasteFormat.value.selectedPastingFromEmailsOption,
      ) || pastingFromEmailsOptions.value[0]
    console.log(
      selectedPastingFromEmailsOption.value,
      selectedPastingFromAppsOption.value,
      'initial',
    )
    selectedPastingFromAppsOption.value =
      pastingFromAppsOptions.value.find(
        (option) =>
          option.text === copyPasteFormat.value.selectedPastingFromAppsOption,
      ) || pastingFromAppsOptions.value[1]

    console.log(
      selectedPastingFromEmailsOption.value,
      selectedPastingFromAppsOption.value,
      'initial',
    )
  }
}
onMounted(() => {
  console.log('Mounted CopyPaste component')
  setInitialValue()
})
defineExpose({
  setInitialValue,
})
</script>

<template>
  <div class="border-b border-[#F1F2F6] mt-4 pb-4">
    <div class="flex flex-col space-y-3.5">
      <p class="text-[#333333] font-semibold">Cut, copy, and paste</p>
      <p class="text-[#707070]">
        Choose how content from different apps is pasted when writing an email.
      </p>
    </div>
    <div class="mt-4 flex flex-col space-y-4">
      <div class="grid grid-cols-[244px_1fr] gap-x-4 items-center">
        <p class="text-[#333333]">Pasting from emails and calendar</p>
        <BaseDropsDown
          class="rounded-full language-dropdown"
          v-model="selectedPastingFromEmailsOption"
          :options="pastingFromEmailsOptions"
          labelKey="text"
          placeholder="Keep source formatting"
          :menuWidth="286"
          :menuHeight="35"
          :dropdownWidth="286"
          :dropdownMaxHeight="136"
          :menuBgColor="!pastingFromEmailsDropdown ? '#F1F2F6' : '#E3EFFF'"
          menuTextColor="#333333"
          dropsdownTextColor="#333333"
          scrollbarTrackColor="#a1cdff50"
          scrollbarThumbColor="#a1cdff"
          hoverColor="#4A71D4"
          hoverTextColor="#FFFFFF"
          :showSelectedIcon="true"
          @open="($event) => (pastingFromEmailsDropdown = $event)"
        />
      </div>
      <div class="grid grid-cols-[244px_1fr] gap-x-4 items-center">
        <p class="text-[#333333]">Pasting from other apps</p>
        <BaseDropsDown
          class="rounded-full language-dropdown"
          v-model="selectedPastingFromAppsOption"
          :options="pastingFromAppsOptions"
          labelKey="text"
          placeholder="Keep source formatting"
          :menuWidth="286"
          :menuHeight="35"
          :dropdownWidth="286"
          :dropdownMaxHeight="136"
          :menuBgColor="!pastingFromAppsDropdown ? '#F1F2F6' : '#E3EFFF'"
          menuTextColor="#333333"
          dropsdownTextColor="#333333"
          scrollbarTrackColor="#a1cdff50"
          scrollbarThumbColor="#a1cdff"
          hoverColor="#4A71D4"
          hoverTextColor="#FFFFFF"
          :showSelectedIcon="true"
          @open="($event) => (pastingFromAppsDropdown = $event)"
        />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
