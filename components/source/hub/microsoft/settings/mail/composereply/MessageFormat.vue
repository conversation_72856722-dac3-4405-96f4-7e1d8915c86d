<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const emit = defineEmits(['set-message-format'])
const closeAllMenus = computed(() => store.state.emails.closeAllMenus)
const finalMessageFormat = computed(() => store.state.emails.finalMessageFormat)
watch(closeAllMenus, (data) => {
  if (!data) {
    showColorPicker.value = false
  }
})
interface MessageOptions {
  id: number
  text: string
  checked: boolean
}
const selectedMessageOption = ref<string[]>([])
const messageOptions = ref<MessageOptions[]>([
  {
    id: 1,
    text: 'Always show Bcc',
    checked: false,
  },
  {
    id: 2,
    text: 'Always show From',
    checked: false,
  },
])
const setMessageOptions = (option: MessageOptions) => {
  // console.log('setMessageOptions called with option:', option)
  const index = messageOptions.value.findIndex((item) => item.id === option.id)
  // console.log(
  //   'setMessageOptions called with option:',
  //   messageOptions.value[index].checked,
  //   index,
  // )
  if (index !== -1) {
    messageOptions.value[index].checked = option.checked
    if (messageOptions.value[index].checked) {
      selectedMessageOption.value.push(option.text)
    } else {
      const index = selectedMessageOption.value.findIndex(
        (item) => item === option.text,
      )
      // console.log('Removing option:', selectedMessageOption.value)
      selectedMessageOption.value.splice(index, 1)
    }
  }
}
interface FormatOptions {
  id: number
  text: string
}
const formatOptions = ref<FormatOptions[]>([
  {
    id: 1,
    text: 'HTML',
  },
  {
    id: 2,
    text: 'Plain text',
  },
])
const messageFormatDropdown = ref<boolean>(false)
const selectedMessageFormat = ref<FormatOptions>({
  id: 1,
  text: 'HTML',
})
const fontFamilies = [
  { id: 1, label: 'Aptos', value: 'Aptos' },
  { id: 2, label: 'Arial', value: 'Arial' },
  { id: 3, label: 'Sans Serif', value: 'sans-serif' },
  { id: 4, label: 'Serif', value: 'serif' },
  { id: 5, label: 'Fixed Width', value: 'monospace' },
  { id: 6, label: 'Wide', value: 'Arial Black' },
  { id: 7, label: 'Narrow', value: 'Arial Narrow' },
  { id: 8, label: 'Comic Sans MS', value: 'Comic Sans MS' },
  { id: 9, label: 'Garamond', value: 'Garamond' },
  { id: 10, label: 'Georgia', value: 'Georgia' },
  { id: 11, label: 'Tahoma', value: 'Tahoma' },
  { id: 12, label: 'Trebuchet MS', value: 'Trebuchet MS' },
  { id: 13, label: 'Verdana', value: 'Verdana' },
]
const selectedFontFamily = ref({
  id: 1,
  label: 'Aptos',
  value: 'Aptos',
})
const headingArray = ref([
  { id: 1, label: '8', fontSize: 8, selected: false },
  { id: 2, label: '9', fontSize: 9, selected: false },
  { id: 3, label: '10', fontSize: 10, selected: false },
  { id: 4, label: '11', fontSize: 11, selected: false },
  { id: 5, label: '12', fontSize: 12, selected: true }, // default selected
  { id: 6, label: '14', fontSize: 14, selected: false },
  { id: 7, label: '16', fontSize: 16, selected: false },
  { id: 8, label: '18', fontSize: 18, selected: false },
  { id: 9, label: '20', fontSize: 20, selected: false },
  { id: 10, label: '22', fontSize: 22, selected: false },
  { id: 11, label: '24', fontSize: 24, selected: false },
  { id: 12, label: '26', fontSize: 26, selected: false },
  { id: 13, label: '28', fontSize: 28, selected: false },
  { id: 14, label: '36', fontSize: 36, selected: false },
  { id: 15, label: '48', fontSize: 48, selected: false },
  { id: 16, label: '72', fontSize: 72, selected: false },
])
const selectedHeaderSize = ref({
  id: 1,
  label: '8',
  fontSize: 8,
  selected: false,
})
const showColorPicker = ref(false)
const colorText = ref('#000000')
const applyTextColor = (color: string) => {
  colorText.value = color
  // console.log('Selected color:', color)
  // props.editor?.chain().focus().setColor(color).run()
}
const bold = ref(false)
const italic = ref(false)
const underline = ref(false)
const messageFormat = ref({
  selectedMessageOption: [],
  selectedMessageFormat: 'HTML',
  selectedFontFamily: 'Aptos',
  selectedHeaderSize: 8,
  bold: false,
  italic: false,
  underline: false,
  colorText: '#000000',
})
watch(
  () => ({
    selectedMessageOption: selectedMessageOption.value,
    selectedMessageFormat: selectedMessageFormat.value,
    selectedFontFamily: selectedFontFamily.value,
    selectedHeaderSize: selectedHeaderSize.value,
    bold: bold.value,
    italic: italic.value,
    underline: underline.value,
    colorText: colorText.value,
  }),
  (newValue) => {
    // console.log('Message format updated:', newValue)
    messageFormat.value.selectedMessageOption = newValue.selectedMessageOption
    messageFormat.value.selectedMessageFormat =
      newValue.selectedMessageFormat.text
    messageFormat.value.selectedFontFamily = newValue.selectedFontFamily.value
    messageFormat.value.selectedHeaderSize =
      newValue.selectedHeaderSize.fontSize
    messageFormat.value.bold = newValue.bold
    messageFormat.value.italic = newValue.italic
    messageFormat.value.underline = newValue.underline
    messageFormat.value.colorText = colorText.value
    emit('set-message-format', messageFormat.value)
  },
  { deep: true },
)
const setInitialValue = () => {
  messageFormat.value = JSON.parse(JSON.stringify(finalMessageFormat.value))
  // console.log('Setting initial values for message format3')
  if (messageFormat.value) {
    // Use a shallow clone if you're trying to avoid accidental mutation
    selectedMessageOption.value = [
      ...messageFormat.value.selectedMessageOption,
    ]
    // console.log('Setting', selectedMessageOption.value)

    // Update checked status based on selectedMessageOption
    messageOptions.value.forEach((option) => {
      option.checked = selectedMessageOption.value.includes(option.text)
    })

    selectedMessageFormat.value =
      formatOptions.value.find(
        (option) =>
          option.text === messageFormat.value.selectedMessageFormat,
      ) || formatOptions.value[0]

    selectedFontFamily.value =
      fontFamilies.find(
        (font) => font.value === messageFormat.value.selectedFontFamily,
      ) || fontFamilies[0]

    selectedHeaderSize.value =
      headingArray.value.find(
        (size) => size.fontSize === messageFormat.value.selectedHeaderSize,
      ) || headingArray.value[0]

    bold.value = messageFormat.value.bold
    italic.value = messageFormat.value.italic
    underline.value = messageFormat.value.underline
    colorText.value = messageFormat.value.colorText || '#000000'
  }
}

defineExpose({
  setInitialValue,
})
onMounted(() => {
  // console.log('Mounted MessageFormat component')
  setInitialValue()
})
</script>

<template>
  <div class="border-b border-[#F1F2F6] pb-4">
    <div class="flex flex-col space-y-3.5">
      <p class="text-[#333333] font-semibold">Message format</p>
      <p class="text-[#707070]">
        Choose whether to display the From and Bcc lines when you're composing a
        message.
      </p>
    </div>
    <div class="flex flex-col space-y-3.5 mt-3.5">
      <label
        v-for="messageOption in messageOptions"
        :key="messageOption.id"
        class="w-full relative flex space-x-2 items-start cursor-pointer"
        :for="`message_options_${messageOption.id}`"
      >
        <div class="w-4 h-4 relative mt-0.5">
          <input
            :ref="`message_options_${messageOption.id}`"
            :id="`message_options_${messageOption.id}`"
            type="checkbox"
            v-model="messageOption.checked"
            :checked="messageOption.checked ? true : false"
            class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
            :class="messageOption.checked ? '' : 'border border-[#333333]'"
            @change="setMessageOptions(messageOption)"
          />
          <ClientOnly>
            <fa
              class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
              :icon="['fas', 'check']"
            />
          </ClientOnly>
        </div>
        <div class="inline-block text-[#333333]">
          <p>
            {{ messageOption.text }}
          </p>
        </div>
      </label>
    </div>
    <div class="mt-4 flex space-x-1.5 items-center">
      <p class="text-[#333333]">Compose messages in</p>
      <BaseDropsDown
        class="rounded-full language-dropdown"
        v-model="selectedMessageFormat"
        :options="formatOptions"
        labelKey="text"
        placeholder="HTML"
        :menuWidth="160"
        :menuHeight="35"
        :dropdownWidth="160"
        :dropdownMaxHeight="99"
        :menuBgColor="!messageFormatDropdown ? '#F1F2F6' : '#E3EFFF'"
        menuTextColor="#333333"
        dropsdownTextColor="#333333"
        scrollbarTrackColor="#a1cdff50"
        scrollbarThumbColor="#a1cdff"
        hoverColor="#4A71D4"
        hoverTextColor="#FFFFFF"
        :showSelectedIcon="true"
        @open="($event) => (messageFormatDropdown = $event)"
      />
      <p>format</p>
    </div>
    <div class="mt-6 flex space-x-1 items-center">
      <BaseDropsDown
        class="border border-[#C2C2C2] rounded-full language-dropdown"
        v-model="selectedFontFamily"
        :options="fontFamilies"
        labelKey="label"
        placeholder="Aptos"
        :menuWidth="208"
        :menuHeight="32"
        :dropdownWidth="208"
        :dropdownMaxHeight="200"
        menuBgColor="#FFFFFF"
        menuTextColor="#525252"
        dropsdownTextColor="#525252"
        scrollbarTrackColor="#a1cdff50"
        scrollbarThumbColor="#a1cdff"
        hoverColor="#4A71D4"
        hoverTextColor="#FFFFFF"
        :showSelectedIcon="true"
        :showArrow="true"
        @click.stop="showColorPicker = false"
      />
      <BaseDropsDown
        class="border border-[#C2C2C2] rounded-full language-dropdown"
        v-model="selectedHeaderSize"
        :options="headingArray"
        labelKey="label"
        placeholder="Aptos"
        :menuWidth="72"
        :menuHeight="32"
        :dropdownWidth="82"
        :dropdownMaxHeight="200"
        menuBgColor="#FFFFFF"
        menuTextColor="#525252"
        dropsdownTextColor="#525252"
        scrollbarTrackColor="#a1cdff50"
        scrollbarThumbColor="#a1cdff"
        hoverColor="#4A71D4"
        hoverTextColor="#FFFFFF"
        :showSelectedIcon="true"
        :showArrow="true"
        padding-right="14"
        @click.stop="showColorPicker = false"
      />
      <!-- :disabled="!editor.can().chain().focus().toggleBold().run()"
      :class="{ 'is-active': editor.isActive('bold') }"
      @click="
        editor.chain().focus().toggleBold().run(),
          ((showColorPicker = false),
          (showAlignModal = false),
          (showShiftModal = false),
          (showHeaderModal = false),
          (showFontFamilyMenu = false),
          (showBackgroundColorPicker = false))
      " -->
      <div
        class="px-2.5 py-2 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
        :class="bold ? 'bg-[#F1F2F6]' : ''"
        @click.stop="(showColorPicker = false), (bold = !bold)"
      >
        <SharedIconHubMicrosoftBoldIcon class="w-3 h-4 min-w-3 min-h-4" />
      </div>
      <!-- :disabled="!editor.can().chain().focus().toggleItalic().run()"
      :class="{ 'is-active': editor.isActive('italic') }"
      @click="
        editor.chain().focus().toggleItalic().run(),
          ((showColorPicker = false),
          (showAlignModal = false),
          (showShiftModal = false),
          (showHeaderModal = false),
          (showFontFamilyMenu = false),
          (showBackgroundColorPicker = false))
      " -->
      <div
        class="px-2.5 py-2 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
        :class="italic ? 'bg-[#F1F2F6]' : ''"
        @click.stop="(showColorPicker = false), (italic = !italic)"
      >
        <SharedIconHubMicrosoftItalicIcon class="w-3.5 h-4 min-w-3.5 min-h-4" />
      </div>
      <!-- :disabled="!editor.can().chain().focus().toggleUnderline().run()"
      :class="{ 'is-active': editor.isActive('underline') }"
      @click="
        editor.chain().focus().toggleUnderline().run(),
          ((showColorPicker = false),
          (showAlignModal = false),
          (showShiftModal = false),
          (showHeaderModal = false),
          (showFontFamilyMenu = false),
          (showBackgroundColorPicker = false))
      " -->
      <div
        class="px-2.5 py-2 flex justify-center cursor-pointer rounded-md hover:bg-[#F1F2F6]"
        :class="underline ? 'bg-[#F1F2F6]' : ''"
        @click.stop="(showColorPicker = false), (underline = !underline)"
      >
        <SharedIconHubMicrosoftUnderlineIcon class="w-3 h-4 min-w-3 min-h-4" />
      </div>
      <!-- @click.stop="
          (showColorPicker = !showColorPicker),
            store.commit('emails/SET_CLOSE_ALL_MENUS', true),
            (showAlignModal = false),
            (showShiftModal = false),
            (showBackgroundColorPicker = false),
            (showHeaderModal = false),
            (showFontFamilyMenu = false)
        " -->
      <div
        class="relative px-2 py-2 flex justify-center items-center space-x-2 cursor-pointer rounded-md hover:bg-[#F1F2F6]"
        :class="showColorPicker ? 'bg-[#F1F2F6]' : ''"
        @click.stop="
          (showColorPicker = !showColorPicker),
            store.commit('emails/SET_CLOSE_ALL_MENUS', true)
        "
      >
        <SharedIconHubMicrosoftColorIcon class="w-3.5 h-4 min-w-3.5 min-h-4" />
        <SharedIconHubMicrosoftArrowIcon class="w-2 h-1.5" />
        <div
          v-show="showColorPicker && closeAllMenus"
          class="bg-white z-1 max-w-[220px] !ml-0 min-w-[220px] max-h-[252px] p-4 pt-3 color-box absolute top-[28px] min-[1490px]:left-0 min-[1310px]:-left-[180px] right-0"
          @click.stop=""
        >
          <div class="grid grid-cols-[1fr] gap-3">
            <div class="flex flex-col space-y-3.5">
              <p class="text-[#525252]">Text color</p>
              <SourceColorPicker
                :applyColor="applyTextColor"
                title="Text color"
                :selectedColorCode="colorText"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="border-l border-[#F1F2F6] mt-3.5 pl-4 flex flex-col space-y-3.5 text-[#333333]"
      :style="{
        fontFamily: selectedFontFamily.value,
        fontSize: selectedHeaderSize.fontSize + 'px',
        fontWeight: bold ? 'bold' : 'normal',
        fontStyle: italic ? 'italic' : 'normal',
        textDecoration: underline ? 'underline' : 'none',
        color: colorText,
      }"
    >
      <p>Messages you write will look like this by default.</p>
      <p>
        You can also change the format of your messages in the new message
        window.
      </p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.toggle-check-1 {
  &:checked {
    @apply bg-[#4A71D4];
  }
  &:checked + .check-1 {
    @apply opacity-100;
    color: white;
  }
}
</style>
