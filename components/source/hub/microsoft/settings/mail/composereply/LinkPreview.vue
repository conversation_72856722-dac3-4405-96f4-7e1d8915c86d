<script setup lang="ts">
const previewLink = ref<boolean>(true)
</script>

<template>
  <div class="border-b border-[#F1F2F6] mt-4 pb-4">
    <div class="flex flex-col space-y-4">
      <p class="text-[#333333] font-semibold">Link preview</p>
      <p class="text-[#707070]">
        When you open a message that contains a hyperlink or add a link to a
        message, Outlook inserts a preview of the website. If you don't want to
        see previews, clear the check box below.
        <span class="text-[#3964D0]">Learn more</span>
      </p>
    </div>
    <div class="mt-3">
      <label
        class="w-full relative flex space-x-2 items-start cursor-pointer"
        for="preview_link"
      >
        <div class="w-4 h-4 relative mt-0.5">
          <input
            ref="preview_link"
            id="preview_link"
            type="checkbox"
            v-model="previewLink"
            :checked="previewLink ? true : false"
            class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
            :class="previewLink ? '' : 'border border-[#333333]'"
          />
          <ClientOnly>
            <fa
              class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
              :icon="['fas', 'check']"
            />
          </ClientOnly>
        </div>
        <div class="inline-block text-[#333333]">
          <p>Preview links in email</p>
        </div>
      </label>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.toggle-check-1 {
  &:checked {
    @apply bg-[#4A71D4];
  }
  &:checked + .check-1 {
    @apply opacity-100;
    color: white;
  }
}
</style>
