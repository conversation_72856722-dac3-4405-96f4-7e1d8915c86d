<script setup lang="ts">
interface ReplyOptions {
  label: string
  value: string
  description: string
}
const selectedReplyBehavior = ref<string>('replyall')
const replyOptions = ref<ReplyOptions[]>([
  {
    label: 'Reply',
    value: 'reply',
    description: '',
  },
  {
    label: 'Reply all',
    value: 'replyall',
    description: '',
  },
])
</script>

<template>
  <div class="border-b border-[#F1F2F6] mt-4 pb-4">
    <div class="flex flex-col space-y-3.5">
      <p class="text-[#333333] font-semibold">Reply or Reply all</p>
      <p class="text-[#707070]">
        Choose your default response when you reply from the reading pane.
      </p>
    </div>
    <div class="mt-3.5 flex flex-col space-y-3.5">
      <InputsRadioOptionGroup
        v-model="selectedReplyBehavior"
        :options="replyOptions"
      />
    </div>
  </div>
</template>

<style scoped></style>
