<script setup lang="ts">
import { useStore } from 'vuex'

const emit = defineEmits(['set-new-rule', 'hide-save-discard-button'])
const store = useStore()

const showNewRule = computed(() => store.state.emails.showNewRule)
const showEditRule = computed(() => store.state.emails.showEditRule)
</script>

<template>
  <div class="w-full h-full overflow-hidden flex flex-col">
    <div
      class="px-6 py-4 text-xl leading-[26px] font-semibold text-[#333333] border-b border-[#F1F2F6]"
    >
      <p>Rules</p>
    </div>
    <div class="px-6 mt-3.5 flex-grow overflow-y-auto">
      <SourceHubMicrosoftSettingsMailRulesList
        v-if="!showNewRule && !showEditRule"
      />
      <SourceHubMicrosoftSettingsMailRulesAddNewRule
        v-show="showNewRule || showEditRule"
        @set-new-rule="($event) => emit('set-new-rule', $event)"
        @hide-save-discard-button="
          ($event) => emit('hide-save-discard-button', $event)
        "
      />
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
