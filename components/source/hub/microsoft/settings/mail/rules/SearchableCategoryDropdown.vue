<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

interface DropsDownProps {
  showArrow?: boolean
  placeHolder?: string
  zIndex?: number
}
const props = withDefaults(defineProps<DropsDownProps>(), {
  showArrow: true,
  placeHolder: 'Choose folder',
  zIndex: 10,
})
const emit = defineEmits(['change', 'activate'])
interface Categorize {
  id: number
  title: String
  color: String
}
const showAll = ref(false)
const searchTerm = ref('')
const options = computed(() => {
  const sorted = [
    ...JSON.parse(JSON.stringify(store.state.emails.actionCategoryOptions)),
  ].sort((a, b) => a.title.localeCompare(b.title))
  return showAll.value
    ? sorted.filter((option) =>
        option.title.toLowerCase().includes(searchTerm.value.toLowerCase()),
      )
    : sorted
        .slice(0, 6)
        .filter((option) =>
          option.title.toLowerCase().includes(searchTerm.value.toLowerCase()),
        )
})

const selectedOption = defineModel<Categorize | string | null>({
  default: null,
})
const dropdownRef = ref<HTMLDivElement | null>(null)
const searchInput = ref<HTMLInputElement | null>(null)
const createFolderInput = ref<HTMLInputElement | null>(null)

const createFolderInputText = ref('')
const showCreateFolderInput = ref(false)

const isOpen = ref(false)
const toggleDropdown = () => {
  isOpen.value = !isOpen.value

  if (isOpen.value) {
    emit('activate')
    nextTick(() => {
      searchInput.value?.focus()
    })
  }
}
const handleSelectOption = (option: Categorize) => {
  selectedOption.value = option
  isOpen.value = false
  emit('change', selectedOption.value)
}

const handleShowCreateFolderInput = () => {
  showCreateFolderInput.value = true
  nextTick(() => {
    createFolderInput.value?.focus()
  })
}

const handleCreateFolder = () => {
  if (createFolderInputText.value.trim() === '') return
  const newOption: Categorize = {
    id: new Date().getTime().toString(),
    title: createFolderInputText.value,
    color: 'default',
  }
  options.value.push(newOption)
  handleSelectOption(newOption)
  createFolderInputText.value = ''
  showCreateFolderInput.value = false
}

const handleClickOutside = (event: Event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div ref="dropdownRef" class="w-full relative">
    <button
      class="w-full rounded-full h-10 mt-4 text-base font-semibold leading-[21px] text-[#333333] flex items-center justify-center gap-2"
      :class="isOpen ? 'bg-[#E3EFFF]' : 'bg-[#F1F2F6]'"
      @click="toggleDropdown"
    >
      <div class="flex space-x-2 items-center">
        <ClientOnly v-if="selectedOption">
          <fa
            class="size-5 transform rotate-90"
            :style="{ color: selectedOption.color }"
            :icon="['fas', 'tag']"
          />
        </ClientOnly>
        <span>{{ selectedOption ? selectedOption.title : placeHolder }}</span>
      </div>
      <ClientOnly v-if="showArrow">
        <fa
          class="text-[#333333] w-3 transition-all duration-300 ease-in-out"
          :class="[isOpen ? 'rotate-180' : '']"
          :icon="['fas', 'chevron-down']"
        />
      </ClientOnly>
      <ClientOnly v-else-if="!showArrow">
        <fa
          class="text-xl text-[#4A71D4] transition-all duration-300 ease-in-out"
          :class="[isOpen ? 'rotate-180' : '']"
          :icon="['fas', 'caret-down']"
        />
      </ClientOnly>
    </button>

    <div
      v-if="isOpen"
      class="absolute z-10 w-full shadow-xl rounded-lg overflow-hidden"
      :style="{ zIndex: zIndex }"
    >
      <div
        class="w-full bg-white rounded-lg max-h-[300px] overflow-y-auto custom-scroll pb-1"
      >
        <div class="p-1 relative">
          <ClientOnly>
            <fa
              class="text-[#707070] absolute top-1/2 -translate-y-1/2 left-6"
              :icon="['fas', 'magnifying-glass']"
            />
          </ClientOnly>
          <input
            ref="searchInput"
            v-model="searchTerm"
            type="text"
            class="w-full pl-11 py-1.5 rounded-sm text-base leading-[21px] text-[#333333] outline-none border-none focus:ring-1"
            placeholder="Search for a category"
          />
        </div>
        <div
          v-for="option in options"
          :key="option.id"
          class="flex items-center px-6 py-[7px] hover:bg-[#4A71D4] text-[#525252] hover:text-white cursor-pointer gap-2 group"
          @click="handleSelectOption(option)"
        >
          <ClientOnly>
            <fa
              class="size-5 transform rotate-90"
              :style="{ color: option.color }"
              :icon="['fas', 'tag']"
            />
          </ClientOnly>
          <span>{{ option.title }}</span>
        </div>
        <div class="h-0.5 bg-[#F1F2F6]" />
        <!-- <div v-if="showCreateFolderInput" class="px-6 flex items-center gap-2">
          <DefaultFolderIcon class="w-5 min-w-5 text-[#707070]" />
          <input
            ref="createFolderInput"
            v-model="createFolderInputText"
            type="text"
            class="w-full py-2 rounded-sm text-base leading-[21px] text-[#333333] outline-none border-none"
            placeholder="New folder name"
          />
          <button
            class="px-4 py-1.5 rounded-full text-base leading-[21px] text-[#333333]"
            @click="handleCreateFolder"
          >
            Save
          </button>
        </div> -->
        <button
          class="w-full pl-11 py-2 text-base leading-[21px] text-[#525252] text-start hover:bg-[#4A71D4] hover:text-white"
          @click.stop="
            store.commit('emails/SET_SHOW_NEW_CATEGORY_MODAL', true),
              (isOpen = false)
          "
        >
          New category
        </button>
        <div class="h-0.5 bg-[#F1F2F6]" />
        <button
          class="w-full pl-11 py-2 text-base leading-[21px] text-[#525252] text-start hover:bg-[#4A71D4] hover:text-white"
          @click="showAll = !showAll"
        >
          All categories
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.folder-wrapper > button {
  margin-top: 0;
  justify-content: space-between;
  color: #707070;
  font-weight: normal;
  height: 35px;
  padding-inline: 16px;
}
.folder-wrapper > button > div > .icon {
  color: #707070;
}
</style>
