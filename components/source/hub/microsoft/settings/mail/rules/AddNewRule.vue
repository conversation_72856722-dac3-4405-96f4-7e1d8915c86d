<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core'
import { required } from '@vuelidate/validators'
import { useStore } from 'vuex'

const store = useStore()
const showEditRule = computed(() => store.state.emails.showEditRule)
const newRuleDataStore = computed(() => store.state.emails.newRuleData)
const emit = defineEmits(['set-new-rule', 'hide-save-discard-button'])

const allRules = ref({
  ruleName: '',
})
const rules = {
  ruleName: {
    required,
  },
}
const v$ = useVuelidate(rules, allRules)
const conditions = ref<any>([
  {
    id: 1,
    headerLabel: 'People',
    text: 'From',
    peopleTextField: true,
    selectinputField: true,
    placeHolder: '',
    errorMessage: 'Please add a sender',
    options: [
      {
        id: 1,
        headerLabel: 'Suggested contacts',
        name: 'Sharp Archive',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_1.png',
      },
      {
        id: 2,
        name: '<PERSON>',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_2.png',
      },
      {
        id: 3,
        name: 'Jane Doe',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_3.png',
      },
    ],
  },
  {
    id: 2,
    text: 'To',
    peopleTextField: true,
    selectinputField: true,
    placeHolder: '',
    errorMessage: 'Please add a recipient',
    options: [
      {
        id: 1,
        headerLabel: 'Suggested contacts',
        name: 'Sharp Archive',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_1.png',
      },
      {
        id: 2,
        name: 'Sam Wilson',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_2.png',
      },
      {
        id: 3,
        name: 'Jane Doe',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_3.png',
      },
    ],
  },
  {
    id: 3,
    text: 'Emails received for others',
    peopleTextField: true,
    selectinputField: true,
    placeHolder: '',
    errorMessage: 'Select a value',
    options: [
      {
        id: 1,
        headerLabel: 'Suggested contacts',
        name: 'Sharp Archive',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_1.png',
      },
      {
        id: 2,
        name: 'Sam Wilson',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_2.png',
      },
      {
        id: 3,
        name: 'Jane Doe',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_3.png',
      },
    ],
  },
  {
    id: 4,
    headerLabel: 'My name is',
    text: `I'm on the To line`,
    inputField: false,
  },
  {
    id: 5,
    text: `I'm on the Cc line`,
    inputField: false,
  },
  {
    id: 6,
    text: `I'm on the To or Cc line`,
    inputField: false,
  },
  {
    id: 7,
    text: `I'm not on the To line`,
    inputField: false,
  },
  {
    id: 8,
    text: `I'm the only recipient`,
    inputField: false,
  },
  {
    id: 9,
    headerLabel: 'Subject',
    text: 'Subject includes',
    inputField: true,
    placeHolder: 'Enter words to look for',
  },
  {
    id: 10,
    text: 'Subject or body includes',
    inputField: true,
    placeHolder: 'Enter words to look for',
  },
  {
    id: 11,
    headerLabel: 'Keywords',
    text: 'Message body includes',
    inputField: true,
    placeHolder: 'Enter words to look for',
  },
  {
    id: 12,
    text: 'Sender address includes',
    inputField: true,
    placeHolder: 'Enter all or part of an address',
  },
  {
    id: 13,
    text: 'Recipient address includes',
    inputField: true,
    placeHolder: 'Enter all or part of an address',
  },
  {
    id: 14,
    text: 'Message header includes',
    inputField: true,
    placeHolder: 'Enter words to look for',
  },
  {
    id: 15,
    headerLabel: 'Marked with',
    text: 'Importance',
    selectField: true,
    placeHolder: 'Select an option',
    errorMessage: 'Select a value',
    options: [
      { id: 1, text: 'High' },
      { id: 2, text: 'Normal' },
      { id: 3, text: 'Low' },
    ],
  },
  {
    id: 16,
    text: 'Sensitivity',
    selectField: true,
    placeHolder: 'Select an option',
    errorMessage: 'Select a value',
    options: [
      { id: 4, text: 'Company confidential' },
      { id: 1, text: 'Normal' },
      { id: 2, text: 'Personal' },
      { id: 3, text: 'Private' },
    ],
  },
  {
    id: 17,
    headerLabel: 'Message includes',
    text: 'Flag',
    selectField: true,
    placeHolder: 'Select an option',
    errorMessage: 'Select a value',
    options: [
      { id: 1, text: 'Any' },
      { id: 2, text: 'Call' },
      { id: 3, text: 'Do not forward' },
      { id: 4, text: 'Follow up' },
      { id: 5, text: 'Forward' },
      { id: 6, text: 'For your information' },
      { id: 7, text: 'No response necessary' },
      { id: 8, text: 'Mark as read' },
      { id: 9, text: 'Reply' },
      { id: 10, text: 'Reply to all' },
      { id: 11, text: 'Review' },
    ],
  },
  {
    id: 18,
    text: 'Type',
    selectField: true,
    placeHolder: 'Select an option',
    errorMessage: 'Select a value',
    options: [
      { id: 1, text: 'Request for approval' },
      { id: 2, text: 'Automatically forwarded' },
      { id: 3, text: 'Automatic reply' },
      { id: 4, text: 'Invitation' },
      { id: 5, text: 'Event response' },
      { id: 6, text: 'Encrypted' },
      { id: 7, text: 'Non-delivery report' },
      { id: 8, text: 'Permission-controlled' },
      { id: 9, text: 'Read receipt' },
      { id: 10, text: 'Signed' },
      { id: 11, text: 'Voice mail' },
    ],
  },
  {
    id: 19,
    text: 'Has attachment',
    inputField: false,
    selectinputField: false,
  },
  {
    id: 20,
    headerLabel: 'Message size',
    text: 'At least',
    numberinputField: true,
  },
  {
    id: 21,
    text: 'At most',
    numberinputField: true,
  },
  {
    id: 22,
    headerLabel: 'Received',
    text: 'Before',
    dateinputField: true,
  },
  {
    id: 23,
    text: 'After',
    dateinputField: true,
  },
  {
    id: 24,
    headerLabel: 'All messages',
    text: 'Apply to all messages',
  },
])
const exceptions = ref([
  {
    id: 1,
    headerLabel: 'People',
    text: 'From',
    selectinputField: true,
    placeHolder: '',
    errorMessage: 'Please add a sender',
    options: [
      {
        id: 1,
        headerLabel: 'Suggested contacts',
        name: 'Sharp Archive',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_1.png',
      },
      {
        id: 2,
        name: 'Sam Wilson',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_2.png',
      },
      {
        id: 3,
        name: 'Jane Doe',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_3.png',
      },
    ],
  },
  {
    id: 2,
    text: 'To',
    selectinputField: true,
    placeHolder: '',
    errorMessage: 'Please add a recipient',
    options: [
      {
        id: 1,
        headerLabel: 'Suggested contacts',
        name: 'Sharp Archive',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_1.png',
      },
      {
        id: 2,
        name: 'Sam Wilson',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_2.png',
      },
      {
        id: 3,
        name: 'Jane Doe',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_3.png',
      },
    ],
  },
  {
    id: 3,
    text: 'Emails received for others',
    selectinputField: true,
    placeHolder: '',
    errorMessage: 'Select a value',
    options: [
      {
        id: 1,
        headerLabel: 'Suggested contacts',
        name: 'Sharp Archive',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_1.png',
      },
      {
        id: 2,
        name: 'Sam Wilson',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_2.png',
      },
      {
        id: 3,
        name: 'Jane Doe',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_3.png',
      },
    ],
  },
  {
    id: 4,
    headerLabel: 'My name is',
    text: `I'm on the To line`,
    inputField: false,
  },
  {
    id: 5,
    text: `I'm on the Cc line`,
    inputField: false,
  },
  {
    id: 6,
    text: `I'm on the To or Cc line`,
    inputField: false,
  },
  {
    id: 7,
    text: `I'm not on the To line`,
    inputField: false,
  },
  {
    id: 8,
    text: `I'm the only recipient`,
    inputField: false,
  },
  {
    id: 9,
    headerLabel: 'Subject',
    text: 'Subject includes',
    inputField: true,
    placeHolder: 'Enter words to look for',
  },
  {
    id: 10,
    text: 'Subject or body includes',
    inputField: true,
    placeHolder: 'Enter words to look for',
  },
  {
    id: 11,
    headerLabel: 'Keywords',
    text: 'Message body includes',
    inputField: true,
    placeHolder: 'Enter words to look for',
  },
  {
    id: 12,
    text: 'Sender address includes',
    inputField: true,
    placeHolder: 'Enter all or part of an address',
  },
  {
    id: 13,
    text: 'Recipient address includes',
    inputField: true,
    placeHolder: 'Enter all or part of an address',
  },
  {
    id: 14,
    text: 'Message header includes',
    inputField: true,
    placeHolder: 'Enter words to look for',
  },
  {
    id: 15,
    headerLabel: 'Marked with',
    text: 'Importance',
    selectField: true,
    placeHolder: 'Select an option',
    errorMessage: 'Select a value',
    options: [
      { id: 1, text: 'High' },
      { id: 2, text: 'Normal' },
      { id: 3, text: 'Low' },
    ],
  },
  {
    id: 16,
    text: 'Sensitivity',
    selectField: true,
    placeHolder: 'Select an option',
    errorMessage: 'Select a value',
    options: [
      { id: 4, text: 'Company confidential' },
      { id: 1, text: 'Normal' },
      { id: 2, text: 'Personal' },
      { id: 3, text: 'Private' },
    ],
  },
  {
    id: 17,
    headerLabel: 'Message includes',
    text: 'Flag',
    selectField: true,
    placeHolder: 'Select an option',
    errorMessage: 'Select a value',
    options: [
      { id: 1, text: 'Any' },
      { id: 2, text: 'Call' },
      { id: 3, text: 'Do not forward' },
      { id: 4, text: 'Follow up' },
      { id: 5, text: 'Forward' },
      { id: 6, text: 'For your information' },
      { id: 7, text: 'No response necessary' },
      { id: 8, text: 'Mark as read' },
      { id: 9, text: 'Reply' },
      { id: 10, text: 'Reply to all' },
      { id: 11, text: 'Review' },
    ],
  },
  {
    id: 18,
    text: 'Type',
    selectField: true,
    placeHolder: 'Select an option',
    errorMessage: 'Select a value',
    options: [
      { id: 1, text: 'Request for approval' },
      { id: 2, text: 'Automatically forwarded' },
      { id: 3, text: 'Automatic reply' },
      { id: 4, text: 'Invitation' },
      { id: 5, text: 'Event response' },
      { id: 6, text: 'Encrypted' },
      { id: 7, text: 'Non-delivery report' },
      { id: 8, text: 'Permission-controlled' },
      { id: 9, text: 'Read receipt' },
      { id: 10, text: 'Signed' },
      { id: 11, text: 'Voice mail' },
    ],
  },
  {
    id: 19,
    text: 'Has attachment',
    inputField: false,
    selectinputField: false,
  },
  {
    id: 20,
    headerLabel: 'Message size',
    text: 'At least',
    numberinputField: true,
  },
  {
    id: 21,
    text: 'At most',
    numberinputField: true,
  },
  {
    id: 22,
    headerLabel: 'Received',
    text: 'Before',
    dateinputField: true,
  },
  {
    id: 23,
    text: 'After',
    dateinputField: true,
  },
  // {
  //   id: 24,
  //   headerLabel: 'All messages',
  //   text: 'Apply to all messages',
  // },
])
const actions = ref([
  {
    id: 1,
    headerLabel: 'Organize',
    text: 'Move to',
    searchableSelectField: true,
    placeHolder: 'Select a folder',
  },
  {
    id: 2,
    text: 'Copy to',
    searchableSelectField: true,
    placeHolder: 'Select a folder',
  },
  {
    id: 3,
    text: 'Delete',
  },
  {
    id: 4,
    text: 'Pin to top',
  },
  {
    id: 5,
    headerLabel: 'Mark message',
    text: 'Mark as read',
  },
  {
    id: 6,
    text: 'Mark as Junk',
  },
  {
    id: 7,
    text: 'Mark with importance',
    selectField: true,
    placeHolder: 'Select an option',
    errorMessage: 'Select a value',
    options: [
      { id: 1, text: 'High' },
      { id: 2, text: 'Normal' },
      { id: 3, text: 'Low' },
    ],
  },
  {
    id: 8,
    text: 'Categorize',
    searchableCategorizeField: true,
    placeHolder: 'Select a category',
  },
  {
    id: 9,
    headerLabel: 'Route',
    text: 'Forward to',
    selectinputField: true,
    placeHolder: '',
    errorMessage: 'Please add a sender',
    options: [
      {
        id: 1,
        headerLabel: 'Suggested contacts',
        name: 'Sharp Archive',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_1.png',
      },
      {
        id: 2,
        name: 'Sam Wilson',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_2.png',
      },
      {
        id: 3,
        name: 'Jane Doe',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_3.png',
      },
    ],
  },
  {
    id: 10,
    text: 'Forward as attachment',
    selectinputField: true,
    placeHolder: '',
    errorMessage: 'Please add a sender',
    options: [
      {
        id: 1,
        headerLabel: 'Suggested contacts',
        name: 'Sharp Archive',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_1.png',
      },
      {
        id: 2,
        name: 'Sam Wilson',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_2.png',
      },
      {
        id: 3,
        name: 'Jane Doe',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_3.png',
      },
    ],
  },
  {
    id: 11,
    text: 'Redirect to',
    selectinputField: true,
    placeHolder: '',
    errorMessage: 'Please add a sender',
    options: [
      {
        id: 1,
        headerLabel: 'Suggested contacts',
        name: 'Sharp Archive',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_1.png',
      },
      {
        id: 2,
        name: 'Sam Wilson',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_2.png',
      },
      {
        id: 3,
        name: 'Jane Doe',
        text: '<EMAIL>',
        subtext: '<EMAIL>',
        image: '/images/microsoft-mail-settings/profile_3.png',
      },
    ],
  },
])
const moreRules = ref<boolean>(true)
const activeFormItem = ref<number>(0)
const setActiveFormItem = (index: number) => {
  activeFormItem.value = index
}
onMounted(() => {
  document.addEventListener('click', (event) => {
    setActiveFormItem(0)
  })
  allRuleDta.value = {
    ruleName: '',
    newconditionsList: [
      {
        id: Date.now(),
        condition: null,
        allContext: [],
        inputValue: '',
        error: false,
        activeItem: false,
      },
    ],
    newactionList: [
      {
        id: Date.now(),
        action: null,
        allContext: [],
        inputValue: '',
        error: false,
        activeItem: false,
      },
    ],
    newexceptionList: [
      {
        id: Date.now(),
        exception: null,
        allContext: [],
        inputValue: '',
        error: false,
        activeItem: false,
      },
    ],
  }
  allRules.value.ruleName = ''
  newconditionsList.value = allRuleDta.value.newconditionsList
  newactionList.value = allRuleDta.value.newactionList
  newexceptionList.value = allRuleDta.value.newexceptionList
  v$.value.$reset()
})
onUnmounted(() => {
  document.removeEventListener('click', (event) => {
    setActiveFormItem(0)
  })
})

const setNewRule = () => {
  v$.value.$touch()
  if (!v$.value.ruleName.$invalid) {
    store.commit('emails/SET_GENERATED_RULES', allRules.value.ruleName)
    store.commit('emails/SHOW_NEW_RULE', false)
  }
}
const showException = ref<boolean>(false)
const newconditionsList = ref([])
const newactionList = ref([])
const newexceptionList = ref([])
const allRuleDta = ref({
  ruleName: '',
  newconditionsList: [
    {
      id: Date.now(),
      option: null,
      allContext: [],
      inputValue: '',
      error: false,
      activeItem: false,
    },
  ],
  newactionList: [
    {
      id: Date.now(),
      option: null,
      allContext: [],
      inputValue: '',
      error: false,
      activeItem: false,
    },
  ],
  newexceptionList: [
    {
      id: Date.now(),
      option: null,
      allContext: [],
      inputValue: '',
      error: false,
      activeItem: false,
    },
  ],
})
const conditionStatus = ref({})
const actionStatus = ref({})
const exceptionStatus = ref({})

const handleStatus = (status) => {
  if (status.type === 'condition') conditionStatus.value = status
  else if (status.type === 'action') actionStatus.value = status
  else if (status.type === 'exception') exceptionStatus.value = status
}
watch(
  () => ({
    ruleName: allRules.value.ruleName,
    newconditionsList: newconditionsList.value,
    newactionList: newactionList.value,
    newexceptionList: newexceptionList.value,
    showException: showException.value,
    conditionStatus: conditionStatus.value,
    actionStatus: actionStatus.value,
    exceptionStatus: exceptionStatus.value,
  }),
  (newValue) => {
    allRuleDta.value.ruleName = newValue.ruleName
    allRuleDta.value.newconditionsList = newValue.newconditionsList
    allRuleDta.value.newactionList = newValue.newactionList
    allRuleDta.value.newexceptionList = newValue.newexceptionList
    // Use the generic names from each status object
    if (
      !v$.value.ruleName.$invalid &&
      newValue.conditionStatus.allErrors &&
      newValue.conditionStatus.hasAnyOption &&
      !newValue.conditionStatus.optionInputFieldShow &&
      newValue.actionStatus.allErrors &&
      newValue.actionStatus.hasAnyOption &&
      newValue.actionStatus.hasAnyinputValue
    ) {
      if (showException.value) {
        if (
          newValue.exceptionStatus.allErrors &&
          newValue.exceptionStatus.hasAnyOption &&
          !newValue.exceptionStatus.optionInputFieldShow
        ) {
          emit('set-new-rule', allRuleDta.value)
          emit('hide-save-discard-button', true)
        } else {
          emit('hide-save-discard-button', false)
        }
      } else {
        emit('set-new-rule', allRuleDta.value)
        emit('hide-save-discard-button', true)
      }
    } else {
      emit('hide-save-discard-button', false)
    }
  },
  { deep: true },
)
watch(
  () => showEditRule.value,
  (value) => {
    if (value) {
      allRuleDta.value = JSON.parse(JSON.stringify(newRuleDataStore.value))
      allRules.value.ruleName = allRuleDta.value.ruleName
      newconditionsList.value = allRuleDta.value.newconditionsList
      newactionList.value = allRuleDta.value.newactionList
      newexceptionList.value = allRuleDta.value.newexceptionList
    } else {
      allRuleDta.value = {
        ruleName: '',
        newconditionsList: [
          {
            id: Date.now(),
            option: null,
            allContext: [],
            inputValue: '',
            error: false,
            activeItem: false,
          },
        ],
        newactionList: [
          {
            id: Date.now(),
            option: null,
            allContext: [],
            inputValue: '',
            error: false,
            activeItem: false,
          },
        ],
        newexceptionList: [
          {
            id: Date.now(),
            option: null,
            allContext: [],
            inputValue: '',
            error: false,
            activeItem: false,
          },
        ],
      }
      allRules.value.ruleName = ''
      newconditionsList.value = allRuleDta.value.newconditionsList
      newactionList.value = allRuleDta.value.newactionList
      newexceptionList.value = allRuleDta.value.newexceptionList
      v$.value.$reset()
    }
  },
  { deep: true },
)
watch(
  () => store.state.emails.showNewRule,
  (value) => {
    if (value) {
      allRuleDta.value = {
        ruleName: '',
        newconditionsList: [
          {
            id: Date.now(),
            option: null,
            allContext: [],
            inputValue: '',
            error: false,
            activeItem: false,
          },
        ],
        newactionList: [
          {
            id: Date.now(),
            option: null,
            allContext: [],
            inputValue: '',
            error: false,
            activeItem: false,
          },
        ],
        newexceptionList: [
          {
            id: Date.now(),
            option: null,
            allContext: [],
            inputValue: '',
            error: false,
            activeItem: false,
          },
        ],
      }
      allRules.value.ruleName = ''
      newconditionsList.value = allRuleDta.value.newconditionsList
      newactionList.value = allRuleDta.value.newactionList
      newexceptionList.value = allRuleDta.value.newexceptionList
      v$.value.$reset()
    }
  },
)
</script>

<template>
  <div class="w-full h-full flex flex-col">
    <div class="flex flex-col space-y-6 flex-grow">
      <div class="flex space-x-4" @click="setActiveFormItem(1)" key="1">
        <div
          v-if="allRules.ruleName && activeFormItem !== 1"
          class="bg-[#e3efff] w-[35px] h-[35px] rounded-full flex justify-center items-center"
        >
          <fa
            class="text-base text-[#4A71D4] bg-[#e3efff]"
            :icon="['fa', 'check']"
          />
        </div>
        <div
          v-else
          class="rule-number w-[35px] h-[35px] rounded-full flex justify-center items-center"
          :class="
            activeFormItem === 1
              ? 'bg-[#4A71D4] text-white'
              : 'bg-[#F1F2F6] text-[#525252]'
          "
        >
          1
        </div>
        <InputsTextInput
          id="ruleName"
          v-model="v$.ruleName.$model"
          class="w-full"
          extend-class="!h-[35px] !shadow-none !border-none max-w-[800px] !w-full"
          :placeHolderClass="activeFormItem === 1 ? '#525252' : '#707070'"
          :error-message="v$.ruleName.$errors"
          placeHolder="Name your rule"
          :color="activeFormItem === 1 ? '#525252' : '#707070'"
          :background="activeFormItem === 1 ? '#e3efff' : '#F1F2F6'"
          :error="v$.ruleName.$error"
        />
      </div>
      <SourceHubMicrosoftSettingsMailRulesCommonRulesInput
        v-model="newconditionsList"
        :options="conditions"
        type="condition"
        :number="2"
        @update:list="newconditionsList = $event"
        @status="handleStatus"
      />
      <SourceHubMicrosoftSettingsMailRulesCommonRulesInput
        v-model="newactionList"
        title="Add an action"
        :options="actions"
        place-holder="Select an action"
        text-button="Add another action"
        type="action"
        :number="3"
        :show-exception="showException"
        @show-exception="showException = true"
        @update:list="newactionList = $event"
        @status="handleStatus"
      />
      <SourceHubMicrosoftSettingsMailRulesCommonRulesInput
        v-if="showException"
        v-model="newexceptionList"
        :number="4"
        title="Add an exception (optional)"
        :options="exceptions"
        place-holder="Select an exception"
        text-button="Add another exception"
        type="exception"
        :show-exception="showException"
        @show-exception="showException = false"
        @update:list="newexceptionList = $event"
        @status="handleStatus"
      />
      <div class="mt-[22px]">
        <label
          class="w-full relative flex space-x-2 items-start cursor-pointer"
          for="more_rules"
        >
          <div class="w-4 h-4 relative mt-0.5">
            <input
              ref="more_rules"
              id="more_rules"
              type="checkbox"
              v-model="moreRules"
              :checked="moreRules ? true : false"
              class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
              :class="moreRules ? '' : 'border border-[#333333]'"
            />
            <ClientOnly>
              <fa
                class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                :icon="['fas', 'check']"
              />
            </ClientOnly>
          </div>
          <div class="text-[#333333] flex space-x-2 items-center">
            <p>Stop processing more rule</p>
            <img
              class="w-3.5 h-3.5"
              src="/images/microsoft-mail-settings/query.svg"
              alt="query"
            />
          </div>
        </label>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.toggle-check-1 {
  &:checked {
    @apply bg-[#4A71D4];
  }
  &:checked + .check-1 {
    @apply opacity-100;
    color: white;
  }
}
.text-clamp {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
