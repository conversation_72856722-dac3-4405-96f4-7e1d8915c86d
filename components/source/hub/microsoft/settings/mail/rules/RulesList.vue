<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const generatedRules = computed(() => {
  return store.state.emails.generatedRules
})
</script>

<template>
  <div class="w-full h-full flex flex-col">
    <div
      class="pb-3.5 border-b border-[#F1F2F6] flex justify-between items-center"
    >
      <p class="text-[#707070] max-w-[478px]">
        You can create rules that tell Outlook how to handle incoming email
        messages.
      </p>
      <button
        class="w-[158px] h-9 rounded-full bg-[#4A71D4] text-[#FFFFFF] flex space-x-[11px] justify-center items-center"
        @click="store.commit('emails/SHOW_NEW_RULE', true)"
      >
        <fa class="text-[#FFFFFF] text-sm" :icon="['fas', 'plus']" />
        <span class="text-sm">Add new rule</span>
      </button>
    </div>
    <div class="flex-grow py-3.5">
      <SourceHubMicrosoftSettingsMailRulesOption
        v-if="generatedRules && generatedRules.length > 0"
      />
      <div
        v-else
        class="w-full h-full flex flex-col space-y-[22px] justify-center items-center"
      >
        <div class="w-full flex justify-center items-center">
          <img
            class="w-[96px] h-[88px]"
            src="/images/microsoft-mail-settings/no-rules.svg"
            alt="No Rules"
          />
        </div>
        <p class="text-[#525252]">You haven't created any rules yet.</p>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
