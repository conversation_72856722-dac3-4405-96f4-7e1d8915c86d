<script setup lang="ts">
import { getNextId } from '@/utils/labelHelpers'
import { isSameDay, format } from 'date-fns'

const emit = defineEmits(['show-exception', 'update:list', 'status'])
interface CommonRulesInput {
  number?: number
  title?: string
  textButton?: string
  placeHolder?: string
  options?: any | null
  type?: string
  showException?: boolean
  modelValue?: OptionList[]
}
const props = withDefaults(defineProps<CommonRulesInput>(), {
  number: 2,
  title: 'Add a condition',
  textButton: 'Add another condition',
  placeHolder: 'Select a condition',
  options: null,
  type: 'condition',
  showException: false,
  modelValue: () => [
    {
      id: Date.now(),
      option: null,
      allContext: [],
      inputValue: '',
      error: false,
      activeItem: false,
    },
  ],
})

interface OptionList {
  id: number
  option: any
  allContext: string[]
  inputValue: string | number
  error: boolean
  activeItem: boolean
}
const newOptionList = ref<OptionList[]>([
  {
    id: Date.now(),
    option: null,
    allContext: [],
    inputValue: '',
    error: false,
    activeItem: false,
  },
])
// const newOptionList = ref<OptionList[]>(props.modelValue)
const getAvailableOptions = (currentId: number) => {
  const selectedTexts = newOptionList.value
    .filter((b: OptionList) => b.id !== currentId && b.option)
    .map((b: OptionList) => b.option.text)

  const cloned = props.options?.map((cond: any) => ({ ...cond })) // shallow clone
  if (cloned) {
    // Step 1: blank selected items
    for (const item of cloned) {
      if (selectedTexts.includes(item.text)) {
        item.text = ''
      }
    }

    // Step 2: hide headerLabel if all items in same group are blank
    const groupedByHeader: Record<string, any[]> = {}
    let previousHeaderLabel = ''
    for (const item of cloned) {
      if (item.headerLabel) {
        previousHeaderLabel = item.headerLabel
        if (!groupedByHeader[item.headerLabel]) {
          groupedByHeader[item.headerLabel] = []
        }
        groupedByHeader[item.headerLabel].push(item)
      } else {
        groupedByHeader[previousHeaderLabel].push(item)
      }
    }
    for (const [label, group] of Object.entries(groupedByHeader)) {
      const allBlank = group.every((item) => item.text === '')
      if (allBlank) {
        group[0].headerLabel = '' // hide label from dropdown
      }
    }
  }

  return cloned
}
const optionInputFieldShow = ref(false)
const addOptionBlock = () => {
  newOptionList.value.push({
    id: Date.now(),
    option: null,
    inputValue: '',
    allContext: [],
    error: false,
    activeItem: false,
  })
}
const hasAnyOption = computed(() =>
  newOptionList.value.every((item: OptionList) => item.option !== null),
)
const hasAnyinputValue = computed(() => {
  return newOptionList.value.every((item: OptionList) => item.inputValue !== '')
})
const allInactive = computed(() =>
  newOptionList.value.every((item: OptionList) => item.activeItem === false),
)
const allErrors = computed(() =>
  newOptionList.value.every((item: OptionList) => item.error === false),
)
const hasAnyactive = computed(() =>
  newOptionList.value.some((item: OptionList) => item.activeItem === true),
)
const setActiveItem = (items: OptionList[], index: number) => {
  if (items && items.length > 0) {
    items.forEach((item, i) => {
      item.activeItem = i === index
    })
  }
}
const removeOptionBlock = (id: number) => {
  newOptionList.value = newOptionList.value.filter(
    (block: OptionList) => block.id !== id,
  )
}
const removeContextItem = (index: number, block: OptionList) => {
  block.allContext.splice(index, 1)
  if (block.allContext.length === 0) {
    block.error = true
  }
}
const activeDropId = ref<number | null>(null) // Track which one is active
const baseZIndex = 10
const zIndexBoost = 10
const getZIndex = (id: number) => {
  return activeDropId.value === id ? baseZIndex + zIndexBoost : baseZIndex
}
const setActiveDropdown = (id: number) => {
  activeDropId.value = id
}
const inputRef = ref<HTMLElement | null>(null)
const selectinputRef = ref<HTMLElement | null>(null)
const selectRef = ref<HTMLElement | null>(null)
const inputHeight = ref<number>(0)
const observer = ref<ResizeObserver | null>(null)
const {
  selectedDate,
  selectedFirstDay,
  selectedLastDay,
  currentTab,
  currentTitle,
  currentMonthYear,
  weekRange,
  isToday,
  isThisWeek,
  isThisMonth,
  handleGoToPrev,
  handleGoToNext,
  handleGoToToday,
  handleTabChange,
  setMonthYear,
  synchronizeCalendarRange,
  synchronizeCalendarMonth,
} = useCalendarManager()

const getInputHeight = (refValue) => {
  observer.value = new ResizeObserver((entries) => {
    for (const entry of entries) {
      const height = entry.contentRect.height
      inputHeight.value = height
    }
  })
  observer.value.observe(refValue)
}
// let observer: ResizeObserver | null = null
onMounted(() => {
  document.addEventListener('click', (event) => {
    newOptionList.value.forEach((item: OptionList, i: number) => {
      item.activeItem = false
    })
  })
})
onUnmounted(() => {
  document.removeEventListener('click', (event) => {
    newOptionList.value.forEach((item: OptionList, i: number) => {
      item.activeItem = false
    })
  })
  if (observer.value && inputRef.value) {
    observer.value.unobserve(inputRef.value)
    observer.value.disconnect()
  }
  if (observer.value && selectinputRef.value) {
    observer.value.unobserve(selectinputRef.value)
    observer.value.disconnect()
  }
  if (observer.value && selectRef.value) {
    observer.value.unobserve(selectRef.value)
    observer.value.disconnect()
  }
})
watch(
  () => props.modelValue,
  (val) => {
    newOptionList.value = val
  },
  { deep: true },
)
watch(
  newOptionList,
  (val) => {
    emit('update:list', val)
  },
  { deep: true },
)
watch(
  [allErrors, hasAnyOption, hasAnyinputValue, optionInputFieldShow],
  () => {
    emit('status', {
      allErrors: allErrors.value,
      hasAnyOption: hasAnyOption.value,
      hasAnyinputValue: hasAnyinputValue.value,
      optionInputFieldShow: optionInputFieldShow.value,
      type: props.type, // 'condition' | 'action' | 'exception'
    })
  },
  { immediate: true },
)
</script>

<template>
  <div class="flex space-x-4">
    <div
      v-if="
        hasAnyOption &&
        (!optionInputFieldShow || hasAnyinputValue) &&
        allInactive &&
        allErrors
      "
      class="bg-[#e3efff] w-[35px] h-[35px] rounded-full flex justify-center items-center"
    >
      <fa class="text-base text-[#4A71D4]" :icon="['fa', 'check']" />
    </div>
    <div
      v-else
      class="rule-number w-[35px] h-[35px] rounded-full flex justify-center items-center"
      :class="
        hasAnyactive ? 'bg-[#4A71D4] text-white' : 'bg-[#F1F2F6] text-[#525252]'
      "
    >
      {{ number }}
    </div>
    <div class="flex flex-col space-y-4 w-full">
      <p class="text-lg text-[#525252]">
        {{ title }}
      </p>
      <div
        v-for="(block, index) in newOptionList"
        :key="block.id"
        class="flex items-start space-x-2 w-full"
        @click="setActiveItem(newOptionList, index)"
      >
        <BaseDropsDown
          key="2"
          class="rounded-full language-dropdown"
          :options="getAvailableOptions(block.id)"
          labelKey="text"
          mailLabelKey="headerLabel"
          :placeholder="block.option ? block.option.text : placeHolder"
          :menuWidth="396"
          :menuHeight="35"
          :dropdownWidth="240"
          :dropdownMaxHeight="2000"
          :menuBgColor="block.activeItem ? '#e3efff' : '#F1F2F6'"
          :menuTextColor="block.activeItem ? '#525252' : '#707070'"
          dropsdownTextColor="#333333"
          scrollbarTrackColor="#a1cdff50"
          scrollbarThumbColor="#a1cdff"
          hoverColor="#4A71D4"
          :dropdownPosition="
            type === 'action'
              ? 'absolute'
              : 'fixed top-0 xl:left-[815px] left-[515px] !h-full'
          "
          hoverTextColor="#FFFFFF"
          :z-index="getZIndex(1)"
          @activate="setActiveDropdown(1)"
          @change="
            ($event) => {
              block.option = $event
              if (
                !block.option?.selectinputField &&
                !block.option?.selectField &&
                !block.option?.inputField &&
                !block.option?.numberinputField &&
                !block.option?.dateinputField &&
                !block.option?.searchableSelectField &&
                !block.option?.searchableCategorizeField
              ) {
                block.inputValue = $event
                optionInputFieldShow = false
              } else {
                block.inputValue = ''
                optionInputFieldShow = true
              }
              block.allContext = []
              v$.$reset()
            }
          "
        />
        <div
          v-if="block.option?.selectinputField || block.option?.selectField"
          class="w-full max-w-[396px]"
          :ref="
            block.option?.selectinputField ? 'selectinputRef' : 'selectField'
          "
        >
          <div
            tabindex="0"
            class="w-full bg-[#F1F2F6] pl-4 flex items-center flex-wrap max-w-[396px]"
            :class="
              inputHeight > 35 && block.option?.selectinputField
                ? 'rounded-lg'
                : 'rounded-full'
            "
          >
            <div
              v-for="(item, index) in block.allContext"
              :key="item.id"
              class="text-[#333333] w-auto max-w-[92%] pr-2 my-1 mx-1 bg-[#FFFFFF] rounded-full px-2 flex items-center justify-center"
            >
              <p class="text-clamp">{{ item.text }}</p>
              <ClientOnly>
                <fa
                  class="text-[#707070] w-2.5 h-2.5 ml-2 font-normal cursor-pointer"
                  :icon="['fas', 'times']"
                  @click="removeContextItem(index, block)"
                />
              </ClientOnly>
            </div>
            <BaseDropsDown
              v-model="block.inputValue"
              key="2"
              class="rounded-full select-dropdown"
              :options="block.option?.options"
              labelKey="text"
              subTextKey="subtext"
              mailLabelKey="headerLabel"
              mailLabelColor="#525252"
              :placeholder="block.option?.placeHolder"
              :menuWidth="366"
              :menuHeight="35"
              :dropdownWidth="366"
              :dropdownMaxHeight="2000"
              menuBgColor="#F1F2F6"
              menuTextColor="#707070"
              dropsdownTextColor="#333333"
              scrollbarTrackColor="#a1cdff50"
              scrollbarThumbColor="#a1cdff"
              hoverColor="#4A71D4"
              hoverTextColor="#FFFFFF"
              :hideArrow="block.option?.selectinputField ? true : false"
              :z-index="getZIndex(2)"
              @activate="setActiveDropdown(2)"
              @mouseout="
                () => {
                  if (!block.inputValue && block.allContext.length === 0) {
                    block.error = true
                  }
                }
              "
              @change="
                ($event) => {
                  block.inputValue = $event
                  block.error = false
                  if (block.inputValue) {
                    if (block.option?.selectinputField) {
                      block.allContext.push({
                        id: Date.now(),
                        text: block.inputValue.name,
                      })
                      optionInputFieldShow = false
                      if (selectinputRef[0]) {
                        getInputHeight(selectinputRef[0])
                      }
                    } else if (block.option?.selectField) {
                      optionInputFieldShow = false
                      if (selectRef[0]) {
                        getInputHeight(selectRef[0])
                      }
                    }
                  }
                }
              "
            />
          </div>
          <template v-if="block.error && block.allContext.length === 0">
            <p class="text-red-500 text-xs mt-1">
              {{ block.option?.errorMessage }}
            </p>
          </template>
        </div>
        <div
          v-if="block.option?.inputField"
          class="w-full max-w-[396px]"
          ref="inputRef"
        >
          <div
            tabindex="0"
            class="w-full bg-[#F1F2F6] pl-4 flex items-center flex-wrap max-w-[396px]"
            :class="inputHeight > 35 ? 'rounded-lg' : 'rounded-full'"
          >
            <div
              v-for="(item, index) in block.allContext"
              :key="item.id"
              class="text-[#333333] w-auto max-w-[92%] pr-2 my-1 mx-1 bg-[#FFFFFF] rounded-full px-2 flex items-center justify-center"
            >
              <p class="text-clamp">{{ item.text }}</p>
              <ClientOnly>
                <fa
                  class="text-[#707070] w-2.5 h-2.5 ml-2 font-normal cursor-pointer"
                  :icon="['fas', 'times']"
                  @click="removeContextItem(index, block)"
                />
              </ClientOnly>
            </div>
            <InputsTextInput
              id="conditionText"
              tabindex="0"
              v-model="block.inputValue"
              class="rounded-full flex-grow"
              extend-class="!h-[35px] !shadow-none !border-none inputField"
              placeHolderClass="#707070"
              :placeHolder="block.option?.placeHolder"
              color="#707070"
              background="#F1F2F6"
              @keyup.enter="
                () => {
                  if (!block.inputValue && block.allContext.length === 0) {
                    block.error = true
                  }
                  if (block.inputValue) {
                    block.allContext.push({
                      id: Date.now(),
                      text: block.inputValue,
                    })
                    optionInputFieldShow = false
                    block.inputValue = ''
                    block.error = false
                    if (inputRef[0]) {
                      getInputHeight(inputRef[0])
                    }
                  }
                }
              "
              @blur="
                () => {
                  if (!block.inputValue && block.allContext.length === 0) {
                    block.error = true
                  }
                  if (block.inputValue) {
                    block.allContext.push({
                      id: Date.now(),
                      text: block.inputValue,
                    })
                    optionInputFieldShow = false
                    block.inputValue = ''
                    block.error = false
                    if (inputRef[0]) {
                      getInputHeight(inputRef[0])
                    }
                  }
                }
              "
            />
          </div>
          <template v-if="block.error && block.allContext.length === 0">
            <p class="text-red-500 text-xs mt-1">Please add text</p>
          </template>
        </div>
        <div
          v-if="block.option?.numberinputField"
          class="w-full max-w-[396px] rounded-full"
        >
          <div class="flex w-full max-w-[396px] rounded-full">
            <input
              tabindex="0"
              type="number"
              class="px-4 flex-grow bg-[#F1F2F6] focus:bg-[#E3EFFF] overflow-hidden outline-none border-none rounded-l-full"
              v-model="block.inputValue"
              placeholder="0"
              @blur="
                () => {
                  optionInputFieldShow = false
                  if (!block.inputValue) {
                    block.error = true
                  } else {
                    block.error = false
                  }
                }
              "
            />
            <div
              class="px-4 py-[7px] bg-[#F1F2F6] rounded-r-full flex justify-center items-center"
            >
              <p class="text-[#707070]">KB</p>
            </div>
          </div>
          <template
            v-if="
              block.error &&
              (block.inputValue <= 0 || block.inputValue >= 2097152)
            "
          >
            <p
              v-if="block.inputValue >= 2097152"
              class="text-red-500 text-xs mt-1"
            >
              The value you choose must be less than 2097152 KB (2 GB).
            </p>
            <p
              v-else-if="block.inputValue <= 0"
              class="text-red-500 text-xs mt-1"
            >
              The value you choose must be a positive number.
            </p>
          </template>
        </div>
        <div
          v-if="block.option?.dateinputField"
          class="w-full max-w-[396px] rounded-full"
        >
          <div class="flex w-full max-w-[396px] rounded-full">
            <SourcePostListWeekMonthDatePicker
              class="date-select-box w-full"
              calender-height="!h-[35px] w-full !px-[16px] !justify-between bg-[#F1F2F6] focus:!bg-[#E3EFFF] !text-[#525252] !font-normal"
              :currentTab="currentTab"
              :selectedDate="block.inputValue || new Date()"
              :weekRange="weekRange"
              :monthYear="currentMonthYear"
              :currentTitle="currentTitle"
              :selectedFormat="'dd/MM/yyyy'"
              @update:date="
                (date: Date) => {
                  selectedFirstDay = date
                  block.inputValue = date
                  optionInputFieldShow = false
                }
              "
              @update:range="
                (range: WeekRange) => {
                  weekRange = range
                  synchronizeCalendarRange()
                }
              "
              @update:monthYear="setMonthYear"
            >
              <template v-slot:icon>
                <SharedIconHubMicrosoftCalenderIcon />
              </template>
              <template v-slot:button>
                <div
                  @click="
                    (selectedFirstDay = new Date()),
                      ((block.inputValue = new Date()),
                      (optionInputFieldShow = false))
                  "
                  :disabled="isSameDay(block.inputValue, new Date())"
                  class="text-sm pl-[74%]"
                  :class="
                    isSameDay(block.inputValue, new Date())
                      ? 'text-[#C2C2C2] cursor-not-allowed'
                      : 'text-[#525252] cursor-pointer'
                  "
                >
                  Today
                </div>
              </template></SourcePostListWeekMonthDatePicker
            >
          </div>
        </div>
        <div
          v-if="block.option?.searchableSelectField"
          class="w-full max-w-[396px]"
          ref="inputRef"
        >
          <div
            tabindex="0"
            class="w-full bg-[#F1F2F6] flex items-center flex-wrap max-w-[396px]"
            :class="inputHeight > 35 ? 'rounded-lg' : 'rounded-full'"
          >
            <SourceHubMicrosoftSettingsFileSearchableDropdown
              class="folder-wrapper"
              v-model="block.inputValue"
              :place-holder="block.action?.placeHolder"
              :showArrow="false"
              :z-index="getZIndex(7)"
              @activate="setActiveDropdown(7)"
              @mouseout="
                () => {
                  if (!block.inputValue) {
                    block.error = true
                  }
                }
              "
              @change="
                () => {
                  if (block.inputValue) {
                    block.error = false
                  }
                }
              "
            />
          </div>
        </div>
        <div
          v-if="block.option?.searchableCategorizeField"
          class="w-full max-w-[396px]"
          ref="inputRef"
        >
          <div
            tabindex="0"
            class="w-full bg-[#F1F2F6] flex items-center flex-wrap max-w-[396px]"
            :class="inputHeight > 35 ? 'rounded-lg' : 'rounded-full'"
          >
            <SourceHubMicrosoftSettingsMailRulesSearchableCategoryDropdown
              class="folder-wrapper"
              v-model="block.inputValue"
              :place-holder="block.action?.placeHolder"
              :showArrow="false"
              :z-index="getZIndex(8)"
              @activate="setActiveDropdown(8)"
              @mouseout="
                () => {
                  if (!block.inputValue) {
                    block.error = true
                  }
                }
              "
              @change="
                () => {
                  if (block.inputValue) {
                    block.error = false
                  }
                }
              "
            />
          </div>
        </div>
        <!-- Delete block button -->
        <div
          v-if="
            newOptionList.length > 1 &&
            (type === 'condition' || type === 'action')
          "
          class="h-[35px] flex items-center !ml-[11px]"
        >
          <ClientOnly>
            <fa
              class="text-2xl cursor-pointer text-[#4A71D4]"
              :icon="['fas', 'times']"
              @click="removeOptionBlock(block.id)"
            />
          </ClientOnly>
        </div>
        <div
          v-else-if="type === 'exception'"
          class="h-[35px] flex items-center !ml-[11px]"
        >
          <ClientOnly>
            <fa
              class="text-2xl cursor-pointer text-[#4A71D4]"
              :icon="['fas', 'times']"
              @click="
                newOptionList.length === 1
                  ? emit('show-exception', false)
                  : removeOptionBlock(block.id)
              "
            />
          </ClientOnly>
        </div>
      </div>
      <div
        v-if="
          newOptionList.length && newOptionList[newOptionList.length - 1].option
        "
        class="text-[#3964D0] mt-3.5 cursor-pointer"
        @click="addOptionBlock"
      >
        {{ textButton }}
      </div>
      <p
        v-if="!showException && type === 'action'"
        class="text-[#3964D0] !mt-[14px] cursor-pointer"
        @click="emit('show-exception', true)"
      >
        Add an expectation
      </p>
    </div>
  </div>
</template>

<style scoped>
.text-clamp {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
