<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

const actionCategoryOptions = computed(() =>
  JSON.parse(JSON.stringify(store.state.emails.actionCategoryOptions)),
)

const newCategory = ref({
  name: '',
  color: '',
})
const colors = [
  {
    color: '#E9C7CD',
    borderColor: '#AC4F5E',
  },
  {
    color: '#F6D1D5',
    borderColor: '#DC626D',
  },
  {
    color: '#F9DCD1',
    borderColor: '#E9835E',
  },
  {
    color: '#F1D9CC',
    borderColor: '#CA8057',
  },
  {
    color: '#FFDDB3',
    borderColor: '#FFBA66',
  },
  {
    color: '#FCEFD3',
    borderColor: '#F2C661',
  },
  {
    color: '#F5EECE',
    borderColor: '#DAC157',
  },
  {
    color: '#CAADA3',
    borderColor: '#946B5C',
  },
  {
    color: '#E5F1D3',
    borderColor: '#A4CC6C',
  },
  {
    color: '#BDD99B',
    borderColor: '#85B44C',
  },
  {
    color: '#CEF0CD',
    borderColor: '#5EC75A',
  },
  {
    color: '#9AD29A',
    borderColor: '#4DA64D',
  },
  {
    color: '#A6E9ED',
    borderColor: '#58D3DB',
  },

  {
    color: '#92D1D1',
    borderColor: '#41A3A3',
  },
  {
    color: '#94C8D4',
    borderColor: '#4496A9',
  },
  {
    color: '#D4F4FD',
    borderColor: '#63D7F7',
  },
  {
    color: '#C2D6E7',
    borderColor: '#4178A3',
  },
  {
    color: '#E7E4FB',
    borderColor: '#A79CF1',
  },
  {
    color: '#B9A3D3',
    borderColor: '#7E5CA7',
  },
  {
    color: '#FBDDF0',
    borderColor: '#EF85C8',
  },
  {
    color: '#D696C0',
    borderColor: '#AD4589',
  },
  {
    color: '#EAE8E8',
    borderColor: '#AFABAA',
  },
  {
    color: '#CECCCB',
    borderColor: '#9E9B99',
  },
  {
    color: '#D8DFE0',
    borderColor: '#B3BFC2',
  },
  {
    color: '#C4C4C4',
    borderColor: '#888888',
  },
  {
    color: 'transparent',
    borderColor: '#d1d1d1',
  },
]
const showText = ref(false)
const count = ref(0)
const selectedColor = (color: string) => {
  newCategory.value.color = color

  count.value = actionCategoryOptions.value.filter(
    (option) => option.color === color,
  ).length
  if (count.value > 0) {
    showText.value = true
  } else {
    showText.value = false
  }
}
</script>

<template>
  <div
    class="bg-white rounded-lg absolute z-[999] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1/2 max-w-[600px] p-6"
  >
    <div class="flex justify-between border-b border-[#F1F2F6] pb-3">
      <p class="text-xl font-semibold text-[#333333]">Create new category</p>
      <ClientOnly>
        <fa
          @click="store.commit('emails/SET_SHOW_NEW_CATEGORY_MODAL', false)"
          class="text-xl text-[#505050] cursor-pointer"
          :icon="['fas', 'times']"
        />
      </ClientOnly>
    </div>
    <div class="mt-4 border-b border-[#F1F2F6] pb-3">
      <div class="flex flex-col space-y-2">
        <label class="font-semibold text-[#333333]">Name</label>
        <input
          class="text-[#707070] bg-[#F1F2F6] w-full h-[35px] px-4 py-2 rounded-full border-none outline-none"
          type="text"
          placeholder="Name your category"
          v-model="newCategory.name"
        />
      </div>
      <div class="mt-4 w-full">
        <div class="w-full flex justify-between items-center">
          <label class="font-semibold text-[#333333]">Color</label>
          <div
            v-if="showText"
            class="flex space-x-2 items-center text-sm text-[#707070]"
          >
            <ClientOnly>
              <fa :icon="['fa', 'circle-info']" />
            </ClientOnly>
            <p>Selected color is used {{ count }} other times</p>
          </div>
        </div>
        <div class="flex flex-wrap mt-3">
          <div
            v-for="item in colors"
            :key="item.color"
            class="p-[5px] cursor-pointer"
            @click="selectedColor(item.color)"
          >
            <div
              class="size-8 rounded-full item hover:bg-white hover:p-0.5 hover:border-2 hover:border-[#F5F5F5]"
              :class="
                newCategory.color === item.color
                  ? 'bg-white p-0.5 border-2 border-[#4A71D4]'
                  : ''
              "
            >
              <div
                class="w-full h-full text-[#00373A] flex justify-center items-center rounded-full"
                :style="{
                  backgroundColor: item.color,
                  borderWidth: '1px',
                  borderColor: item.borderColor,
                }"
              >
                A
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="w-full flex justify-end space-x-4 mt-4">
      <button
        :disabled="!newCategory.name || !newCategory.color"
        class="flex justify-center items-center w-[104px] h-[35px] rounded-full bg-[#4A71D4] text-[#FFFFFF]"
        :class="
          !newCategory.name || !newCategory.color ? 'cursor-not-allowed' : ''
        "
        @click="
          store.commit('emails/SET_NEW_ACTION_CATEGORY_OPTION', newCategory),
            store.commit('emails/SET_SHOW_NEW_CATEGORY_MODAL', false)
        "
      >
        Save
      </button>
      <button
        class="flex justify-center items-center w-[104px] h-[35px] rounded-full border border-[#4A71D4] text-[#4A71D4]"
        @click="store.commit('emails/SET_SHOW_NEW_CATEGORY_MODAL', false)"
      >
        Cancel
      </button>
    </div>
  </div>
</template>

<style scoped></style>
