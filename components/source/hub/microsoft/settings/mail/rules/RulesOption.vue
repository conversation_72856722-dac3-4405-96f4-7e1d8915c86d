<script setup lang="ts">
import draggable from 'vuedraggable'
import { useStore } from 'vuex'
const store = useStore()
const drag = ref(false)
const generatedRules = computed({
  get() {
    return store.state.emails.generatedRules
  },
  set(value) {
    store.commit('emails/SET_ORDERED_GENERATED_RULES', value)
  },
})
const anyRuleExpanded = computed(() => {
  return generatedRules.value.some((rule: any) => rule.expand)
})
const moveUp = (index: number) => {
  store.commit('emails/MOVE_RULE_UP', index)
}
const moveDown = (index: number) => {
  store.commit('emails/MOVE_RULE_DOWN', index)
}
</script>

<template>
  <div class="pt-6 pb-4">
    <div class="flex justify-end">
      <button
        class="w-[174px] h-9 rounded-full flex justify-center items-center border border-[#C2C2C2] text-[#525252] font-semibold"
        @click="
          anyRuleExpanded
            ? store.commit('emails/SET_GENERATED_RULES_ALL_EXPAND_STATE', false)
            : store.commit('emails/SET_GENERATED_RULES_ALL_EXPAND_STATE', true)
        "
      >
        {{ anyRuleExpanded ? 'Hide all description' : 'Show all description' }}
      </button>
    </div>
    <draggable
      v-model="generatedRules"
      class="flex flex-col space-y-4 mt-4"
      ghost-class="ghost-class"
      item-key="id"
      group="mailRules"
      handle=".handle"
      animation="200"
      @start="drag = true"
      @end="drag = false"
    >
      <template #item="{ element, index }">
        <div
          class="pl-4 pr-6 py-8 border border-[#E3E3E3] rounded transition-transform"
        >
          <div class="flex items-start justify-between">
            <div class="flex space-x-4">
              <img
                class="w-[26px] h-[26px] handle cursor-move"
                src="/images/microsoft-mail-settings/drag-indicator.svg"
                alt="drag-indicator"
              />
              <div class="flex flex-col mt-0.5">
                <p class="text-[#333333] font-semibold">
                  {{ element.ruleName }}
                </p>
              </div>
            </div>
            <div class="flex items-center">
              <InputsToggleInput
                :id="element.id"
                :select="element.toggleButton"
                uncheckedLabelBgColor="#F1F1F2"
              />
              <!-- @toggle-select="changeToggleValue" -->
              <button
                :disabled="index === 0"
                class="ml-6"
                :class="[index === 0 ? 'opacity-50' : '']"
                @click="moveUp(index)"
              >
                <SharedIconHubMicrosoftButtonMoveUp />
              </button>
              <button
                :disabled="index === generatedRules.length - 1"
                :class="[
                  index === generatedRules.length - 1 ? 'opacity-50' : '',
                ]"
                @click="moveDown(index)"
              >
                <SharedIconHubMicrosoftButtonMoveDown />
              </button>
              <img
                class="w-8 h-8 cursor-pointer"
                src="/images/microsoft-mail-settings/button-3dot.svg"
                alt="button-3dot"
              />
              <img
                class="w-8 h-8 cursor-pointer"
                src="/images/microsoft-mail-settings/down-arrow.svg"
                alt="down-arrow"
                @click="
                  store.commit(
                    'emails/SET_GENERATED_RULES_EXPAND_STATE',
                    element.id,
                  )
                "
              />
            </div>
          </div>
          <div v-if="element.expand" class="mt-8">
            <p class="text-[#333333] max-w-[513px] !ml-[42px]">
              {{ element.description }}
            </p>
            <div class="w-full flex justify-end space-x-8 mt-[22px]">
              <div
                class="flex space-x-2.5 items-center cursor-pointer"
                @click="
                  store.commit('emails/SET_SHOW_EDIT_RULE', true),
                    store.commit('emails/SET_NEW_RULE_DATA_ON_EDIT', element.id)
                "
              >
                <SharedIconHubMicrosoftEditIcon color="#707070" />
                <p class="text-[#525252] text-sm font-semibold">Edit</p>
              </div>
              <div
                class="flex space-x-2.5 items-center cursor-pointer"
                @click="
                  store.commit(
                    'emails/DELETE_NEW_RULE_FROM_OPTIONS',
                    element.id,
                  )
                "
              >
                <SharedIconHubEmailsDeleteIcon />
                <p class="text-[#525252] text-sm font-semibold">Delete</p>
              </div>
            </div>
          </div>
        </div>
      </template>
    </draggable>
  </div>
</template>

<style scoped>
.handle {
  touch-action: none;
}
.ghost-class {
  @apply opacity-40 bg-gray-100;
}
</style>
