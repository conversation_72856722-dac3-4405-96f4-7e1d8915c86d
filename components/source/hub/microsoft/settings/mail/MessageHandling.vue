<script setup lang="ts">
import { useStore } from 'vuex'
import type { MessageHandling } from '~/types/hubEmailsSettings'

const emit = defineEmits<{
  changeMessageHandling: [messageHandling: MessageHandling]
}>()

interface Option {
  label: string
  value: string
  description: string
}

const markAsReadOptions = ref<Option[]>([
  {
    label: "Mark displayed items as read as soon as they're selected",
    value: 'selected',
    description: '',
  },
  {
    label: 'Mark displayed items as read after 5 seconds',
    value: 'after_5_seconds',
    description: '',
  },
  {
    label: 'Mark displayed items as read when the selection changes',
    value: 'selection_changes',
    description: '',
  },
  {
    label: "Don't automatically mark items as read",
    value: 'do_not_automatically',
    description: '',
  },
])

const readReceiptOptions = ref<Option[]>([
  {
    label: 'Ask me before sending a response',
    value: 'ask_me',
    description: '',
  },
  {
    label: 'Always send a response',
    value: 'always_send',
    description: '',
  },
  {
    label: 'Never send a response',
    value: 'never_send',
    description: '',
  },
])

const translateMessageOptions = ref<Option[]>([
  {
    label: 'Always translate',
    value: 'always_translate',
    description: '',
  },
  {
    label: 'Ask me before translating',
    value: 'ask_me_before_translating',
    description: '',
  },
  {
    label: 'Never translate',
    value: 'never_translate',
    description: '',
  },
])

const store = useStore()

const messageHandling = ref<MessageHandling>({
  emptyDeletedItemsFolder: false,
  markAsRead: 'selection_changes',
  alwaysKeepUnread: true,
  readReceipt: 'ask_me',
  showShoppingLinks: false,
  confirmAction: true,
  translateMessage: 'ask_me_before_translating',
  translateLanguage: null,
  noTranslateLanguages: [],
})

const { uniqueLanguages } = useWorldLanguages()
const translateLanguage = ref<UniqueLanguage | null>(null)
const isTranslateLanguageOpen = ref(false)
const handleChangeTranslateLanguage = (language: UniqueLanguage) => {
  messageHandling.value.translateLanguage = language.name
  const isAlreadyAdded = messageHandling.value.noTranslateLanguages.includes(
    language.name,
  )
  if (isAlreadyAdded) return
  messageHandling.value.noTranslateLanguages.push(language.name)
}

const noTranslateLanguagesOption = computed(() => {
  return uniqueLanguages.value.filter(
    (item) => !messageHandling.value.noTranslateLanguages.includes(item.name),
  )
})
const noTranslateLanguage = ref<UniqueLanguage | null>(null)
const isNoTranslateLanguageOpen = ref(false)
const handleAddToNoTranslateLanguages = () => {
  if (!noTranslateLanguage.value) return
  messageHandling.value.noTranslateLanguages.push(
    noTranslateLanguage.value.name,
  )
  noTranslateLanguage.value = null
}
const removeNoTranslateLanguage = (language: string) => {
  messageHandling.value.noTranslateLanguages =
    messageHandling.value.noTranslateLanguages.filter(
      (item) => item !== language,
    )
}

watch(
  messageHandling,
  () => {
    emit('changeMessageHandling', messageHandling.value)
  },
  { deep: true },
)

const initMessageHandling = () => {
  const savedMessageHandling = store.state.emails
    .messageHandling as MessageHandling
  messageHandling.value = JSON.parse(JSON.stringify(savedMessageHandling))
  translateLanguage.value =
    uniqueLanguages.value.find(
      (item) => item.name === messageHandling.value.translateLanguage,
    ) || null
  noTranslateLanguage.value = null
}

defineExpose({
  initMessageHandling,
})
onMounted(async () => {
  await nextTick()
  initMessageHandling()
})
</script>

<template>
  <div class="w-full h-full overflow-hidden flex flex-col">
    <div
      class="px-6 py-4 text-xl leading-[26px] font-semibold text-[#333333] border-b border-[#F1F2F6]"
    >
      <p>Message handling</p>
    </div>
    <div class="px-6 mt-3.5 pb-4 flex-grow overflow-y-auto">
      <div class="space-y-3.5 pb-4">
        <p class="text-base leading-[21px] text-[#333333] font-semibold">
          Message options
        </p>
        <p class="text-base leading-[21px] text-[#707070]">When signing out</p>
        <InputsCheckBoxWithLabel
          v-model="messageHandling.emptyDeletedItemsFolder"
          checkColor="#4A71D4"
          label="Empty my deleted items folder"
          labelClass="text-base text-[#333333] leading-[21px]"
          class="w-min whitespace-nowrap"
        />
      </div>

      <div class="pt-3.5 pb-4 border-t border-[#F1F2F6] flex flex-col">
        <p class="text-base leading-[21px] text-[#333333] font-semibold">
          Mark as read
        </p>
        <InputsRadioOptionGroup
          v-model="messageHandling.markAsRead"
          :options="markAsReadOptions"
          labelClass="font-normal"
          class="!space-y-3.5 pt-3.5"
        />
        <div class="flex items-center space-x-2 mt-[22px]">
          <p class="text-[#333333] font-semibold">In the unread filter</p>
          <InputsCheckBoxWithLabel
            v-model="messageHandling.alwaysKeepUnread"
            checkColor="#4A71D4"
            label="Always keep items unread unless I explicitly mark them as read"
            labelClass="text-base text-[#333333] leading-[21px]"
            class="w-min whitespace-nowrap"
          />
        </div>
      </div>

      <div
        class="pt-3.5 pb-4 border-t border-[#F1F2F6] flex flex-col space-y-3.5"
      >
        <p class="text-base leading-[21px] text-[#333333] font-semibold">
          Read receipts
        </p>
        <p class="text-base leading-[21px] text-[#707070]">
          Choose how to respond to requests for read receipts.
        </p>
        <InputsRadioOptionGroup
          v-model="messageHandling.readReceipt"
          :options="readReceiptOptions"
          labelClass="font-normal"
          class="!space-y-3.5"
        />
      </div>

      <div class="space-y-3.5 pt-3.5 pb-4 border-t border-[#F1F2F6]">
        <p class="text-base leading-[21px] text-[#333333] font-semibold">
          Shopping
        </p>
        <InputsCheckBoxWithLabel
          v-model="messageHandling.showShoppingLinks"
          checkColor="#4A71D4"
          label="For shopping-related messages, show a sender logo and relevant links in the message header."
          labelClass="text-base text-[#333333] leading-[21px]"
          class="w-min whitespace-nowrap"
        />
      </div>

      <div class="space-y-3.5 pt-3.5 pb-4 border-t border-[#F1F2F6]">
        <p class="text-base leading-[21px] text-[#333333] font-semibold">
          Action confirmations
        </p>
        <p class="text-base leading-[21px] text-[#707070]">
          When I move, delete, or archive a message
        </p>
        <InputsCheckBoxWithLabel
          v-model="messageHandling.confirmAction"
          checkColor="#4A71D4"
          label="Confirm that the action was successful"
          labelClass="text-base text-[#333333] leading-[21px]"
          class="w-min whitespace-nowrap"
        />
      </div>

      <div class="space-y-3.5 pt-3.5 pb-4 border-t border-[#F1F2F6]">
        <p class="text-base leading-[21px] text-[#333333] font-semibold">
          Translation
        </p>
        <p class="text-base leading-[21px] text-[#707070]">
          Choose how to handle messages received in other languages.
        </p>
        <InputsRadioOptionGroup
          v-model="messageHandling.translateMessage"
          :options="translateMessageOptions"
          labelClass="font-normal"
          class="!space-y-3.5"
        />
      </div>
      <div class="flex flex-col space-y-3.5">
        <p class="text-base leading-[21px] text-[#707070]">
          Translate messages into this language
        </p>
        <BaseDropsDown
          class="rounded-full language-dropdown"
          v-model="translateLanguage"
          :options="uniqueLanguages"
          idKey="id"
          labelKey="name"
          placeholder="Select language"
          :menuWidth="396"
          :menuHeight="35"
          :dropdownWidth="300"
          :dropdownMaxHeight="2000"
          :menuBgColor="isTranslateLanguageOpen ? '#E3EFFF' : '#F1F2F6'"
          :menuTextColor="isTranslateLanguageOpen ? '#333333' : '#707070'"
          dropsdownTextColor="#333333"
          scrollbarTrackColor="#a1cdff50"
          scrollbarThumbColor="#a1cdff"
          hoverColor="#4A71D4"
          dropdownPosition="fixed top-0 xl:left-[760px] left-[500px] !h-full"
          hoverTextColor="#FFFFFF"
          :showSelectedIcon="true"
          @open="(isOpen: boolean) => (isTranslateLanguageOpen = isOpen)"
          @change="handleChangeTranslateLanguage"
        />
      </div>
      <div class="flex flex-col space-y-3.5 mt-4">
        <p class="text-base leading-[21px] text-[#707070]">
          Don't translate the following languages
        </p>
        <div class="flex items-center space-x-2">
          <BaseDropsDown
            class="rounded-full language-dropdown"
            v-model="noTranslateLanguage"
            :options="noTranslateLanguagesOption"
            idKey="id"
            labelKey="name"
            placeholder="Select language"
            :menuWidth="396"
            :menuHeight="35"
            :dropdownWidth="300"
            :dropdownMaxHeight="2000"
            :menuBgColor="isNoTranslateLanguageOpen ? '#E3EFFF' : '#F1F2F6'"
            :menuTextColor="isNoTranslateLanguageOpen ? '#333333' : '#707070'"
            dropsdownTextColor="#333333"
            scrollbarTrackColor="#a1cdff50"
            scrollbarThumbColor="#a1cdff"
            hoverColor="#4A71D4"
            dropdownPosition="fixed top-0 xl:left-[760px] left-[500px] !h-full"
            hoverTextColor="#FFFFFF"
            :showSelectedIcon="true"
            @open="(isOpen: boolean) => (isNoTranslateLanguageOpen = isOpen)"
          />
          <button
            v-if="noTranslateLanguage"
            class="h-[35px] w-[104px] bg-[#4A71D4] text-[#fff] rounded-full text-base leading-[21px] font-semibold"
            @click="handleAddToNoTranslateLanguages"
          >
            Add
          </button>
        </div>
      </div>
      <div class="mt-5">
        <div
          v-for="language in messageHandling.noTranslateLanguages"
          :key="language"
          class="h-12 rounded flex items-center justify-between px-4 py-[15px] group border-b-2 border-[#F1F2F6]"
        >
          <p class="text-[#333333] text-base leading-[21px]">
            {{ language }}
          </p>
          <button
            :disabled="translateLanguage?.name === language"
            class="rounded-full p-2 hover:bg-[#E3EFFF]"
            :class="[translateLanguage?.name === language ? 'opacity-50 ' : '']"
            @click="removeNoTranslateLanguage(language)"
          >
            <SharedIconHubEmailsDeleteIcon color="#333333" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
