<script setup lang="ts">
import { useStore } from 'vuex'
import Promotions from '~/components/shared/icon/hub/emails/Promotions.vue'
import SocialIcon from '~/components/shared/icon/hub/emails/SocialIcon.vue'
import Updates from '~/components/shared/icon/hub/emails/Updates.vue'
import Forums from '~/components/shared/icon/hub/emails/Forums.vue'

const store = useStore()

interface MoveToTabMenuOption {
  id: number
  image: string | Component
  title: string
  function: (payload: MouseEvent) => void
}

const moveToTabMenuOptions = ref<MoveToTabMenuOption[]>([
  {
    id: 1,
    image: markRaw(Promotions),
    title: 'Promotions',
    function: (payload: MouseEvent) => {},
  },
  {
    id: 2,
    image: markRaw(SocialIcon),
    title: 'Social',
    function: () => {},
  },
  {
    id: 3,
    image: markRaw(Updates),
    title: 'Updates',
    function: () => {},
  },
  {
    id: 4,
    image: markRaw(Forums),
    title: 'Forums',
    function: () => {},
  },
])
</script>

<template>
  <div
    class="flex flex-col select-extend-menu-box space-y-3.5 bg-white py-1 absolute custom-scroll"
  >
    <div class="flex flex-col">
      <div
        v-for="moveToTabMenuOption in moveToTabMenuOptions"
        :key="moveToTabMenuOption.id"
        class="relative flex justify-start items-center !space-x-2.5 px-3.5 py-[5px] hover:bg-[#F1F2F6] cursor-pointer"
        @click.stop="moveToTabMenuOption.function"
      >
        <div class="flex items-center !space-x-2">
          <component :is="moveToTabMenuOption.image" color="#707070" />
        </div>
        <p class="text-[#525252]">
          {{ moveToTabMenuOption.title }}
        </p>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.select-menu-box {
  min-width: 143px !important;
  max-width: 143px !important;
  height: fit-content;
  z-index: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 8px #2228313d;
  border-radius: 8px;
}
.select-extend-menu-box {
  min-width: 143px !important;
  max-width: 143px !important;
  height: fit-content;
  z-index: 1000;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 8px #2228313d;
  border-radius: 8px;
}
</style>
