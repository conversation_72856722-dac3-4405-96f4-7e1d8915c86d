<script setup lang="ts">
import useVuelidate from '@vuelidate/core'
import { required } from '@vuelidate/validators'
import {
  addDays,
  format,
  isBefore,
  isSameDay,
  isValid,
  parse,
  startOfDay,
} from 'date-fns'
import debounce from 'lodash.debounce'
import { useStore } from 'vuex'

const store = useStore()
const { saToast, toast } = useCustomToast()
const snoozeDateTimeModal = computed(
  () => store.state.emails.snoozeDateTimeModal,
)

const closeModal = () => {
  store.commit('emails/SET_SNOOZE_DATE_TIME_MODAL', false)
}

const cancel = () => {
  closeModal()
}

const currentDate = ref<Date | null>(null)

// Form fields
const form = ref({
  dateInputValue: '',
  time: '',
})

// Custom validation rules
const customDateValidator = (value: string) => {
  const error = validateDate(value)
  return !error
}

const customTimeValidator = (value: string) => {
  const error = validateTime(value)
  return !error
}

const rules = {
  dateInputValue: { required, customDateValidator },
  time: { required, customTimeValidator },
}
const v$ = useVuelidate(rules, form)

const formatDateForDisplay = (date: Date | null) => {
  if (!date) return ''
  return format(date, 'MMM d, yyyy') // e.g. "Nov 30, 2024"
}

const parseUserDate = (dateString: string): Date | null => {
  if (!dateString.trim()) return null

  const formats = ['MMM d, yyyy', 'MMMM d, yyyy', 'MM/dd/yyyy', 'yyyy-MM-dd']

  for (const fmt of formats) {
    const parsed = parse(dateString, fmt, new Date())
    if (isValid(parsed)) return parsed
  }

  return null
}

const validateDate = (dateString: string) => {
  if (!dateString.trim()) {
    return 'Date is required'
  }

  const date = parseUserDate(dateString)
  if (!date) {
    return 'Please enter a valid date (e.g., Nov 30, 2024)'
  }

  const today = startOfDay(new Date())
  const picked = startOfDay(date)

  if (isBefore(picked, today)) {
    return 'Date cannot be in the past'
  }

  return ''
}

const validateTime = (timeString: string) => {
  if (!timeString.trim()) {
    return 'Time is required'
  }

  const timeRegex =
    /^(0?[1-9]|1[0-2]):[0-5][0-9]\s?(AM|PM|am|pm)$|^([01]?[0-9]|2[0-3]):[0-5][0-9]$/

  if (!timeRegex.test(timeString.trim())) {
    return 'Please enter a valid time (e.g., 8:00 AM or 20:00)'
  }

  return ''
}

const dateError = computed(() => {
  return validateDate(form.value.dateInputValue)
})

const timeError = computed(() => {
  return validateTime(form.value.time)
})

const save = () => {
  v$.value.$touch()
  if (!v$.value.$invalid) {
    console.log(
      'Saving date:',
      form.value.dateInputValue,
      'time:',
      form.value.time,
    )
    toast.clear()
    saToast({
      message: 'Conversation snoozed.',
      onUndo: undoSnoozeConversation,
    })
    closeModal()
  }
}
const undoSnoozeConversation = () => {
  toast.clear()
  saToast({
    message: 'Action undone.',
  })
}

const handleCalendarDateChange = (date: Date | null) => {
  if (date && isSameDay(date, new Date())) {
    const now = new Date()
    const currentHour = now.getHours()
    if (currentHour >= 8) {
      currentDate.value = addDays(now, 1)
    } else {
      currentDate.value = now
    }
  } else {
    currentDate.value = date
  }
  form.value.time = '8:00 AM'
  form.value.dateInputValue = formatDateForDisplay(currentDate.value)

  v$.value.dateInputValue.$reset()
  v$.value.time.$reset()
}

const handleDateInput = debounce(() => {
  v$.value.dateInputValue.$touch()
  const parsed = parseUserDate(form.value.dateInputValue)

  if (parsed && !validateDate(form.value.dateInputValue)) {
    currentDate.value = parsed
    form.value.dateInputValue = formatDateForDisplay(parsed)
  }
}, 1000)

onMounted(() => {
  const now = new Date()
  const currentHour = now.getHours()

  if (currentHour >= 8) {
    currentDate.value = addDays(now, 1)
  } else {
    currentDate.value = now
  }

  form.value.time = '8:00 AM'
  form.value.dateInputValue = formatDateForDisplay(currentDate.value)
})
</script>

<template>
  <Transition name="page" mode="out-in">
    <div v-if="snoozeDateTimeModal">
      <div
        class="bg-white w-full max-w-[678px] rounded-2xl fixed z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
      >
        <div class="border-b-[2px] border-[#F1F2F6]">
          <div class="flex justify-between items-center px-6 py-3.5">
            <p class="text-lg font-semibold text-[#505050]">Pick date & time</p>
            <SharedIconHubEmailsCrossIcon
              @click="closeModal"
              class="w-4 h-4 cursor-pointer"
            />
          </div>
        </div>
        <div
          class="border-b-[2px] border-[#F1F2F6] px-6 pb-3 pt-[18px] text-[#525252] text-base grid grid-cols-[330px_1fr] gap-6"
        >
          <div class="w-full">
            <VDatePicker
              :model-value="currentDate"
              :min-date="new Date()"
              borderless
              @update:model-value="handleCalendarDateChange"
            />
          </div>
          <div class="flex flex-col space-y-4 w-full">
            <div class="w-full">
              <input
                type="text"
                v-model="form.dateInputValue"
                @blur="v$.dateInputValue.$touch()"
                @input="handleDateInput"
                placeholder="e.g., Nov 30, 2024"
                class="w-full outline-none rounded-full text-[#525252] bg-[#F1F2F6] border-none px-6 py-1.5 text-lg leading-6 focus:ring-[1px]"
              />
              <p
                v-if="v$.dateInputValue.$error || dateError"
                class="text-red-500 text-sm mt-1 px-2"
              >
                {{ dateError }}
              </p>
            </div>

            <div class="w-full">
              <input
                type="text"
                v-model="form.time"
                @blur="v$.time.$touch()"
                @input="v$.time.$touch()"
                placeholder="e.g., 8:00 AM"
                class="w-full outline-none rounded-full text-[#525252] bg-[#F1F2F6] border-none px-6 py-1.5 text-lg leading-6 focus:ring-[1px]"
              />
              <p
                v-if="v$.time.$error || timeError"
                class="text-red-500 text-sm mt-1 px-2"
              >
                {{ timeError }}
              </p>
            </div>
          </div>
        </div>

        <div class="flex justify-end items-center space-x-2 px-6 py-3.5">
          <button
            @click="cancel"
            class="rounded-full font-semibold text-[#4A71D4] border-[1px] border-[#4A71D4] min-w-[100px] h-[35px] px-6"
          >
            Cancel
          </button>

          <button
            @click="save"
            :disabled="v$.$invalid || !!dateError || !!timeError"
            class="rounded-full font-semibold text-[#FFFFFF] bg-[#4A71D4] min-w-[100px] h-[35px] px-6 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Save
          </button>
        </div>
      </div>
      <div @click.stop="closeModal" class="fixed inset-0 transition-opacity">
        <div class="absolute inset-0 bg-[#323744] opacity-50"></div>
      </div>
    </div>
  </Transition>
</template>

<style scoped>
:deep(.vc-day) {
  width: 46px;
}
:deep(.vc-monthly .is-not-in-month *) {
  opacity: 0.5;
}
</style>
