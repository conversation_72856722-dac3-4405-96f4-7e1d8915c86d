<template>
  <div class="">
    <NuxtLink
      v-if="item.showInLabelList"
      :to="
        `/source/hub/emails/${route.params.slug}/${route.params.categories}/${item.name}` ||
        '#'
      "
      class="nuxt-link whitespace-nowrap flex justify-between px-4 py-2 items-center rounded-full"
      @click.native="
        store.commit('emails/RESET_SHOW_COMPOSE_SECTION'),
          store.commit('emails/SHOW_HIDE_SELECTED_LABEL', { id: item.id })
      "
    >
      <div class="menu-container flex !space-x-[18px] items-center">
        <component v-if="item.image" :is="item.image"></component>
        <p>{{ item.name }}</p>
      </div>
      <p v-if="item.unread > 0" class="text-sm">
        {{ item.unread }}
      </p>
    </NuxtLink>

    <!-- Recursively render children -->
    <div
      v-if="
        item.children &&
        item.children.length > 0 &&
        item.showInLabelList &&
        item.selected
      "
      class="ml-4 mt-2"
    >
      <SourceHubEmailsLabelItem
        v-for="child in item.children"
        :key="child.id"
        :item="child"
      />
    </div>
  </div>
</template>

<script setup>
import { useStore } from 'vuex'
const props = defineProps({
  item: Object,
})
const store = useStore()
const route = useRoute()
</script>

<style lang="scss" scoped>
.router-link-exact-active,
.router-link-active {
  @apply bg-blue-200 text-white;
}
</style>
