<script setup lang="ts">
import { useStore } from 'vuex'
import { format, addDays, setHours, setMinutes, nextSunday } from 'date-fns'

const store = useStore()
const { saToast, toast } = useCustomToast()
const emit = defineEmits<{
  (e: 'closeSnooze'): void
}>()

interface ExtendMenu {
  id: number
  title: string
  time: string
  date: Date
}

function formatDate(date: Date) {
  return format(date, 'EEE, h:mm a')
}

const now = new Date()
const laterToday = setHours(setMinutes(now, 0), 18) // Today 6 PM
const tomorrow = setHours(setMinutes(addDays(now, 1), 0), 8) // Tomorrow 8 AM
const laterThisWeek = setHours(setMinutes(addDays(now, 3), 0), 8) // +3 days 8 AM
const thisWeekend = setHours(setMinutes(nextSunday(now), 0), 8) // Sunday 8 AM
const nextWeek = setHours(setMinutes(addDays(now, 7), 0), 8) // +7 days 8 AM

const extendMenu = ref<ExtendMenu[]>([
  {
    id: 1,
    title: 'Later today',
    time: formatDate(laterToday),
    date: laterToday,
  },
  { id: 2, title: 'Tomorrow', time: formatDate(tomorrow), date: tomorrow },
  {
    id: 3,
    title: 'Later this week',
    time: formatDate(laterThisWeek),
    date: laterThisWeek,
  },
  {
    id: 4,
    title: 'This weekend',
    time: formatDate(thisWeekend),
    date: thisWeekend,
  },
  { id: 5, title: 'Next week', time: formatDate(nextWeek), date: nextWeek },
])

const handleSnooze = (item: ExtendMenu) => {
  console.log('Snooze until:', item)
  emit('closeSnooze')
  toast.clear()
  saToast({
    message: 'Conversation snoozed.',
    onUndo: undoSnoozeConversation,
  })
}
const undoSnoozeConversation = () => {
  toast.clear()
  saToast({
    message: 'Action undone.',
  })
}
const handleOpenDateTimeModal = () => {
  emit('closeSnooze')
  store.commit('emails/SET_SNOOZE_DATE_TIME_MODAL', true)
}
</script>

<template>
  <div
    class="flex flex-col bg-white pb-1 absolute top-10 left-0 select-extend-menu-box"
    @click.stop=""
  >
    <p
      class="px-4 text-[#525252] text-base font-semibold leading-[21px] py-[7px]"
    >
      Snooze until...
    </p>

    <ul class="flex flex-col border-b border-[#C2C2C2]">
      <li
        v-for="item in extendMenu"
        :key="item.id"
        class="flex justify-between items-center px-4 py-[7px] text-[#525252] text-base leading-[21px] hover:bg-[#F1F2F6] cursor-pointer"
        @click="handleSnooze(item)"
      >
        <p class="text-[#525252]">{{ item.title }}</p>
        <span class="text-[#707070]">{{ item.time }}</span>
      </li>
    </ul>

    <button
      class="px-4 bg-[#F1F2F6] text-[#525252] text-base py-[7px] !mt-0.5 flex items-center space-x-2"
      @click="handleOpenDateTimeModal"
    >
      <SharedIconHubCalendar /> <span>Pick date & time</span>
    </button>
  </div>
</template>

<style lang="scss" scoped>
.select-extend-menu-box {
  min-width: 256px;
  max-width: 256px;
  height: fit-content;
  z-index: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 8px #2228313d;
  border-radius: 8px;
}
</style>
