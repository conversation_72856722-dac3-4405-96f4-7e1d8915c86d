<script setup lang="ts">
import { useStore } from 'vuex'

defineProps({
  showAddStar: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['hide-labelasmenu'])

const store = useStore()
const router = useRouter()

interface LabelAsOptions {
  id: number
  title: string
  checked: boolean
}

const searchText = ref<string>('')
const labelAsOptions = computed(() => store.state.emails.labelAsOptions)

const searchLabels = computed(() => {
  return labelAsOptions.value.filter((labelAsOption: LabelAsOptions) =>
    labelAsOption.title
      .toLowerCase()
      .startsWith(searchText.value.toLowerCase()),
  )
})
const tempLabelAsOptions = ref(JSON.parse(JSON.stringify(labelAsOptions.value)))
const newCheckedId = ref([])
const hideShowApply = ref(false)
const toggleOption = (option: LabelAsOptions) => {
  // const tempLabelAsOptions = JSON.parse(JSON.stringify(labelAsOptions.value))
  option.checked = !option.checked
  if (option.checked) {
    newCheckedId.value.push(option.id)
  } else {
    const find = newCheckedId.value.some((item) => item === option.id)
    if (find) {
      const index = newCheckedId.value.findIndex((item) => item === option.id)
      if (index) {
        newCheckedId.value.splice(index, 1)
      }
    }
  }
  hideShowApply.value = shallowEqual(
    JSON.stringify(tempLabelAsOptions.value),
    JSON.stringify(labelAsOptions.value),
  )
}
const toggleOptionAndMenuClose = (option: LabelAsOptions) => {
  // const tempLabelAsOptions = JSON.parse(JSON.stringify(labelAsOptions.value))
  option.checked = !option.checked
  if (option.checked) {
    newCheckedId.value.push(option.id)
  } else {
    const find = newCheckedId.value.some((item) => item === option.id)
    if (find) {
      const index = newCheckedId.value.findIndex((item) => item === option.id)
      if (index) {
        newCheckedId.value.splice(index, 1)
      }
    }
  }
  hideShowApply.value = shallowEqual(
    JSON.stringify(tempLabelAsOptions.value),
    JSON.stringify(labelAsOptions.value),
  )
  applyChanges()
}
const shallowEqual = (obj1, obj2) => {
  if (obj1 === obj2) {
    return false
  } else {
    return true
  }
}
const applyChanges = () => {
  tempLabelAsOptions.value = tempLabelAsOptions.value.map((item) => ({
    ...item,
    checked: newCheckedId.value.includes(item.id),
  }))
  hideShowApply.value = false
  console.log('Applied changes:', tempLabelAsOptions.value)
  store.commit(
    'emails/SET_UPDATED_LABEL_AS_MENU_OPTIONS',
    JSON.parse(JSON.stringify(tempLabelAsOptions.value)),
  )
  console.log('Applied changes:', tempLabelAsOptions.value)
  emit('hide-labelasmenu', false)
}
const isSolid = ref(false)
const handleAddStar = () => {
  isSolid.value = !isSolid.value
}
</script>

<template>
  <div
    class="select-extend-menu-box !max-h-[291px] flex flex-col space-y-1 bg-white py-1 absolute custom-scroll"
  >
    <div class="px-3.5 py-[5px]">
      <p class="text-[#525252]">Label as:</p>
    </div>
    <div class="px-3.5 h-[36px] !mt-0">
      <div
        class="flex space-x-3 justify-between items-center border-b-2 border-[#4A71D4] h-[36px] pb-1.5"
      >
        <input
          class="flex-grow outline-none border-none"
          type="text"
          v-model="searchText"
        />
        <ClientOnly>
          <fa class="text-[#707070]" :icon="['fa', 'magnifying-glass']" />
        </ClientOnly>
      </div>
    </div>
    <ul class="flex flex-col">
      <li
        v-for="labelAsOption in searchLabels"
        :key="labelAsOption.id"
        class="flex justify-start items-center !space-x-2.5 px-3.5 py-[5px] hover:bg-[#F1F2F6] cursor-pointer"
        :class="
          labelAsOption.id === searchLabels[searchLabels.length - 1].id
            ? 'border-b border-[#C2C2C2]'
            : ''
        "
        :for="`${labelAsOption.id}_${labelAsOption.title}`"
        @click.stop="toggleOptionAndMenuClose(labelAsOption)"
      >
        <!-- @update:modelValue="() => toggleOption(option)" -->
        <InputsCheckBoxInput
          :id="`${labelAsOption.id}_${labelAsOption.title}`"
          checkColor="#1a1a1a"
          borderColor="#707070"
          :modelValue="labelAsOption.checked"
          class="label-as-checkbox-input"
          @click.stop=""
          @update:modelValue="() => toggleOption(labelAsOption)"
        />
        <div
          :for="`${labelAsOption.id}_${labelAsOption.title}`"
          class="flex items-center !space-x-2"
        >
          <p class="text-[#525252]">{{ labelAsOption.title }}</p>
        </div>
      </li>
    </ul>
    <div
      v-if="showAddStar"
      class="!mt-0 w-full px-3.5 py-[5px] hover:bg-[#F1F2F6] cursor-pointer border-b border-[#C2C2C2] flex items-center space-x-2.5"
      @click.stop="handleAddStar"
    >
      <SharedIconStar
        :isSolid="isSolid"
        class="text-[#525252]"
        :class="isSolid ? 'text-yellow-500' : ''"
      />
      <span>Add star</span>
    </div>
    <ul v-if="!hideShowApply" class="flex flex-col px-3.5">
      <li class="py-[5px]">
        <p
          class="text-[#525252]"
          @click="
            searchText ? '' : store.commit('emails/SET_NEW_LABEL_MODAL', true),
              emit('hide-labelasmenu', false)
          "
        >
          {{ searchText ? `"${searchText}"` : '' }} Create new
        </p>
      </li>
      <li
        class="py-[5px] cursor-pointer"
        @click="router.push('/source/hub/emails/settings/labels')"
      >
        <p class="text-[#525252]">Manage Labels</p>
      </li>
    </ul>
    <ul v-else class="flex flex-col px-3.5">
      <li class="py-[5px]" @click="applyChanges">
        <p class="text-[#525252]">Apply</p>
      </li>
    </ul>
  </div>
</template>

<style lang="scss" scoped>
.select-menu-box {
  min-width: 176px;
  max-width: 176px;
  max-height: 291px;
  z-index: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
.select-extend-menu-box {
  min-width: 240px;
  max-width: 240px;
  max-height: 291px;
  z-index: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
.single-message-label-as-menu {
  box-shadow: 0px 0px 8px #2228313d !important;
}
// @media (max-height: 819px) and (min-width: 1536px) {
//   .header-menu {
//     top: 0%;
//     right: 100%;
//     left: auto;
//   }
// }
// @media (max-height: 719px) and (min-width: 1536px) {
//   .header-menu {
//     top: 0%;
//     right: 100%;
//     left: auto;
//   }
// }
// @media (max-height: 919px) and (max-width: 1535px) {
//   .header-menu {
//     top: 0%;
//     right: 100%;
//     left: auto;
//   }
// }
// @media (max-height: 819px) and (max-width: 1535px) {
//   .header-menu {
//     top: 0%;
//     right: 100%;
//     left: auto;
//     max-height: 300px;
//   }
// }
// @media (max-height: 750px) and (max-width: 1535px) {
//   .header-menu {
//     top: 0%;
//     right: 100%;
//     left: auto;
//     max-height: 250px;
//   }
// }
// @media (max-height: 690px) and (max-width: 1535px) {
//   .header-menu {
//     top: 0%;
//     right: 100%;
//     left: auto;
//     max-height: 200px;
//   }
// }
</style>
