<script setup lang="ts">
import { useStore } from 'vuex'

const emit = defineEmits(['close-modal'])
const store = useStore()
const accountItem = computed(() => {
  return store.state.social.accountItem
})
</script>

<template>
  <div
    class="absolute z-1 top-0 left-full w-[322px] h-[246px] bg-white rounded-lg p-4 profile-modal"
  >
    <div
      class="absolute cursor-pointer top-2 right-2 size-9 rounded-full bg-[#F1F2F6] flex items-center justify-center"
      @click.stop="emit('close-modal')"
    >
      <ClientOnly>
        <fa class="text-[20px] text-[#525252]" :icon="['fas', 'times']" />
      </ClientOnly>
    </div>
    <div class="flex flex-col items-center">
      <p class="text-[#525252]">accountItem.username</p>
      <SourceAccountLogo
        class="mt-6"
        :account-profile-pic="accountItem.profilePic"
        :account-provider="accountItem.provider"
        :show-edit-icon="true"
        width="80px"
        height="80px"
      />
      <p class="!mt-3.5 text-[#525252] text-xl">Hi Larry!</p>
      <button
        class="mt-3 py-1.5 flex justify-center items-center w-[258px] h-[32px] rounded-full border border-[#4A71D4] text-[#4A71D4]"
      >
        Manage your Google account
      </button>
    </div>
  </div>
</template>

<style scoped>
.profile-modal {
  box-shadow: 0px 0px 8px #2228313d;
}
</style>
