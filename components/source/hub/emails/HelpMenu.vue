<script setup lang="ts">
import SharedIconHubEmailsCalenderIcon from '~/components/shared/icon/hub/emails/CalenderIcon.vue'

interface HelpMenuOptions {
  id: number
  label: string
  icon: string | Component
}

const helpMenuOptions = ref<HelpMenuOptions[]>([
  {
    id: 1,
    label: 'Help',
    icon: '',
  },
  {
    id: 2,
    label: 'send feedback to Google',
    icon: '',
  },
])
</script>

<template>
  <div
    class="menu-box absolute z-1 right-0 top-full w-[240px] !py-[1px]"
  >
    <div
      class="grid grid-cols-[16px_1fr] gap-x-2.5 items-center px-[18px] py-[7px] hover:bg-[#F1F2F6] cursor-pointer"
      v-for="helpMenuOption in helpMenuOptions"
      :key="helpMenuOption.id"
      :class="
        helpMenuOption.id === 1
          ? 'border-b border-[#C2C2C2] border-opacity-50'
          : ''
      "
    >
      <component v-if="helpMenuOption.icon" :is="helpMenuOption.icon" />
      <div class="flex justify-between items-center">
        <p class="#525252 leading-[21px] whitespace-nowrap">
          {{ helpMenuOption.label }}
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.menu-box {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
</style>
