<script setup lang="ts">
import { useStore } from 'vuex'
import DatePickerModal from '../../microsoft/settings/account/DatePickerModal.vue'

interface Filters {
  from: string
  to: string
  subject: string
  hasTheWord: string
  doesNotHaveTheWord: string
  size: string
  sizeOperator: { text: string; value: string } // s_ss, s_sl
  sizeUnit: { text: string; value: string } // s_smb, s_skb, s_sb
  hasAttachment: boolean
  excludeChats: boolean
  shouldArchive: boolean
  shouldMarkAsRead: boolean
  shouldStar: boolean
  label: {
    isActive: boolean
    value: any
  }
  forwardTo: {
    isActive: boolean
    value: any
  }
  delete: boolean
  shouldNeverSpam: boolean
  shouldAlwaysMarkAsImportant: boolean
  shouldNeverMarkAsImportant: boolean
  smartLabelToApply: {
    isActive: boolean
    value: any
  }
  applyMatchingFilter: boolean
  dateWithin: {
    value: { text: string; value: string }
    uptoDate: string | null
  }
  search: { id: number; text: string }
}

const props = withDefaults(
  defineProps<{
    wrapperClass?: string
  }>(),
  {
    wrapperClass: 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2',
  },
)

const { $toast } = useNuxtApp()
const store = useStore()
const router = useRouter()
const createdLabelOptions = computed(
  () => store.state.emails.createdLabelOptions,
)
const categoriesLabelsOptions = computed(
  () => store.state.emails.categoriesLabelsOptions,
)
const modalType = computed(() => store.state.emails.createNewFilterModal.type)
const modalFor = computed(() => store.state.emails.createNewFilterModal.for)
const selectedFilter = computed(() => store.state.emails.selectedFilter)
const tempFilters = computed(() => store.state.emails.tempFilters)
const isInSearchFilterDisabled = computed(() => {
  if (
    filter.value.from ||
    filter.value.to ||
    filter.value.subject ||
    filter.value.hasTheWord ||
    filter.value.doesNotHaveTheWord ||
    filter.value.size ||
    filter.value.hasAttachment
  ) {
    return false
  }
  return true
})
const validateFilter = computed(() => {
  if (
    !filter.value.shouldArchive &&
    !filter.value.delete &&
    !filter.value.shouldMarkAsRead &&
    !filter.value.shouldStar &&
    !filter.value.label.isActive &&
    !filter.value.forwardTo.isActive &&
    !filter.value.delete &&
    !filter.value.shouldNeverSpam &&
    !filter.value.shouldAlwaysMarkAsImportant &&
    !filter.value.shouldNeverMarkAsImportant &&
    !filter.value.smartLabelToApply.isActive &&
    !filter.value.applyMatchingFilter
  ) {
    return 'Please select at least one filter'
  } else if (filter.value.forwardTo.isActive && !filter.value.forwardTo.value) {
    return 'Please select a forwarding address'
  } else if (filter.value.label.isActive && !filter.value.label.value) {
    return 'Please select a label'
  } else if (
    filter.value.smartLabelToApply.isActive &&
    !filter.value.smartLabelToApply.value
  ) {
    return 'Please select a category'
  }
  return ''
})
// const menuItems = computed(() => store.state.emails.menuItems)

const isSearchInputs = ref(true)
const filter = ref<Filters>({
  from: '',
  to: '',
  subject: '',
  hasTheWord: '',
  doesNotHaveTheWord: '',
  size: '',
  sizeOperator: { text: 'greater than', value: 's_ss' }, // s_ss, s_sl
  sizeUnit: { text: 'MB', value: 's_smb' }, // s_smb, s_skb, s_sb
  hasAttachment: false,
  excludeChats: false,
  shouldArchive: false,
  shouldMarkAsRead: false,
  shouldStar: false,
  label: {
    isActive: false,
    value: null,
  },
  forwardTo: {
    isActive: false,
    value: null,
  },
  delete: false,
  shouldNeverSpam: false,
  shouldAlwaysMarkAsImportant: false,
  shouldNeverMarkAsImportant: false,
  smartLabelToApply: {
    isActive: false,
    value: null,
  },
  applyMatchingFilter: false,
  dateWithin: {
    value: { text: '1 day', value: '1_day' },
    uptoDate: null,
  },
  search: { id: 1, text: 'All Mail' },
})
const dateWithinOptions = [
  { text: '1 day', value: '1_day' },
  { text: '3 days', value: '3_days' },
  { text: '1 week', value: '1_week' },
  { text: '2 weeks', value: '2_weeks' },
  { text: '1 month', value: '1_month' },
  { text: '2 months', value: '2_months' },
  { text: '6 months', value: '6_months' },
  { text: '1 year', value: '1_year' },
]
const sizeOperatorOptions = [
  { text: 'greater than', value: 's_ss' },
  { text: 'less than', value: 's_sl' },
]
const sizeUnitOptions = [
  { text: 'MB', value: 's_smb' },
  { text: 'KB', value: 's_skb' },
  { text: 'Bytes', value: 's_sb' },
]
const forwardingAddressOptions = [
  { id: 1, text: '<EMAIL>' },
  { id: 2, text: '<EMAIL>' },
]

const searchMenuOptions = [
  { id: 1, text: 'All Mail', sectionEnd: false },
  { id: 2, text: 'Inbox', sectionEnd: false },
  { id: 3, text: 'Starred', sectionEnd: false },
  { id: 4, text: 'Sent Mail', sectionEnd: false },
  { id: 5, text: 'Drafts', sectionEnd: false },
  { id: 6, text: 'Chats', sectionEnd: false },
  { id: 7, text: 'Spam', sectionEnd: false },
  { id: 8, text: 'Trash', sectionEnd: true },
  { id: 9, text: 'Mail & Spam & Trash', sectionEnd: true },
  { id: 10, text: 'Read Mail', sectionEnd: false },
  { id: 11, text: 'Unread Mail', sectionEnd: true },
  { id: 12, text: 'Social', sectionEnd: false },
  { id: 13, text: 'Updates', sectionEnd: false },
  { id: 14, text: 'Forums', sectionEnd: false },
  { id: 15, text: 'Promotions', sectionEnd: true },
  { id: 16, text: 'Product', sectionEnd: false },
]

const closeFilterModal = () => {
  store.commit('emails/SET_CREATE_NEW_FILTER_MODAL', {
    isOpen: false,
    type: '',
    for: '',
  })
}
const backToSearchInputs = () => {
  isSearchInputs.value = true
}
const handleSearchInputs = () => {
  isSearchInputs.value = false
  closeFilterModal()
}
const createFilter = () => {
  if (validateFilter.value) {
    $toast('clear')
    $toast('error', {
      message: validateFilter.value,
      className: 'toasted-bg-alert',
    })
    return
  }
  if (modalType.value === 'create') {
    store.commit('emails/SET_NEW_FILTER', {
      id: new Date().getTime(),
      isChecked: false,
      ...filter.value,
    })
  } else {
    store.commit('emails/UPDATE_FILTER', filter.value)
  }
  closeFilterModal()
}

const filterWithinDate = ref<Date | null>(null)

const handleDateChange = (date: Date | null) => {
  if (date) {
    filter.value.dateWithin.uptoDate = date.toISOString()
  } else {
    filter.value.dateWithin.uptoDate = null
  }
}

const dateIsToday = computed(() => {
  return filterWithinDate.value
    ? filterWithinDate.value.toDateString() === new Date().toDateString()
    : false
})

const moveDateToToday = () => {
  filterWithinDate.value = new Date()
}
const handleNone = () => {
  filterWithinDate.value = null
}

const gotoForwardingAddress = () => {
  router.push('/source/hub/emails/settings/forwardandpop')
  closeFilterModal()
}
const isOperatorOpen = ref(false)
const isUnitOpen = ref(false)
const isDayWithinOpen = ref(false)
const isDateOpen = ref(false)
const isSearchMenuOpen = ref(false)

onMounted(() => {
  if (modalType.value === 'edit') {
    filter.value = {
      ...selectedFilter.value,
    }
  }
  if (modalFor.value === 'menu') {
    filter.value = {
      ...filter.value,
      ...tempFilters.value,
    }
  }
})
</script>

<template>
  <div
    class="bg-white w-full max-w-[678px] rounded-2xl z-10"
    :class="wrapperClass"
  >
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="flex justify-between items-center px-6 py-3.5">
        <p class="text-lg font-semibold text-[#505050]">Filter</p>
        <SharedIconHubEmailsCrossIcon
          @click.stop="closeFilterModal"
          class="w-4 h-4 cursor-pointer"
        />
      </div>
    </div>
    <div
      class="border-b-[2px] border-[#F1F2F6] px-6 py-5"
      :class="[
        modalFor === 'search' || modalFor === 'menu'
          ? 'max-h-[400px] overflow-y-auto custom-scroll'
          : '',
      ]"
    >
      <div v-if="isSearchInputs" class="space-y-[22px]">
        <div
          class="flex items-center text-base leading-[21px] text-[#525252] space-x-10"
        >
          <p class="min-w-[108px]">From</p>
          <input
            type="email"
            v-model="filter.from"
            class="w-full h-[27px] outline-none border-0 border-b border-[#E3E3E3] focus:border-[#525252]/30"
          />
        </div>
        <div
          class="flex items-center text-base leading-[21px] text-[#525252] space-x-10"
        >
          <p class="min-w-[108px]">To</p>
          <input
            type="email"
            v-model="filter.to"
            class="w-full h-[27px] outline-none border-0 border-b border-[#E3E3E3] focus:border-[#525252]/30"
          />
        </div>
        <div
          class="flex items-center text-base leading-[21px] text-[#525252] space-x-10"
        >
          <p class="min-w-[108px]">Subject</p>
          <input
            type="text"
            v-model="filter.subject"
            class="w-full h-[27px] outline-none border-0 border-b border-[#E3E3E3] focus:border-[#525252]/30"
          />
        </div>
        <div
          class="flex items-center text-base leading-[21px] text-[#525252] space-x-10"
        >
          <p class="min-w-[108px]">Has the words</p>
          <input
            type="text"
            v-model="filter.hasTheWord"
            class="w-full h-[27px] outline-none border-0 border-b border-[#E3E3E3] focus:border-[#525252]/30"
          />
        </div>
        <div
          class="flex items-center text-base leading-[21px] text-[#525252] space-x-10"
        >
          <p class="min-w-[108px]">Doesn't have</p>
          <input
            type="text"
            v-model="filter.doesNotHaveTheWord"
            class="w-full h-[27px] outline-none border-0 border-b border-[#E3E3E3] focus:border-[#525252]/30"
          />
        </div>
        <div
          class="flex items-center text-base leading-[21px] text-[#525252] space-x-10"
        >
          <p class="min-w-[108px]">Size</p>
          <div class="flex items-center justify-between space-x-2 w-full">
            <BaseDropsDown
              class="border-0 border-b border-[#E3E3E3] focus:border-[#525252]/30 language-dropdown"
              :class="[isOperatorOpen ? 'rounded-full' : '']"
              :options="sizeOperatorOptions"
              v-model="filter.sizeOperator"
              labelKey="text"
              id-key="value"
              :dorpdownPlaceholder="false"
              :menuWidth="200"
              :menuHeight="27"
              :dropdownWidth="200"
              :dropdownMaxHeight="290"
              :menuBgColor="!isOperatorOpen ? '#FFFFFF' : '#F1F2F6'"
              menuTextColor="#333333"
              dropsdownTextColor="#333333"
              hoverColor="#4A71D4"
              hoverTextColor="#FFFFFF"
              @open="(isOpen: boolean) => (isOperatorOpen = isOpen)"
            />
            <input
              type="text"
              v-model="filter.size"
              v-maska
              data-maska="########"
              class="w-24.5 h-[27px] outline-none border-0 border-b border-[#E3E3E3] focus:border-[#525252]/30"
            />
            <BaseDropsDown
              class="border-0 border-b border-[#E3E3E3] focus:border-[#525252]/30 language-dropdown"
              :class="[isUnitOpen ? 'rounded-full' : '']"
              :options="sizeUnitOptions"
              v-model="filter.sizeUnit"
              labelKey="text"
              id-key="value"
              :dorpdownPlaceholder="false"
              :menuWidth="100"
              :menuHeight="27"
              :dropdownWidth="100"
              :dropdownMaxHeight="290"
              :menuBgColor="!isUnitOpen ? '#FFFFFF' : '#F1F2F6'"
              menuTextColor="#333333"
              dropsdownTextColor="#333333"
              hoverColor="#4A71D4"
              hoverTextColor="#FFFFFF"
              @open="(isOpen: boolean) => (isUnitOpen = isOpen)"
            />
          </div>
        </div>
        <div
          v-if="modalFor === 'search'"
          class="flex items-center text-base leading-[21px] text-[#525252] space-x-10"
        >
          <p class="min-w-[108px]">Date within</p>
          <div class="flex items-center justify-between space-x-8 w-full">
            <BaseDropsDown
              class="border-0 border-b border-[#E3E3E3] focus:border-[#525252]/30 language-dropdown w-1/2"
              :class="[isDayWithinOpen ? 'rounded-full' : '']"
              :options="dateWithinOptions"
              v-model="filter.dateWithin.value"
              labelKey="text"
              id-key="value"
              :dorpdownPlaceholder="false"
              :menuWidth="'100%'"
              :menuHeight="27"
              :dropdownWidth="200"
              :dropdownMaxHeight="290"
              :menuBgColor="!isDayWithinOpen ? '#FFFFFF' : '#F1F2F6'"
              menuTextColor="#333333"
              dropsdownTextColor="#333333"
              hoverColor="#4A71D4"
              hoverTextColor="#FFFFFF"
              @change="moveDateToToday"
              @open="(isOpen: boolean) => (isDayWithinOpen = isOpen)"
            />
            <DatePickerModal
              v-model="filterWithinDate"
              class="w-1/2"
              dropdownClass="top-full right-0"
              :triggerBtnClass="`${isDateOpen ? 'bg-[#F1F2F6] rounded-full' : ''} w-full py-1`"
              @change="handleDateChange"
              @open="(isOpen: boolean) => (isDateOpen = isOpen)"
            >
              <template #footer>
                <div class="flex justify-between p-5">
                  <button
                    :disabled="dateIsToday"
                    class="text-sm"
                    :class="[dateIsToday ? 'opacity-50' : '']"
                    @click.stop="moveDateToToday"
                  >
                    Today
                  </button>
                  <button
                    :disabled="!filterWithinDate"
                    class="text-sm"
                    :class="[!filterWithinDate ? 'opacity-50' : '']"
                    @click.stop="handleNone"
                  >
                    None
                  </button>
                </div>
              </template>
            </DatePickerModal>
          </div>
        </div>
        <div
          v-if="modalFor === 'search'"
          class="flex items-center text-base leading-[21px] text-[#525252] space-x-10"
        >
          <p class="min-w-[108px]">Search</p>
          <BaseDropsDown
            class="border-0 border-b border-[#E3E3E3] focus:border-[#525252]/30 language-dropdown w-full"
            :class="[isSearchMenuOpen ? 'rounded-full' : '']"
            :options="searchMenuOptions"
            v-model="filter.search"
            labelKey="text"
            id-key="id"
            :dorpdownPlaceholder="false"
            :menuWidth="'100%'"
            :menuHeight="27"
            :dropdownWidth="200"
            :dropdownMaxHeight="290"
            :menuBgColor="!isSearchMenuOpen ? '#FFFFFF' : '#F1F2F6'"
            menuTextColor="#333333"
            dropsdownTextColor="#333333"
            hoverColor="#4A71D4"
            hoverTextColor="#FFFFFF"
            @open="(isOpen: boolean) => (isSearchMenuOpen = isOpen)"
          />
        </div>
        <div
          class="flex items-center text-base leading-[21px] text-[#525252] space-x-6"
        >
          <label class="flex items-center space-x-4">
            <InputsCheckBoxInput
              id="has_attachment"
              v-model="filter.hasAttachment"
              checkColor="#4A71D4"
            />
            <span>Has attachment</span></label
          >

          <label class="flex items-center space-x-4">
            <InputsCheckBoxInput
              id="exclude_chats"
              v-model="filter.excludeChats"
              checkColor="#4A71D4"
            />
            <span>don't include chats</span></label
          >
        </div>
      </div>
      <div v-else class="text-base text-[#333333]">
        <div class="flex items-center space-x-4">
          <fa
            :icon="['fas', 'arrow-left']"
            class="text-[#707070] cursor-pointer"
            @click.stop="backToSearchInputs"
          />
          <p class="text-[#525252]">
            When a message is an exact match for your search criteria:
          </p>
        </div>
        <div class="flex flex-col pt-[22px] space-y-[15px]">
          <label class="flex items-center space-x-4 whitespace-nowrap w-min">
            <InputsCheckBoxInput
              id="shouldArchive"
              :disabled="filter.delete"
              v-model="filter.shouldArchive"
              checkColor="#4A71D4"
            />
            <span>Skip the Inbox (Archive it)</span></label
          >
          <label class="flex items-center space-x-4 whitespace-nowrap w-min">
            <InputsCheckBoxInput
              id="shouldMarkAsRead"
              v-model="filter.shouldMarkAsRead"
              checkColor="#4A71D4"
            />
            <span>Mark as read</span></label
          >
          <label class="flex items-center space-x-4 whitespace-nowrap w-min">
            <InputsCheckBoxInput
              id="shouldStar"
              v-model="filter.shouldStar"
              checkColor="#4A71D4"
            />
            <span>Star it</span></label
          >
          <div class="flex items-center space-x-5">
            <label class="flex items-center space-x-4 whitespace-nowrap w-min">
              <InputsCheckBoxInput
                id="label_isActive"
                v-model="filter.label.isActive"
                checkColor="#4A71D4"
              />
              <span>Apply the label:</span></label
            >
            <BaseDropsDown
              class="border-0 border-b border-[#E3E3E3] focus:border-[#525252]/30 language-dropdown gray-placeholder"
              :options="createdLabelOptions"
              v-model="filter.label.value"
              labelKey="text"
              id-key="id"
              placeholder="Choose label"
              :dorpdownPlaceholder="true"
              :menuWidth="180"
              :menuHeight="27"
              :dropdownWidth="180"
              :dropdownMaxHeight="290"
              menuBgColor="#FFFFFF"
              menuTextColor="#333333"
              dropsdownTextColor="#333333"
              hoverColor="#4A71D4"
              hoverTextColor="#FFFFFF"
              :showEventSlot="true"
            >
              <template #event>
                <div
                  class="px-4 py-2 cursor-pointer hover:bg-[#F1F2F6] transition-all duration-200 opacity-80 border-b"
                  @click.stop="store.commit('emails/SET_NEW_LABEL_MODAL', true)"
                >
                  New label
                </div>
              </template>
            </BaseDropsDown>
          </div>
          <div class="flex items-center space-x-5">
            <label class="flex items-center space-x-4 whitespace-nowrap w-min">
              <InputsCheckBoxInput
                id="forwardTo_isAcative"
                v-model="filter.forwardTo.isActive"
                checkColor="#4A71D4"
              />
              <span>Forward it</span></label
            >
            <div class="flex items-center space-x-2">
              <BaseDropsDown
                v-if="forwardingAddressOptions.length > 0"
                class="border-0 border-b border-[#E3E3E3] focus:border-[#525252]/30 language-dropdown gray-placeholder"
                :options="forwardingAddressOptions"
                v-model="filter.forwardTo.value"
                labelKey="text"
                id-key="id"
                placeholder="Choose an address"
                :menuWidth="200"
                :menuHeight="27"
                :dropdownWidth="200"
                :dropdownMaxHeight="210"
                menuBgColor="#FFFFFF"
                menuTextColor="#333333"
                dropsdownTextColor="#333333"
                hoverColor="#4A71D4"
                hoverTextColor="#FFFFFF"
              />
              <span
                role="button"
                tabindex="0"
                class="text-[#3964D0] font-semibold cursor-pointer"
                @click.stop="gotoForwardingAddress"
              >
                Add forwarding address</span
              >
            </div>
          </div>
          <label class="flex items-center space-x-4 whitespace-nowrap w-min">
            <InputsCheckBoxInput
              id="delete_it"
              :disabled="filter.shouldArchive"
              v-model="filter.delete"
              checkColor="#4A71D4"
            />
            <span>Delete it</span></label
          >
          <label class="flex items-center space-x-4 whitespace-nowrap w-min">
            <InputsCheckBoxInput
              id="shouldNeverSpam"
              v-model="filter.shouldNeverSpam"
              checkColor="#4A71D4"
            />
            <span>Never send it to Spam</span></label
          >
          <label class="flex items-center space-x-4 whitespace-nowrap w-min">
            <InputsCheckBoxInput
              id="shouldAlwaysMarkAsImportant"
              :disabled="filter.shouldNeverMarkAsImportant"
              v-model="filter.shouldAlwaysMarkAsImportant"
              checkColor="#4A71D4"
            />
            <span>Always mark it as important</span></label
          >
          <label class="flex items-center space-x-4 whitespace-nowrap w-min">
            <InputsCheckBoxInput
              id="shouldNeverMarkAsImportant"
              :disabled="filter.shouldAlwaysMarkAsImportant"
              v-model="filter.shouldNeverMarkAsImportant"
              checkColor="#4A71D4"
            />
            <span>Never mark it as important</span></label
          >
          <div class="flex items-center space-x-5">
            <label class="flex items-center space-x-4 whitespace-nowrap w-min">
              <InputsCheckBoxInput
                id="smartLabelToApply_isActive"
                v-model="filter.smartLabelToApply.isActive"
                checkColor="#4A71D4"
              />
              <span>Categorize as:</span></label
            >
            <BaseDropsDown
              class="border-0 border-b border-[#E3E3E3] focus:border-[#525252]/30 language-dropdown gray-placeholder"
              :options="categoriesLabelsOptions"
              v-model="filter.smartLabelToApply.value"
              labelKey="text"
              id-key="id"
              placeholder="Choose category"
              :dorpdownPlaceholder="true"
              :menuWidth="192"
              :menuHeight="27"
              :dropdownWidth="192"
              :dropdownMaxHeight="210"
              menuBgColor="#FFFFFF"
              menuTextColor="#333333"
              dropsdownTextColor="#333333"
              hoverColor="#4A71D4"
              hoverTextColor="#FFFFFF"
            />
          </div>
          <label class="flex items-center space-x-4 whitespace-nowrap w-min">
            <InputsCheckBoxInput
              id="applyMatchingFilter"
              v-model="filter.applyMatchingFilter"
              checkColor="#4A71D4"
            />
            <span>Also apply filter to 20 matching conversations.</span></label
          >
          <p
            class="flex items-center text-[#525252] space-x-2 font-semibold cursor-pointer px-4 w-min whitespace-nowrap"
          >
            <fa :icon="['fas', 'circle-question']" />
            <span>Learn more</span>
          </p>
        </div>
      </div>
    </div>
    <div class="flex justify-end items-center space-x-2 px-6 py-3.5">
      <button
        v-if="isSearchInputs"
        :disabled="isInSearchFilterDisabled"
        class="rounded-full font-semibold text-[#4A71D4] border-[1px] border-[#4A71D4] min-w-[108px] h-[35px] px-6"
        :class="isInSearchFilterDisabled ? 'opacity-50' : ''"
        @click.stop="isSearchInputs = false"
      >
        {{ modalType === 'create' ? 'Create Filter' : 'Continue' }}
      </button>
      <button
        v-if="isSearchInputs"
        class="rounded-full font-semibold text-[#FFFFFF] bg-[#4A71D4] min-w-[108px] h-[35px] px-6"
        @click.stop="handleSearchInputs"
      >
        Search
      </button>

      <button
        v-if="!isSearchInputs"
        class="rounded-full font-semibold text-[#FFFFFF] bg-[#4A71D4] min-w-[108px] h-[35px] px-6"
        @click.stop="createFilter"
      >
        {{ modalType === 'create' ? 'Create Filter' : 'Update' }}
      </button>
    </div>
  </div>
</template>

<style scoped></style>
