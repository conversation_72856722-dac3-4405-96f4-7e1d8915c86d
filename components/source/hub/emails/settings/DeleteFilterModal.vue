<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const deleteFilterModal = computed(() => store.state.emails.deleteFilterModal)

const closeDeleteModal = () => {
  store.commit('emails/SET_DELETE_FILTER_MODAL', {
    isOpen: false,
    type: '',
  })
}
const deleteFilter = () => {
  store.commit('emails/DELETE_FILTER')
  closeDeleteModal()
}
</script>

<template>
  <div
    class="bg-white w-full max-w-[678px] rounded-2xl fixed z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
  >
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="flex justify-between items-center px-6 py-3.5">
        <p class="text-lg font-semibold text-[#505050]">
          Confirm filter deletion
        </p>
        <SharedIconHubEmailsCrossIcon
          @click="closeDeleteModal"
          class="w-4 h-4 cursor-pointer"
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6] px-6 py-5">
      {{
        deleteFilterModal.type === 'single'
          ? 'Really delete this filter?'
          : 'Really delete selected filters?'
      }}
    </div>
    <div class="flex justify-end items-center space-x-2 px-6 py-3.5">
      <button
        @click="closeDeleteModal"
        class="rounded-full font-semibold text-[#4A71D4] border-[1px] border-[#4A71D4] min-w-[108px] h-[35px] px-6"
      >
        Cancel
      </button>

      <button
        @click="deleteFilter"
        class="rounded-full font-semibold text-[#FFFFFF] bg-[#4A71D4] min-w-[108px] h-[35px] px-6"
      >
        Ok
      </button>
    </div>
  </div>
</template>

<style scoped></style>
