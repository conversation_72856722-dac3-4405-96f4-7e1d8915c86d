<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

const removeEmailModal = computed(() => store.state.emails.removeEmailModal)

const closeModal = () => {
  store.commit('emails/SET_REMOVE_EMAIL_MODAL', {
    isOpen: false,
    type: '',
    email: null,
  })
}
const handleRemoveEmail = () => {
  if (removeEmailModal.value.type === 'verified') {
    store.commit('emails/REMOVE_EMAIL')
  } else {
    store.commit('emails/REMOVE_UNVERIFIED_EMAIL')
  }
  closeModal()
}
</script>

<template>
  <div
    class="bg-white w-full max-w-[678px] rounded-2xl fixed z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
  >
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="flex justify-between items-center px-6 py-3.5">
        <p class="text-lg font-semibold text-[#505050]">
          Confirm forward address removal
        </p>
        <SharedIconHubEmailsCrossIcon
          @click="closeModal"
          class="w-4 h-4 cursor-pointer"
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6] px-6 py-5 text-[#333333]">
      Are you sure you want to remove {{ removeEmailModal.email.email }}?
    </div>

    <div class="flex justify-end items-center space-x-2 px-6 py-3.5">
      <button
        @click="handleRemoveEmail"
        class="rounded-full font-semibold text-[#FFFFFF] bg-[#4A71D4] min-w-[108px] h-[35px] px-6"
      >
        Ok
      </button>
    </div>
  </div>
</template>

<style scoped></style>
