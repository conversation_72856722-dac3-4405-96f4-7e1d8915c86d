<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

const reportSpamModal = computed(() => store.state.emails.reportSpamModal)

const closeModal = () => {
  store.commit('emails/SET_REPORT_SPAM_MODAL', false)
}
const reportSpam = () => {
  closeModal()
}
const unsubscribe = () => {
  closeModal()
}
</script>

<template>
  <Transition name="page" mode="out-in">
    <div v-if="reportSpamModal">
      <div
        class="bg-white w-full max-w-[678px] rounded-2xl fixed z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
      >
        <div class="border-b-[2px] border-[#F1F2F6]">
          <div class="flex justify-between items-center px-6 py-3.5">
            <p class="text-lg font-semibold text-[#505050]">
              Report spam or unsubscribe?
            </p>
            <SharedIconHubEmailsCrossIcon
              @click="closeModal"
              class="w-4 h-4 cursor-pointer"
            />
          </div>
        </div>
        <div
          class="border-b-[2px] border-[#F1F2F6] px-6 pb-3 pt-[18px] text-[#525252] text-base"
        >
          <p>
            Gmail can
            <span class="text-[#525252] font-bold">unsubscribe</span> you from
            the sender.
          </p>
          <p class="pt-[22px]">
            If you didn't sign up to receive this message,
            <span class="text-[#525252] font-bold">Report spam</span> instead to
            help protect all Gmail users from unwanted email.
            <span class="text-[#4A71D4] underline">Learn more</span>
          </p>
        </div>

        <div class="flex justify-end items-center space-x-2 px-6 py-3.5">
          <button
            @click="reportSpam"
            class="rounded-full font-semibold text-[#4A71D4] border-[1px] border-[#4A71D4] min-w-[140px] h-[35px] px-6"
          >
            Report spam
          </button>

          <button
            @click="unsubscribe"
            class="rounded-full font-semibold text-[#FFFFFF] bg-[#4A71D4] min-w-[140px] h-[35px] px-6"
          >
            Unsubscribe
          </button>
        </div>
      </div>
      <div @click.stop="closeModal" class="fixed inset-0 transition-opacity">
        <div class="absolute inset-0 bg-gray-600 opacity-75"></div>
      </div>
    </div>
  </Transition>
</template>

<style scoped></style>
