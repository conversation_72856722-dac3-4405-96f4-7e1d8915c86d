<script setup lang="ts">
import { useStore } from 'vuex'
import type { EmailAddress, Forwarding } from '~/types/hubEmailsSettings'
import EmailDropdown from './EmailDropdown.vue'

const emit = defineEmits<{
  changeForwarding: [forwarding: Forwarding]
}>()

const store = useStore()
const { $toast } = useNuxtApp()

const forwardingEmails = computed<EmailAddress[]>(
  () => store.state.emails.forwardingEmails,
)
const unverifiedEmails = computed<EmailAddress[]>(
  () => store.state.emails.unverifiedEmails,
)
const forwardingData = computed<Forwarding>(
  () => store.state.emails.forwardingAndPopImap.forwarding,
)

const forwardingOptions = [
  {
    label: "Keep Gmail's copy in the Inbox",
    value: 'keep-in-inbox',
  },
  {
    label: "Mark Gmail's copy as read",
    value: 'mark-as-read',
  },
  {
    label: "Archive Gmail's copy",
    value: 'archive-copy',
  },
  {
    label: "Delete Gmail's copy",
    value: 'delete-copy',
  },
]

const forwarding = ref<Forwarding>({
  status: 'disabled',
  forwardingEmail: null,
  forwardingAction: {
    label: "Keep Gmail's copy in the Inbox",
    value: 'keep-in-inbox',
  },
})

const handleOpenCreateNewFilterModal = () => {
  store.commit('emails/SET_CREATE_NEW_FILTER_MODAL', {
    isOpen: true,
    type: 'create',
    for: 'settings',
  })
}
const handleOpenForwardingAddressModal = () => {
  store.commit('emails/SET_FORWARDING_ADDRESS_MODAL', true)
}

const handleRemoveEmailModal = (email: EmailAddress) => {
  store.commit('emails/SET_REMOVE_EMAIL_MODAL', {
    type: 'verified',
    isOpen: true,
    email,
  })
}
const handleRemoveUnverifiedEmailModal = (email: EmailAddress) => {
  store.commit('emails/SET_REMOVE_EMAIL_MODAL', {
    type: 'unverified',
    isOpen: true,
    email,
  })
}

const handleResentEmail = (email: EmailAddress) => {
  $toast('clear')
  $toast('success', {
    message: `Verification email sent to ${email.email}`,
    className: 'toasted-bg-archive',
  })
}

const handleEnableForwarding = () => {
  if (!forwarding.value.forwardingEmail && forwardingEmails.value.length > 0) {
    forwarding.value.forwardingEmail = forwardingEmails.value[0]
  }
}

watch(
  () => forwardingData.value,
  (newVal) => {
    if (!newVal?.forwardingEmail) {
      forwarding.value.status = 'disabled'
      forwarding.value.forwardingEmail = null
    }
  },
  { deep: true },
)

watch(
  forwarding,
  () => {
    emit('changeForwarding', forwarding.value)
  },
  { deep: true },
)

onMounted(() => {
  if (forwardingData.value) {
    forwarding.value = JSON.parse(JSON.stringify(forwardingData.value))
  }
})

// This is for dummy verify
const verifyEmail = (email: EmailAddress) => {
  store.commit('emails/SET_VERIFIED_EMAIL', email)
}
</script>

<template>
  <div class="border-b-[2px] border-[#F1F2F6]">
    <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
      <div class="text-base space-y-1.5">
        <p class="text-[#333333] font-semibold">Forwarding:</p>
        <p class="text-[#4A71D4]">Learn more</p>
      </div>
      <div class="">
        <div v-if="forwardingEmails.length > 0" class="mb-6">
          <div class="flex items-center space-x-3">
            <input
              type="radio"
              id="forwarding_disabled"
              name="forwarding"
              value="disabled"
              v-model="forwarding.status"
              class="w-4 h-4 text-[#4A71D4] border-gray-300 cursor-pointer"
            />
            <label for="forwarding_disabled" class="cursor-pointer">
              Disable forwarding
            </label>
          </div>
          <div class="flex items-center space-x-3">
            <input
              type="radio"
              id="forwarding_enabled"
              name="forwarding"
              value="enabled"
              v-model="forwarding.status"
              class="w-4 h-4 text-[#4A71D4] border-gray-300 cursor-pointer"
              @input="handleEnableForwarding"
            />
            <div class="flex items-center space-x-1">
              <label for="forwarding_enabled" class="cursor-pointer">
                Forward a copy of incoming mail to</label
              >
              <EmailDropdown
                class="border border-[#C2C2C2] rounded-full w-min language-dropdown"
                :options="forwardingEmails"
                v-model="forwarding.forwardingEmail"
                labelKey="email"
                id-key="id"
                :dorpdownPlaceholder="false"
                :menuWidth="215"
                :menuHeight="27"
                :dropdownWidth="244"
                :dropdownMaxHeight="300"
                menuBgColor="#FFFFFF"
                menuTextColor="#333333"
                dropsdownTextColor="#333333"
                hoverColor="#4A71D4"
                hoverTextColor="#FFFFFF"
                selectedOptionClass="text-[#333333] text-sm"
                @removeEmail="handleRemoveEmailModal"
                @removeUnverifiedEmail="handleRemoveUnverifiedEmailModal"
              />
              <span> and </span>
              <BaseDropsDown
                class="border border-[#C2C2C2] rounded-full w-min language-dropdown"
                :options="forwardingOptions"
                v-model="forwarding.forwardingAction"
                labelKey="label"
                id-key="value"
                :dorpdownPlaceholder="false"
                :menuWidth="266"
                :menuHeight="27"
                :dropdownWidth="266"
                :dropdownMaxHeight="290"
                menuBgColor="#FFFFFF"
                menuTextColor="#333333"
                dropsdownTextColor="#333333"
                hoverColor="#4A71D4"
                hoverTextColor="#FFFFFF"
                selectedOptionClass="text-[#333333] text-sm"
              />
            </div>
          </div>
        </div>
        <button
          @click="handleOpenForwardingAddressModal"
          class="h-[33px] px-4 text-sm text-[#3964D0] rounded-full bg-[#F1F2F6] text-center"
        >
          Add a forwarding address
        </button>
        <p
          v-if="unverifiedEmails.length === 0"
          class="text-base leading-[21px] text-[#525252]"
          :class="[unverifiedEmails.length === 0 ? 'pt-[22px]' : '']"
        >
          Tip: You can also forward only some of your mail by
          <button
            class="text-[#3964D0]"
            @click="handleOpenCreateNewFilterModal"
          >
            creating a filter!
          </button>
        </p>
        <div v-if="unverifiedEmails.length > 0" class="pt-[22px] space-y-1">
          <p
            v-for="verify in unverifiedEmails"
            :key="verify.id"
            class="text-base leading-[21px] space-x-1.5"
          >
            <span class="text-[#333333]" @click="verifyEmail(verify)"
              >Verify {{ verify.email }}</span
            >
            <button class="text-[#3964D0]" @click="handleResentEmail(verify)">
              Re-send email
            </button>
            <button
              class="text-[#3964D0]"
              @click="handleRemoveUnverifiedEmailModal(verify)"
            >
              Remove address
            </button>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
