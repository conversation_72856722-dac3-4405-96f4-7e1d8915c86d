<script setup lang="ts">
import { useStore } from 'vuex'
import type { EmailAddress } from '~/types/hubEmailsSettings'

const store = useStore()

interface DropsDownProps {
  placeholder?: string
  options: Record<string, any>[]
  labelKey?: string
  idKey?: string
  menuWidth?: number
  menuHeight?: number
  menuBgColor?: string
  menuTextColor?: string
  dropsdownTextColor?: string
  arrowColor?: string
  dropdownWidth?: number
  dropdownBgColor?: string
  dropdownMaxHeight?: number
  scrollbarTrackColor?: string
  scrollbarThumbColor?: string
  dorpdownPlaceholder?: boolean
  hoverColor?: string
  hoverTextColor?: string
  showEventSlot?: boolean
  disabled?: boolean
  selectedOptionClass?: string
}

type OptionType = Record<string, any> | null

const props = withDefaults(defineProps<DropsDownProps>(), {
  placeholder: 'Select an option',
  options: () => [],
  labelKey: 'label',
  idKey: 'id',
  menuWidth: 200,
  menuHeight: 27,
  menuBgColor: '#fff',
  menuTextColor: '#525252',
  dropsdownTextColor: '#525252',
  arrowColor: '#4A71D4',
  dropdownWidth: 250,
  dropdownBgColor: '#fff',
  dropdownMaxHeight: 200,
  scrollbarTrackColor: '#a1cdff50',
  scrollbarThumbColor: '#a1cdff',
  dorpdownPlaceholder: false,
  hoverColor: '#F1F2F6',
  hoverTextColor: '#333333',
  showEventSlot: false,
  disabled: false,
  selectedOptionClass: '',
})

const isOpen = ref(false)
const model = defineModel<OptionType>({ default: null })
const dropdownRef = ref<HTMLDivElement | null>(null)
const emit = defineEmits(['change', 'removeEmail', 'removeUnverifiedEmail'])

const unverifiedEmails = computed<EmailAddress[]>(
  () => store.state.emails.unverifiedEmails,
)

const selectedOption = computed(() => model.value)

const toggleDropdown = () => {
  if (!props.disabled) {
    isOpen.value = !isOpen.value
  }
}

const selectOption = (option: OptionType) => {
  if (!props.disabled) {
    model.value = option
    isOpen.value = false
    emit('change', option)
  }
}

const handleClickOutside = (event: Event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false
  }
}

const handleRemoveEmail = (option: OptionType) => {
  emit('removeEmail', option)
  isOpen.value = false
}

const handleRemoveUnverifiedEmail = (option: OptionType) => {
  emit('removeUnverifiedEmail', option)
  isOpen.value = false
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div ref="dropdownRef" class="relative">
    <!-- Dropdown button -->
    <div
      class="dropdown_button flex items-center justify-between px-4 py-2 rounded-full transition-all font-semibold"
      :class="{
        'cursor-pointer': !disabled,
        'opacity-50': disabled,
      }"
      :style="{
        color: menuTextColor,
        width: `${menuWidth}px`,
        height: `${menuHeight}px`,
        backgroundColor: menuBgColor,
      }"
      @click="toggleDropdown"
    >
      <span :class="[selectedOption ? selectedOptionClass : 'placeholder']">{{
        selectedOption ? selectedOption[labelKey] : placeholder
      }}</span>
      <ClientOnly>
        <fa
          class="text-xl"
          :style="{ color: disabled ? '#9ca3af' : arrowColor }"
          :icon="['fas', isOpen ? 'caret-up' : 'caret-down']"
        />
      </ClientOnly>
    </div>

    <!-- Dropdown menu -->
    <div
      v-if="isOpen && !disabled && (options.length > 0 || showEventSlot)"
      class="absolute mt-1 rounded-lg z-10 h-auto pb-3 dropdown_menu_container"
    >
      <div
        class="py-2 rounded-lg shadow-xl overflow-y-auto dropdown_menu dropdown-scroll"
        :style="{
          color: dropsdownTextColor,
          width: `${dropdownWidth}px`,
          backgroundColor: dropdownBgColor,
          maxHeight: `${dropdownMaxHeight}px`,
          '--scrollbar-track-color': props.scrollbarTrackColor,
          '--scrollbar-thumb-color': props.scrollbarThumbColor,
        }"
      >
        <div
          v-if="dorpdownPlaceholder"
          class="px-4 py-2 cursor-pointer hover:bg-[#F1F2F6] transition-all duration-200 opacity-80"
          @click="selectOption(null)"
        >
          {{ placeholder }}
        </div>
        <div
          v-for="option in options"
          :key="option[props.idKey]"
          class="px-4 py-1.5 cursor-pointer option transition-all duration-200 truncate"
          :style="{
            '--hovercolor': hoverColor,
            '--hovertextcolor': hoverTextColor,
          }"
          @click="selectOption(option)"
        >
          {{ option[props.labelKey] }}
        </div>
        <div
          class="px-4 py-1.5 option transition-all duration-200"
          :style="{
            '--hovercolor': hoverColor,
            '--hovertextcolor': hoverTextColor,
          }"
        >
          --
        </div>
        <template v-if="options.length > 0">
          <div
            v-for="option in options"
            :key="option[props.idKey]"
            class="px-4 py-1.5 cursor-pointer option transition-all duration-200 truncate"
            :style="{
              '--hovercolor': hoverColor,
              '--hovertextcolor': hoverTextColor,
            }"
            @click="handleRemoveEmail(option)"
          >
            {{ 'Remove ' + option[props.labelKey] }}
          </div>
        </template>
        <template v-if="unverifiedEmails.length > 0">
          <div
            v-for="option in unverifiedEmails"
            :key="option.id"
            class="px-4 py-1.5 cursor-pointer option transition-all duration-200 truncate"
            :style="{
              '--hovercolor': hoverColor,
              '--hovertextcolor': hoverTextColor,
            }"
            @click="handleRemoveUnverifiedEmail(option)"
          >
            {{ 'Remove ' + option.email }}
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.gray-placeholder .placeholder {
  color: #707070;
}
.dropdown-scroll {
  overflow-y: auto;
  overflow-x: auto;
  -ms-overflow-style: none; /* IE 11 */
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb-color) var(--scrollbar-track-color);

  &::-webkit-scrollbar {
    width: 4px !important;
    height: 4px !important;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background: var(--scrollbar-track-color);
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: var(--scrollbar-thumb-color);
  }
}
@media (max-width: 767px) {
  .dropdown-scroll {
    &::-webkit-scrollbar {
      width: 3px;
      height: 3px;
    }
  }
}
.language-dropdown > div {
  font-weight: 400;
}
.option:hover {
  background-color: var(--hovercolor);
  color: var(--hovertextcolor);
}
.dropdown > .dropdown_button {
  width: 100% !important;
}
.dropdown > .dropdown_menu_container {
  width: 100% !important;
}
.dropdown > .dropdown_menu_container > .dropdown_menu {
  width: 100% !important;
}
</style>
