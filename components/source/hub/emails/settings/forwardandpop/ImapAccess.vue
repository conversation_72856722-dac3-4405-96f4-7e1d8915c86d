<script setup lang="ts">
import { useStore } from 'vuex'
import type { ImapAccess } from '~/types/hubEmailsSettings'

const emit = defineEmits<{
  changeImapAccess: [imapAccess: ImapAccess]
}>()

const store = useStore()
const imapData = computed<ImapAccess>(
  () => store.state.emails.forwardingAndPopImap.imapAccess,
)

const folderSizeOptions = [
  {
    label: '1,000',
    value: 1000,
  },
  {
    label: '2,000',
    value: 2000,
  },
  {
    label: '5,000',
    value: 5000,
  },
  {
    label: '10,000',
    value: 10000,
  },
]

const imapAccess = ref<ImapAccess>({
  expunge: 'on',
  deleteAction: 'archive',
  folderSizeLimit: 'unlimited_size',
  currentFolderSize: {
    label: '1,000',
    value: 1000,
  },
})

watch(
  imapAccess,
  () => {
    emit('changeImapAccess', imapAccess.value)
  },
  { deep: true },
)

onMounted(() => {
  if (imapData.value) {
    imapAccess.value = JSON.parse(JSON.stringify(imapData.value))
  }
})
</script>

<template>
  <div class="border-b-[2px] border-[#F1F2F6]">
    <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
      <div class="text-base">
        <p class="text-[#333333] font-semibold">IMAP access:</p>
        <p class="text-[#525252] text-sm mt-2">
          (access Gmail from other clients using IMAP)
        </p>
        <p class="text-[#4A71D4] mt-1">Learn more</p>
      </div>
      <div class="flex flex-col space-y-6">
        <div
          class="text-base leading-[21px] text-[#333333] space-y-1.5 flex flex-col"
        >
          <p class="font-semibold">When I mark a message in IMAP as deleted:</p>
          <div class="flex items-center space-x-3">
            <input
              type="radio"
              id="expunge_on"
              name="expunge"
              value="on"
              v-model="imapAccess.expunge"
              class="w-4 h-4 border-gray-300 cursor-pointer"
            />
            <label for="expunge_on" class="cursor-pointer"
              >Auto-Expunge on - Immediately update the server. (default)</label
            >
          </div>
          <div class="flex items-center space-x-3">
            <input
              type="radio"
              id="expunge_off"
              name="expunge"
              value="off"
              v-model="imapAccess.expunge"
              class="w-4 h-4 border-gray-300 cursor-pointer"
            />
            <label for="expunge_off" class="cursor-pointer"
              >Auto-Expunge off - Wait for the client to update the
              server.</label
            >
          </div>
        </div>

        <div
          class="text-base leading-[21px] text-[#333333] space-y-1.5 flex flex-col"
        >
          <p class="font-semibold">
            When a message is marked as deleted and expunged from the last
            visible IMAP folder:
          </p>
          <div class="flex items-center space-x-3">
            <input
              type="radio"
              :disabled="imapAccess.expunge === 'on'"
              id="archive"
              name="deleteAction"
              value="archive"
              v-model="imapAccess.deleteAction"
              class="w-4 h-4 border-gray-300 cursor-pointer"
            />
            <label for="archive" class="cursor-pointer"
              >Archive the message (default)</label
            >
          </div>
          <div class="flex items-center space-x-3">
            <input
              type="radio"
              :disabled="imapAccess.expunge === 'on'"
              id="move_to_bin"
              name="deleteAction"
              value="move_to_bin"
              v-model="imapAccess.deleteAction"
              class="w-4 h-4 border-gray-300 cursor-pointer"
            />
            <label for="move_to_bin" class="cursor-pointer"
              >Move the message to the Bin</label
            >
          </div>
          <div class="flex items-center space-x-3">
            <input
              type="radio"
              :disabled="imapAccess.expunge === 'on'"
              id="delete_forever"
              name="deleteAction"
              value="delete_forever"
              v-model="imapAccess.deleteAction"
              class="w-4 h-4 border-gray-300 cursor-pointer"
            />
            <label for="delete_forever" class="cursor-pointer"
              >Immediately delete the message forever</label
            >
          </div>
        </div>

        <div
          class="text-base leading-[21px] text-[#333333] space-y-1.5 flex flex-col"
        >
          <p class="font-semibold">Folder size limits</p>
          <div class="flex items-center space-x-3">
            <input
              type="radio"
              id="unlimited_size"
              name="folderSizeLimit"
              value="unlimited_size"
              v-model="imapAccess.folderSizeLimit"
              class="w-4 h-4 border-gray-300 cursor-pointer"
            />
            <label for="unlimited_size" class="cursor-pointer"
              >Do not limit the number of messages in an IMAP folder
              (default)</label
            >
          </div>
          <div class="flex items-center space-x-3">
            <input
              type="radio"
              id="limited_size"
              name="folderSizeLimit"
              value="limited_size"
              v-model="imapAccess.folderSizeLimit"
              class="w-4 h-4 border-gray-300 cursor-pointer"
            />
            <label for="limited_size" class="cursor-pointer"
              >Limit IMAP folders to contain no more than this many
              messages</label
            >
            <BaseDropsDown
              class="border border-[#C2C2C2] rounded-full w-min"
              :options="folderSizeOptions"
              v-model="imapAccess.currentFolderSize"
              labelKey="label"
              id-key="value"
              :dorpdownPlaceholder="false"
              :menuWidth="100"
              :menuHeight="27"
              :dropdownWidth="100"
              :dropdownMaxHeight="290"
              menuBgColor="#FFFFFF"
              menuTextColor="#333333"
              dropsdownTextColor="#333333"
              hoverColor="#4A71D4"
              hoverTextColor="#FFFFFF"
              selectedOptionClass="text-[#333333] text-base font-normal"
              @change="
                () => {
                  imapAccess.folderSizeLimit = 'limited_size'
                }
              "
            />
          </div>
        </div>

        <div class="text-base leading-[21px] text-[#333333] space-y-1.5">
          <p class="font-semibold">
            Configure your email client
            <span class="font-normal">(e.g. Outlook, Thunderbird, iPhone)</span>
          </p>
          <p class="text-[#3964D0] font-normal">Configuration instructions</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
