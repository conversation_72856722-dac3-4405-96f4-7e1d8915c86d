<script setup lang="ts">
import { useStore } from 'vuex'
import type { Pop } from '~/types/hubEmailsSettings'

const emit = defineEmits<{
  changePop: [pop: Pop]
}>()

const store = useStore()
const popData = computed<Pop>(() => store.state.emails.forwardingAndPopImap.pop)

const pop = ref({
  status: 'disabled',
  enabledSince: '4:21 PM',
  popAction: {
    label: "Keep Gmail's copy in the Inbox",
    value: 'keep-in-inbox',
  },
})

const popOptions = [
  {
    label: "Keep Gmail's copy in the Inbox",
    value: 'keep-in-inbox',
  },
  {
    label: "Mark Gmail's copy as read",
    value: 'mark-as-read',
  },
  {
    label: "Archive Gmail's copy",
    value: 'archive-copy',
  },
  {
    label: "Delete Gmail's copy",
    value: 'delete-copy',
  },
]

watch(
  pop,
  () => {
    emit('changePop', pop.value)
  },
  { deep: true },
)

onMounted(() => {
  if (popData.value) {
    pop.value = JSON.parse(JSON.stringify(popData.value))
  }
})
</script>

<template>
  <div class="border-b-[2px] border-[#F1F2F6]">
    <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
      <div class="text-base space-y-1.5">
        <p class="text-[#333333] font-semibold">POP download:</p>
        <p class="text-[#4A71D4]">Learn more</p>
      </div>
      <div class="flex flex-col space-y-6">
        <div
          class="text-base leading-[21px] text-[#333333] space-y-1.5 flex flex-col"
        >
          <p class="font-semibold">
            1. Status:
            <span v-if="popData.status === 'disabled'">POP is disabled</span>
            <span v-else class="text-[#007700]">POP is enabled</span>
            <span v-if="popData.status !== 'disabled'" class="font-normal">
              for all mail that has arrived since
              {{
                popData.status === 'all_mail' ? '1/1/70' : popData.enabledSince
              }}</span
            >
          </p>
          <div class="flex items-center space-x-3">
            <input
              type="radio"
              id="all_mail"
              name="pop"
              value="all_mail"
              v-model="pop.status"
              class="w-4 h-4 text-[#4A71D4] border-gray-300 cursor-pointer"
            />
            <label for="all_mail" class="cursor-pointer"
              >Enable POP for <span class="font-semibold">all mail</span>
              <span v-if="popData.status !== 'disabled'">
                (even mail that's already been downloaded)
              </span>
            </label>
          </div>
          <div class="flex items-center space-x-3">
            <input
              type="radio"
              id="new_mail"
              name="pop"
              value="new_mail"
              v-model="pop.status"
              class="w-4 h-4 text-[#4A71D4] border-gray-300 cursor-pointer"
            />
            <label for="new_mail" class="cursor-pointer"
              >Enable POP for
              <span class="font-semibold"
                >mail that arrives from now on</span
              ></label
            >
          </div>
          <div
            v-if="popData.status !== 'disabled'"
            class="flex items-center space-x-3"
          >
            <input
              type="radio"
              id="disabled"
              name="pop"
              value="disabled"
              v-model="pop.status"
              class="w-4 h-4 text-[#4A71D4] border-gray-300 cursor-pointer"
            />
            <label for="disabled" class="cursor-pointer">
              <span class="font-semibold">Disable </span>POP</label
            >
          </div>
        </div>
        <div
          class="text-base leading-[21px] text-[#333333] flex space-x-2 items-center"
        >
          <p class="font-semibold">2. When messages are accessed with POP</p>
          <BaseDropsDown
            class="border border-[#C2C2C2] rounded-full w-min language-dropdown"
            :options="popOptions"
            v-model="pop.popAction"
            labelKey="label"
            id-key="value"
            :dorpdownPlaceholder="false"
            :menuWidth="266"
            :menuHeight="27"
            :dropdownWidth="266"
            :dropdownMaxHeight="290"
            menuBgColor="#FFFFFF"
            menuTextColor="#333333"
            dropsdownTextColor="#333333"
            hoverColor="#4A71D4"
            hoverTextColor="#FFFFFF"
            selectedOptionClass="text-[#333333] text-sm"
            :disabled="pop.status === 'disabled'"
          />
        </div>
        <div class="text-base leading-[21px] text-[#333333] space-y-1.5">
          <p class="font-semibold">
            3. Configure your email client
            <span class="font-normal"
              >(e.g. Outlook, Eudora, Netscape Mail)</span
            >
          </p>
          <p class="text-[#3964D0] font-normal">Configuration instructions</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
