<template>
  <div class="label-wrapper py-4 px-4 border-b-[2px] border-[#F1F2F6]">
    <!-- Header -->
    <div class="grid-row header text-[#333333] font-semibold">
      <h4>Labels</h4>
      <h4>Show in label list</h4>
      <h4>Show in message list</h4>
      <h4>Actions</h4>
    </div>
    <button
      class="mt-2 flex justify-center items-center bg-[#F1F2F6] border-[1px] border-[#C2C2C2] w-[142px] h-[33px] rounded-full text-[#525252] text-sm"
      @click="store.commit('emails/SET_NEW_LABEL_MODAL', true)"
    >
      Create new labels
    </button>
    <!-- Labels -->
    <SourceHubEmailsSettingsLabelsLabelItem
      v-for="(label, index) in labelsItems"
      :key="index"
      :label="label"
      @remove="removeLabel"
      @edit="editLabel"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useStore } from 'vuex'

const store = useStore()
const labelsItems = computed(() => {
  return sortLabelsByName(store.state.emails.labelsItems)
})

const sortLabelsByName = (labels) => {
  return labels
    .map((label) => ({
      ...label,
      children: label.children ? sortLabelsByName(label.children) : [],
    }))
    .sort((a, b) => a.name.localeCompare(b.name))
}
const labels = ref([
  {
    id: 1,
    name: 'dad',
    nested: false,
    showInLabelList: true,
    showInMessageList: true,
    children: [],
  },
  {
    id: 2,
    name: 'yyy',
    nested: false,
    showInLabelList: true,
    showInMessageList: true,
    children: [],
  },
  {
    id: 3,
    name: 'ete',
    nested: false,
    showInLabelList: true,
    showInMessageList: true,
    children: [],
  },
  {
    id: 4,
    name: 'ppp',
    nested: false,
    showInLabelList: true,
    showInMessageList: true,
    children: [
      {
        id: 5,
        name: 'ttt12',
        nested: true,
        showInLabelList: true,
        showInMessageList: true,
        children: [],
      },
    ],
  },
  {
    id: 6,
    name: 'iii',
    nested: false,
    showInLabelList: true,
    showInMessageList: true,
    children: [],
  },
])

const removeLabel = (labelToRemove) => {
  const recursiveRemove = (arr) =>
    arr.filter((label) => {
      if (label === labelToRemove) return false
      if (label.children) label.children = recursiveRemove(label.children)
      return true
    })

  labels.value = recursiveRemove(labels.value)
}

const editLabel = (label) => {
  console.log('Editing label:', label)
  store.commit('emails/SET_EDIT_LABEL_MODAL', true)
  store.commit('emails/SET_NEW_LABEL', label)
  // const newName = prompt('Edit label name:', label.name)
  // if (newName) label.name = newName
}

const createLabel = () => {
  const name = prompt('Enter new label name:')
  if (name) {
    labels.value.push({
      name,
      conversations: 0,
      showInLabelList: true,
      showInMessageList: true,
      children: [],
    })
  }
}
</script>

<style scoped>
.grid-row {
  display: grid;
  grid-template-columns: 250px 300px 300px 1fr;
  align-items: start;
  /* gap: 10px;
  padding: 6px 0;
  border-bottom: 1px solid #ccc; */
}

.create-button {
  margin-top: 12px;
  padding: 5px 10px;
}
</style>
