<template>
  <div>
    <div class="grid-row label-row mt-[18px] text-[#333333]">
      <!-- Label Name -->
      <div :style="{ paddingLeft: `${depth * 20}px` }">
        {{ label.name }}
      </div>

      <!-- Show in label list -->
      <div class="flex space-x-4" :class="label.nested ? 'invisible' : ''">
        <button
          :class="
            label.showInLabelList
              ? 'text-[#333333] font-semibold'
              : 'text-[#3964D0]'
          "
          @click.prevent="
            store.commit('emails/SET_LABEL_ITEMS_SHOW_HIDE', {
              id: label.id,
              showInLabelList: true,
            })
          "
        >
          show
        </button>
        <button
          :class="
            !label.showInLabelList
              ? 'text-[#333333] font-semibold'
              : 'text-[#3964D0]'
          "
          @click.prevent="
            store.commit('emails/SET_LABEL_ITEMS_SHOW_HIDE', {
              id: label.id,
              showInLabelList: false,
            })
          "
        >
          hide
        </button>
      </div>

      <!-- Show in message list -->
      <div class="flex space-x-4">
        <button
          :class="
            label.showInMessageList
              ? 'text-[#333333] font-semibold'
              : 'text-[#3964D0]'
          "
          @click.prevent="
            store.commit('emails/SET_LABEL_ITEMS_SHOW_HIDE', {
              id: label.id,
              showInMessageList: true,
            })
          "
        >
          show
        </button>
        <button
          :class="
            !label.showInMessageList
              ? 'text-[#333333] font-semibold'
              : 'text-[#3964D0]'
          "
          @click.prevent="
            store.commit('emails/SET_LABEL_ITEMS_SHOW_HIDE', {
              id: label.id,
              showInMessageList: false,
            })
          "
        >
          hide
        </button>
      </div>

      <!-- Actions -->
      <div class="flex space-x-4">
        <button class="text-[#3964D0]" @click.prevent="$emit('remove', label)">
          remove
        </button>
        <button class="text-[#3964D0]" @click.prevent="$emit('edit', label)">
          edit
        </button>
      </div>
    </div>

    <!-- Recursively render children -->
    <SourceHubEmailsSettingsLabelsLabelItem
      v-for="(child, index) in label.children"
      :key="index"
      :label="child"
      :depth="depth + 1"
      @remove="$emit('remove', $event)"
      @edit="$emit('edit', $event)"
    />
  </div>
</template>

<script setup>
import { useStore } from 'vuex'

defineProps({
  label: Object,
  depth: {
    type: Number,
    default: 0,
  },
})
const store = useStore()
const toggle = (label, key) => {
  label[key] = !label[key]
}
</script>

<style scoped>
.grid-row {
  display: grid;
  grid-template-columns: 250px 300px 300px 1fr;
  align-items: start;
}

.sub-text {
  /* font-size: 12px; */
  color: #333333;
}
</style>
