<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const grantAccount = computed(() => store.state.emails.selectedGrantAccount)
</script>

<template>
  <div
    class="bg-white w-1/2 lg:min-w-[678px] min-w-[578px] max-w-[678px] z-1 rounded-2xl absolute transform top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
  >
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="px-6 pt-3.5 pb-4 flex justify-between items-center">
        <h2 class="text-[#505050] font-semibold">
          Confirm deletion of granted account
        </h2>
        <SharedIconHubEmailsCrossIcon
          class="w-4 h-4 cursor-pointer"
          @click.stop="
            store.commit('emails/SHOW_GRANT_ACCOUNT_DELETE_MODAL', {
              show: false,
              id: '',
              grantAccount: null,
            })
          "
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="px-6 py-4">
        <p>
          You are about to revoke access to your account from
          {{ grantAccount.email }}. Are you sure?
        </p>
      </div>
    </div>
    <div class="flex justify-end items-center space-x-2 px-6 pt-[18px] pb-4">
      <button
        class="w-[104px] h-[35px] rounded-full flex justify-center items-center border border-[#4A71D4] text-[#4A71D4]"
        @click.stop="
          store.commit('emails/SHOW_GRANT_ACCOUNT_DELETE_MODAL', {
            show: false,
            id: '',
            grantAccount: null,
          })
        "
      >
        Cancel
      </button>
      <button
        class="w-[104px] h-[35px] rounded-full flex justify-center items-center bg-[#4A71D4] text-[#FFFFFF]"
        @click.stop="
          store.commit('emails/SHOW_GRANT_ACCOUNT_DELETE_MODAL', {
            show: false,
            id: grantAccount.id,
            grantAccount: null,
          })
        "
      >
        Ok
      </button>
    </div>
  </div>
</template>

<style scoped></style>
