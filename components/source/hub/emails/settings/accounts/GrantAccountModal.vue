<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core'
import { required, email } from '@vuelidate/validators'
import { useStore } from 'vuex'

const store = useStore()
const importEmail = ref('')

const rules = {
  importEmail: {
    required,
    email,
  },
}
const v$ = useVuelidate(rules, { importEmail })
const { sanitizedOutput } = useInputValidations()
const steps = ref(1)
const nextStep = () => {
  v$.value.$touch()
  if (!v$.value.importEmail.$invalid) {
    if (steps.value === 1) {
      steps.value = 2
    }
  }
}
</script>

<template>
  <div
    class="bg-white w-1/2 lg:min-w-[678px] min-w-[578px] max-w-[678px] z-1 rounded-2xl absolute transform top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
  >
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="px-6 pt-3.5 pb-4 flex justify-between items-center">
        <h2 class="text-[#505050] font-semibold">
          Grant access to your account
        </h2>
        <SharedIconHubEmailsCrossIcon
          class="w-4 h-4 cursor-pointer"
          @click.stop="
            store.commit('emails/SHOW_GRANT_ACCOUNT_MODAL', {
              show: false,
              email: '',
            })
          "
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div v-if="steps === 1" class="px-6 pt-4 pb-4">
        <div class="flex flex-col space-y-4">
          <p class="text-xl text-[#707070]">
            Specify a Google Account holder to access your account.
          </p>
          <div class="flex flex-col space-y-2">
            <p class="text-[#333333]">
              This person will be able to sign in to your account to read,
              delete and send emails, and edit Gmail settings on your behalf.
              They will not be able to modify your Google Account settings nor
              change your password.
            </p>
            <div class="flex flex-col space-y-2">
              <p class="text-[#333333]">Email address</p>
              <InputsEmailInput
                id="email"
                v-model="v$.importEmail.$model"
                email-input="emailInput"
                :error-message="v$.importEmail.$errors"
                placeHolder=""
                color="#333333"
                background="#F1F2F6"
                autocomplete="email"
                :error="v$.importEmail.$error"
                @update:modelValue="importEmail = sanitizedOutput($event)"
              />
            </div>
          </div>
        </div>
      </div>
      <div v-if="steps === 2" class="px-6 pt-4 pb-4">
        <div class="flex flex-col space-y-4">
          <p class="text-xl text-[#707070]">Granted</p>
          <p class="text-[#333333]">
            A confirmation request has been sent to {{ importEmail }}. Once they
            accept the request, they'll have access to your mail. You can always
            revoke this later.
          </p>
        </div>
      </div>
    </div>
    <div class="flex justify-end items-center space-x-2 px-6 pt-[18px] pb-4">
      <button
        v-if="steps === 1 || steps === 2"
        class="w-[104px] h-[35px] rounded-full flex justify-center items-center border border-[#4A71D4] text-[#4A71D4]"
        @click.stop="
          store.commit('emails/SHOW_GRANT_ACCOUNT_MODAL', {
            show: false,
            email: '',
          })
        "
      >
        Cancel
      </button>
      <button
        v-if="steps === 1"
        class="w-[135px] h-[35px] rounded-full flex justify-center items-center bg-[#4A71D4] text-[#FFFFFF]"
        @click="nextStep()"
      >
        Next step »
      </button>
      <button
        v-if="steps === 2"
        class="w-[245px] h-[35px] rounded-full flex justify-center items-center bg-[#4A71D4] text-[#FFFFFF]"
        @click.stop="
          store.commit('emails/SHOW_GRANT_ACCOUNT_MODAL', {
            show: false,
            email: importEmail,
          })
        "
      >
        Send email to grant access
      </button>
    </div>
  </div>
</template>

<style scoped></style>
