<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const importMail = computed(() => store.state.emails.importMail)
const accountList = computed(() => store.state.emails.accountList)
const emailAddressList = computed(() => store.state.emails.emailAddressList)
const grantAccountLists = computed(() => store.state.emails.grantAccountLists)
const selectedMarkAsReadOption = ref('as_read')
const markAsReadOptions = [
  {
    label: 'Mark conversation as read when opened by others',
    value: 'as_read',
    description: '',
  },
  {
    label: `Leave conversation as unread when opened by others`,
    value: 'as_unread',
    description: '',
  },
]
const selectedSenderInformation = ref('address_person')
const senderInformationOptions = [
  {
    label: `Show this address and the person who sent it ('sent by …')`,
    value: 'address_person',
    description: '',
  },
  {
    label: `Show this address only (<EMAIL>)`,
    value: 'address',
    description: '',
  },
]
const extractNameFromEmail = (email: string) => {
  const username = email.split('@')[0]
  const parts = username.split('.')

  if (parts.length === 0) return ''

  const first =
    parts[0].charAt(0).toUpperCase() + parts[0].slice(1).toLowerCase()
  const rest = parts.slice(1).map((part) => part.toLowerCase())

  return [first, ...rest].join(' ')
}
</script>

<template>
  <div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">Import mail and contacts:</p>
          <p class="text-[#4A71D4] !mt-1.5">Learn more</p>
        </div>
        <div class="">
          <p v-if="importMail.length === 0" class="text-[#525252]">
            Import from Yahoo!, Hotmail, AOL, or other webmail or POP3 accounts.
          </p>
          <div v-if="importMail && importMail.length > 0" class="">
            <p
              class="text-[#333333] font-semibold"
              v-for="mail in importMail"
              :key="mail"
            >
              {{ mail }}
            </p>
            <p class="text-[#525252] !mt-1.5">Imported successfully.</p>
          </div>
          <p
            class="text-[#4A71D4] font-semibold !mt-1.5 cursor-pointer"
            @click.stop="
              store.commit('emails/SET_SHOW_IMPORT_MAIL_MODAL', {
                show: true,
                email: '',
              })
            "
          >
            Import mail and contacts
          </p>
        </div>
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">Send mail as:</p>
          <p class="text-sm text-[#525252] !mt-1.5">
            (Use Gmail to send from your other email addresses)
          </p>
          <p class="text-[#4A71D4] !mt-1">Learn more</p>
        </div>
        <div class="">
          <div v-if="emailAddressList && emailAddressList.length > 0" class="">
            <p
              class="text-[#333333] font-semibold"
              v-for="mail in emailAddressList"
              :key="mail"
            >
              {{ extractNameFromEmail(mail) }} {{ '<' + mail + '>' }}
            </p>
            <p class="text-[#525252] !mt-1.5">Added successfully.</p>
          </div>
          <!-- <p class="text-[#333333] font-semibold">
            {{ 'Sharparchive <<EMAIL>>' }}
          </p> -->
          <p
            class="text-[#4A71D4] font-semibold !mt-3.5 cursor-pointer"
            @click.stop="
              store.commit('emails/SET_SHOW_ANOTHER_EMAIL_MODAL', {
                show: true,
                email: '',
              })
            "
          >
            Add another email address
          </p>
        </div>
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">
            Check email from other accounts:
          </p>
          <p class="text-[#4A71D4] !mt-1.5">Learn more</p>
        </div>
        <div class="">
          <div v-if="accountList && accountList.length > 0" class="">
            <p
              class="text-[#333333] font-semibold"
              v-for="mail in accountList"
              :key="mail"
            >
              {{ mail }}
            </p>
            <p class="text-[#525252] !mt-1.5">Added successfully.</p>
          </div>
          <p
            class="text-[#4A71D4] font-semibold cursor-pointer"
            @click.stop="
              store.commit('emails/SET_SHOW_ADD_ACCOUNT_MODAL', {
                show: true,
                email: '',
              })
            "
          >
            Add an email account
          </p>
        </div>
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="grid grid-cols-[280px_1fr] gap-x-4 py-4">
        <div>
          <p class="text-[#333333] font-semibold">
            Grant access to your account:
          </p>
          <p class="text-sm text-[#525252] !mt-1.5">
            (Allow others to read and send mail on your behalf)
          </p>
          <p class="text-[#4A71D4] !mt-1">Learn more</p>
        </div>
        <div class="">
          <div
            class="mb-3.5 w-full"
            v-if="grantAccountLists && grantAccountLists.length > 0"
          >
            <div
              v-for="grantAccountList in grantAccountLists"
              :key="grantAccountList.id"
              class="flex justify-between items-center w-full"
            >
              <p class="text-[#333333] font-semibold">
                {{ extractNameFromEmail(grantAccountList.email) }}
                {{ '<' + grantAccountList.email + '>' }}
              </p>
              <div class="flex space-x-6">
                <p class="text-[#C2C2C2]">pending</p>
                <p
                  class="text-[#3964D0] cursor-pointer"
                  @click.stop="
                    store.commit('emails/SHOW_GRANT_ACCOUNT_DELETE_MODAL', {
                      show: true,
                      id: '',
                      grantAccount: grantAccountList,
                    })
                  "
                >
                  Delete
                </p>
              </div>
            </div>
          </div>
          <p
            class="text-[#4A71D4] font-semibold cursor-pointer"
            @click.stop="
              store.commit('emails/SHOW_GRANT_ACCOUNT_MODAL', {
                show: true,
                email: '',
              })
            "
          >
            Add another account
          </p>
          <div class="mt-[22px]">
            <p class="text-[#333333] font-semibold">Mark as read</p>
            <InputsRadioOptionGroup
              class="mt-1.5"
              v-model="selectedMarkAsReadOption"
              :options="markAsReadOptions"
            />
          </div>
          <div class="mt-[22px]">
            <p class="text-[#333333] font-semibold">Sender information</p>
            <InputsRadioOptionGroup
              class="mt-1.5"
              v-model="selectedSenderInformation"
              :options="senderInformationOptions"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
