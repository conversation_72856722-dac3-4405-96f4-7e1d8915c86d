<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core'
import { required, email } from '@vuelidate/validators'
import { useStore } from 'vuex'

const store = useStore()
const emailAccount = ref({
  importEmail: '',
  userName: '',
  replyEmail: '',
})
const rules = {
  importEmail: {
    required,
    email,
  },
  userName: {
    required,
  },
  replyEmail: {
    required,
    email,
  },
}
const v$ = useVuelidate(rules, emailAccount)
const { sanitizedOutput } = useInputValidations()
const treatAlias = ref(false)
const replyAddress = ref(false)
const steps = ref(1)
const nextStep = () => {
  v$.value.$touch()
  if (
    !v$.value.importEmail.$invalid &&
    !v$.value.userName.$invalid &&
    (!v$.value.userName.$invalid || !replyAddress.value)
  ) {
    if (steps.value === 1) {
      steps.value = 2
    } else if (steps.value === 2) {
      steps.value = 3
    }
  }
}
const preStep = () => {
  if (steps.value === 2) {
    steps.value = 1
  }
}
const finalStep = () => {
  store.commit('emails/SET_SHOW_ANOTHER_EMAIL_MODAL', {
    show: false,
    email: emailAccount.value.importEmail,
  })
}
</script>

<template>
  <div
    class="bg-white w-1/2 lg:min-w-[678px] min-w-[578px] max-w-[678px] z-1 rounded-2xl absolute transform top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
  >
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="px-6 pt-3.5 pb-4 flex justify-between items-center">
        <h2 class="text-[#505050] font-semibold">Add another email address</h2>
        <SharedIconHubEmailsCrossIcon
          class="w-4 h-4 cursor-pointer"
          @click.stop="
            store.commit('emails/SET_SHOW_ANOTHER_EMAIL_MODAL', {
              show: false,
              email: '',
            })
          "
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div v-if="steps === 1" class="px-6 pt-3.5 pb-4">
        <p class="text-xl text-[#707070]">
          Add another email address that you own
        </p>
        <div class="mt-[22px]">
          <p class="text-[#333333]">
            Enter information about your other email address.
          </p>
          <p class="!mt-1.5 text-sm text-[#707070]">
            (your name and email address will be shown on mail that you send)
          </p>
        </div>
        <div class="mt-3.5">
          <div class="flex flex-col space-y-1.5">
            <label class="text-[#333333]">Name</label>
            <InputsTextInput
              id="username"
              v-model="v$.userName.$model"
              email-input="emailInput"
              :error-message="v$.userName.$errors"
              placeHolder=""
              color="#333333"
              background="#F1F2F6"
              :error="v$.userName.$error"
            />
          </div>
          <div class="flex flex-col space-y-1.5 mt-1.5">
            <label class="text-[#333333]">Email address</label>
            <InputsEmailInput
              id="email"
              v-model="v$.importEmail.$model"
              email-input="emailInput"
              :error-message="v$.importEmail.$errors"
              placeHolder=""
              color="#333333"
              background="#F1F2F6"
              autocomplete="email"
              :error="v$.importEmail.$error"
              @update:modelValue="
                emailAccount.importEmail = sanitizedOutput($event)
              "
            />
          </div>
          <div class="mt-1.5 flex flex-col space-y-1.5">
            <label
              class="relative flex space-x-2 items-start cursor-pointer"
              for="treat_alias"
            >
              <div class="w-4 h-4 relative mt-0.5">
                <input
                  ref="treat_alias"
                  id="treat_alias"
                  type="checkbox"
                  :checked="treatAlias ? true : false"
                  class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                  :class="treatAlias ? '' : 'border border-[#333333]'"
                />
                <ClientOnly>
                  <fa
                    class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                    :icon="['fas', 'check']"
                  />
                </ClientOnly>
              </div>
              <div class="">
                <p class="text-[#525252] leading-5">
                  Treat as an alias.
                  <span class="text-[#3964D0]">Learn more</span>
                </p>
              </div>
            </label>
            <p
              v-if="!replyAddress"
              class="text-[#3964D0] cursor-pointer"
              @click="replyAddress = true"
            >
              Specify a different "reply-to" address
              <span class="text-[#525252]">(optional)</span>
            </p>
          </div>
          <div v-if="replyAddress" class="mt-4">
            <div class="flex flex-col space-y-1.5">
              <label class="text-[#333333]">Reply-to address</label>
              <InputsEmailInput
                id="email"
                v-model="v$.replyEmail.$model"
                email-input="emailInput"
                :error-message="v$.replyEmail.$errors"
                placeHolder=""
                color="#333333"
                background="#F1F2F6"
                autocomplete="email"
                :error="v$.replyEmail.$error"
                @update:modelValue="
                  emailAccount.replyEmail = sanitizedOutput($event)
                "
              />
              <p class="text-[#525252] text-sm">
                (a reply to mail you send will go to this address.
                <span class="text-[#3964D0]">Learn more</span>)
              </p>
            </div>
          </div>
        </div>
      </div>
      <div v-if="steps === 2" class="px-6 pt-3.5 pb-4">
        <p class="text-xl text-[#707070]">
          Add another email address that you own
        </p>
        <div class="mt-[22px]">
          <p class="text-[#333333] font-semibold">Verify your email address</p>
          <p class="text-[#333333] !mt-2">
            Before you can send mail as
            <span class="font-semibold">{{ emailAccount.importEmail }}</span
            >, we need to verify that you own this email address. To perform the
            verification, click "Send Verification". We will then send an email
            to <EMAIL> with instructions on how to verify your
            address.
          </p>
        </div>
      </div>
      <div v-if="steps === 3" class="px-6 pt-3.5 pb-4">
        <p class="text-xl text-[#707070]">
          Add another email address that you own
        </p>
        <div class="mt-[22px]">
          <p class="text-[#333333] font-semibold">
            Confirm verification and add your email address
          </p>
          <p class="text-[#333333] !mt-2">
            An email with a confirmation link was sent to
            <span class="font-semibold">{{ emailAccount.importEmail }}</span
            >. <span class="text-[#3964D0]">[Resend email]</span> To add your
            email address, click on the link in the confirmation email.
          </p>
        </div>
      </div>
    </div>
    <div class="flex justify-end items-center space-x-2 px-6 pt-[18px] pb-4">
      <button
        v-if="steps === 1 || steps === 2"
        class="w-[104px] h-[35px] rounded-full flex justify-center items-center border border-[#4A71D4] text-[#4A71D4]"
        @click.stop="
          store.commit('emails/SET_SHOW_ANOTHER_EMAIL_MODAL', {
            show: false,
            email: '',
          })
        "
      >
        Cancel
      </button>
      <button
        v-if="steps === 2"
        class="w-[104px] h-[35px] rounded-full flex justify-center items-center border border-[#4A71D4] text-[#4A71D4]"
        @click="preStep()"
      >
        « Back
      </button>
      <button
        v-if="steps === 1"
        class="w-[135px] h-[35px] rounded-full flex justify-center items-center bg-[#4A71D4] text-[#FFFFFF]"
        @click="nextStep()"
      >
        Next »
      </button>
      <button
        v-if="steps === 2"
        class="w-[170px] h-[35px] rounded-full flex justify-center items-center bg-[#4A71D4] text-[#FFFFFF]"
        @click="nextStep()"
      >
        Send Verification
      </button>
      <button
        v-if="steps === 3"
        class="w-[150px] h-[35px] rounded-full flex justify-center items-center bg-[#4A71D4] text-[#FFFFFF]"
        @click.stop="finalStep"
      >
        Close window
      </button>
    </div>
  </div>
</template>

<style scoped></style>
