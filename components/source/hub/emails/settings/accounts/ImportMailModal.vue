<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core'
import { required, email } from '@vuelidate/validators'
import { useStore } from 'vuex'

const store = useStore()
const importEmail = ref<string>('')
const rules = {
  importEmail: {
    required,
    email,
  },
}
const v$ = useVuelidate(rules, { importEmail })
const { sanitizedOutput } = useInputValidations()
const steps = ref(1)
const checking = ref(false)
const nextStep = () => {
  if (steps.value === 1) {
    steps.value = 2
  } else if (steps.value === 2) {
    checking.value = true
    setTimeout(() => {
      steps.value = 3
      checking.value = false
    }, 2000)
  }
}
const finalStep = () => {
  if (steps.value === 3) {
    checking.value = true
    setTimeout(() => {
      steps.value = 4
      checking.value = false
    }, 2000)
  }
}
const importOptions = ref([
  {
    id: 1,
    text: 'Import contacts',
    checked: true,
  },
  {
    id: 2,
    text: 'Import mail',
    checked: true,
  },
  {
    id: 3,
    text: 'Import new mail for next 30 days',
    checked: true,
  },
])
const checkUncheckImportOptions = (id: number) => {
  importOptions.value.forEach((importOption) => {
    if (importOption.id === id) {
      importOption.checked = !importOption.checked
    }
  })
}
</script>

<template>
  <div
    class="bg-white w-1/2 max-w-[678px] z-1 rounded-2xl absolute transform top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
  >
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="px-6 pt-3.5 pb-4 flex justify-between items-center">
        <h2 class="text-[#505050] font-semibold">Import mail and contacts</h2>
        <SharedIconHubEmailsCrossIcon
          class="w-4 h-4 cursor-pointer"
          @click.stop="
            store.commit('emails/SET_SHOW_IMPORT_MAIL_MODAL', {
              show: false,
              email: '',
            })
          "
        />
      </div>
    </div>
    <div
      v-if="steps === 1 || steps === 2"
      class="border-b-[2px] border-[#F1F2F6]"
    >
      <div class="px-6 pt-3.5 pb-4">
        <h2 class="text-xl text-[#707070]">
          Step 1: Sign into your other email account
        </h2>
        <Transition name="globalFadeInFadeOut" mode="out-in">
          <div v-if="steps === 1" class="mt-[22px]">
            <div>
              <p class="text-[#333333]">
                What account do you want to import from?
              </p>

              <InputsEmailInput
                id="email"
                v-model="v$.importEmail.$model"
                email-input="emailInput"
                :error-message="v$.importEmail.$errors"
                placeHolder=""
                color="#333333"
                background="#F1F2F6"
                autocomplete="email"
                :error="v$.importEmail.$error"
                @update:modelValue="importEmail = sanitizedOutput($event)"
              />
            </div>
            <p class="text-[#707070] !mt-3.5">
              For example: <span class="font-semibold"><EMAIL></span>
            </p>
          </div>
          <div v-else-if="steps === 2" class="mt-[24px]">
            <p class="text-[#3964D0]">
              Sign in to your other email account to confirm import of emails
              and/or contacts. Press
              <span class="font-semibold">Continue</span> and follow the
              instructions in the pop-up window.
            </p>
            <p class="!mt-[22px] text-[#333333]">
              Importing is powered by ShuttleCloud. By clicking "Continue", you
              agree to ShuttleCloud's
              <span class="text-[#3964D0]">Terms of Use</span> and
              <span class="text-[#3964D0]">Privacy Policy</span>. During import,
              the connection to the service <NAME_EMAIL>
              may be unencrypted.
            </p>
          </div>
        </Transition>
      </div>
    </div>
    <div v-else-if="steps === 3" class="border-b-[2px] border-[#F1F2F6]">
      <div class="px-6 pt-3.5 pb-4">
        <h2 class="text-xl text-[#707070]">Step 2: Import options</h2>
        <div class="mt-[24px]">
          <p class="text-[#3964D0]">
            Select the import options for
            <span class="text-[#3964D0] font-semibold italic"
              ><EMAIL></span
            >:
          </p>
          <div class="flex flex-col space-y-1.5 mt-3">
            <label
              v-for="importOption in importOptions"
              :key="importOption.id"
              class="w-full relative flex space-x-2 items-start cursor-pointer"
              :for="importOption.id"
            >
              <div class="w-4 h-4 relative mt-0.5">
                <input
                  :ref="importOption.id"
                  :id="importOption.id"
                  type="checkbox"
                  :checked="importOption.checked ? true : false"
                  class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                  :class="[
                    importOption.checked ? '' : 'border border-[#333333]',
                  ]"
                  @input="checkUncheckImportOptions(importOption.id)"
                />
                <ClientOnly>
                  <fa
                    class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                    :icon="['fas', 'check']"
                  />
                </ClientOnly>
              </div>
              <div class="inline-block">
                <p class="text-[#3964D0]">
                  {{ importOption.text }}
                </p>
              </div>
            </label>
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="steps === 4" class="border-b-[2px] border-[#F1F2F6]">
      <div class="px-6 pt-3.5 pb-4">
        <h2 class="text-xl text-[#707070]">Step 3: Finish</h2>
        <div class="mt-[24px]">
          <p class="text-[#3964D0] font-semibold">
            Your messages and contacts are being imported.
          </p>
          <p class="!mt-[14px] text-[#3964D0]">
            It may take several hours (sometimes up to 2 days) before you start
            to see imported messages.
          </p>
          <p class="!mt-[14px] text-[#3964D0]">
            You can close this window and continue using Gmail, or even log out
            and close your browser – we'll continue importing your mail and/or
            contacts in the background. To check the status of your import, look
            under Settings > Accounts and Import.
          </p>
        </div>
      </div>
    </div>
    <div class="flex justify-between items-center px-6 pt-[18px] pb-4">
      <p
        :class="checking ? 'visible ' : ' invisible'"
        class="text-[#3964D0] font-semibold animate-pulse"
      >
        Checking...
      </p>
      <div class="flex justify-end items-center space-x-2">
        <button
          v-if="steps !== 4"
          :disabled="checking"
          class="w-[104px] h-[35px] rounded-full flex justify-center items-center"
          :class="
            checking
              ? 'border border-[#C2C2C2] text-[#C2C2C2]'
              : 'border border-[#4A71D4] text-[#4A71D4]'
          "
          @click.stop="
            store.commit('emails/SET_SHOW_IMPORT_MAIL_MODAL', {
              show: false,
              email: '',
            })
          "
        >
          Cancel
        </button>
        <button
          v-if="(steps === 1 || steps === 2) && !checking"
          class="w-[104px] h-[35px] rounded-full flex justify-center items-center"
          :class="
            (!importEmail || v$.importEmail.$error) && steps === 1
              ? 'bg-[#F1F2F6] text-[#C2C2C2]'
              : 'bg-[#4A71D4] text-[#FFFFFF]'
          "
          @click="nextStep()"
        >
          Continue
        </button>
        <button
          v-if="steps === 3 && !checking"
          class="w-[135px] h-[35px] rounded-full flex justify-center items-center bg-[#4A71D4] text-[#FFFFFF]"
          @click="finalStep()"
        >
          Start Import
        </button>
        <button
          v-if="steps === 4"
          class="w-[104px] h-[35px] rounded-full flex justify-center items-center bg-[#4A71D4] text-[#FFFFFF]"
          @click.stop="
            store.commit('emails/SET_SHOW_IMPORT_MAIL_MODAL', {
              show: false,
              email: importEmail,
            })
          "
        >
          Ok
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.toggle-check-1 {
  &:checked {
    @apply bg-[#4A71D4];
  }
  &:checked + .check-1 {
    @apply opacity-100;
    color: white;
  }
}
</style>
