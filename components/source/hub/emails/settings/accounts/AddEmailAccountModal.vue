<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core'
import { required, email } from '@vuelidate/validators'
import { useStore } from 'vuex'

const store = useStore()
const emailAccount = ref({
  importEmail: '',
  userName: '',
  password: '',
  popServer: 'mail.gmail.com',
})
// const importEmail = ref<string>('')
const rules = {
  importEmail: {
    required,
    email,
  },
  userName: {
    required,
  },
  password: {
    required,
  },
  popServer: {
    required,
  },
}
const v$ = useVuelidate(rules, emailAccount)
const { sanitizedOutput } = useInputValidations()
const steps = ref(1)
const selectedOption = ref('1')
const options = [
  {
    label: 'Link accounts with Gmailify',
    value: '1',
    description: '',
  },
  {
    label: 'Import emails from my other account (POP3)',
    value: '2',
    description: '',
  },
]
const nextStep = () => {
  v$.value.$touch()
  if (!v$.value.importEmail.$invalid) {
    if (steps.value === 1) {
      steps.value = 2
      v$.value.$reset()
    } else if (
      steps.value === 2 &&
      selectedOption.value === '1'
    ) {
      steps.value = 3
      v$.value.$reset()
      
    } else if (steps.value === 2 && selectedOption.value === '2') {
      steps.value = 4
      v$.value.$reset()
    }
  }
}
const preStep = () => {
  if (steps.value === 2) {
    steps.value = 1
  } else if (steps.value === 3) {
    steps.value = 2
  } else if (steps.value === 4) {
    steps.value = 2
  }
}
const linkAccount = () => {
  v$.value.$touch()
  if (!v$.value.password.$invalid) {
    store.commit('emails/SET_SHOW_ADD_ACCOUNT_MODAL', {
      show: false,
      email: emailAccount.value.importEmail,
    })
  }
}
const finalStep = () => {
  v$.value.$touch()
  if (!v$.value.$invalid) {
    store.commit('emails/SET_SHOW_ADD_ACCOUNT_MODAL', {
      show: false,
      email: emailAccount.value.importEmail,
    })
  }
}
const setUserName = (event) => {
  emailAccount.value.userName = event.split('@')[0]
  incomingMessagesOptions.value.forEach((incomingMessagesOption, index) => {
    if (index === 0) {
      incomingMessagesOption.text = emailAccount.value.importEmail
    }
  })
}
const popServer = ref('mail.gmail.com')
const portNumbers = [
  { id: 1, text: '110' },
  { id: 2, text: '120' },
  { id: 3, text: '130' },
]
const incomingMessagesOptions = ref([
  { id: 1, text: '' },
  { id: 2, text: '<EMAIL>' },
  { id: 3, text: '<EMAIL>' },
])
const retrievedMessage = ref(false)
const secureConnection = ref(false)
const incomingMessages = ref(false)
const archiveMessages = ref(false)
</script>

<template>
  <div
    class="bg-white w-1/2 lg:min-w-[678px] min-w-[578px] max-w-[678px] z-1 rounded-2xl absolute transform top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
  >
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="px-6 pt-3.5 pb-4 flex justify-between items-center">
        <h2 class="text-[#505050] font-semibold">
          {{
            steps === 3
              ? 'Link another email address to your Gmail account'
              : 'Add an email account'
          }}
        </h2>
        <SharedIconHubEmailsCrossIcon
          class="w-4 h-4 cursor-pointer"
          @click.stop="
            store.commit('emails/SET_SHOW_ADD_ACCOUNT_MODAL', {
              show: false,
              email: '',
            })
          "
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div v-if="steps === 1" class="px-6 pt-3.5 pb-4">
        <h2 class="text-xl text-[#707070]">
          Enter the email address that you would like to add.
        </h2>
        <div class="mt-[22px]">
          <p class="text-[#333333]">Email address</p>
          <InputsEmailInput
            id="email"
            v-model="v$.importEmail.$model"
            email-input="emailInput"
            :error-message="v$.importEmail.$errors"
            placeHolder=""
            color="#333333"
            background="#F1F2F6"
            autocomplete="email"
            :error="v$.importEmail.$error"
            @update:modelValue="
              setUserName($event),
                (emailAccount.importEmail = sanitizedOutput($event))
            "
          />
        </div>
      </div>
      <div v-else-if="steps === 2" class="px-6 pt-3.5 pb-4">
        <h2 class="text-xl text-[#707070]">
          Enter the email address that you would like to add.
        </h2>
        <div class="mt-6">
          <p class="text-[#333333]">
            You can either import emails from {{ emailAccount.importEmail }} to
            your Gmail inbox, or link the accounts using Gmailify. With
            Gmailify, you keep both email addresses but can manage emails from
            both using your Gmail inbox.
            <span class="text-[#3964D0]">More about Gmailify</span>
          </p>
          <InputsRadioOptionGroup
            class="mt-3"
            v-model="selectedOption"
            :options="options"
          />
        </div>
      </div>
      <div v-else-if="steps === 3" class="px-6 pt-3.5 pb-4">
        <h2 class="text-xl text-[#707070]">
          Enter the credentials for your Gmail account.
        </h2>
        <div class="mt-[22px]">
          <div class="grid grid-cols-[110px_1fr] gap-x-[7px]">
            <p class="text-[#333333]">Provider:</p>
            <p class="text-[#333333] font-semibold">Gmail</p>
          </div>
          <div class="grid grid-cols-[110px_1fr] gap-x-[7px]">
            <p class="text-[#333333]">Email address:</p>
            <p class="text-[#333333] font-semibold">
              {{ emailAccount.importEmail }}
            </p>
          </div>
          <div class="grid grid-cols-1 gap-y-[8px]">
            <p class="text-[#333333]">Password:</p>
            <InputsTextInput
              id="password"
              v-model="v$.password.$model"
              email-input="emailInput"
              :error-message="v$.password.$errors"
              placeHolder=""
              color="#333333"
              background="#F1F2F6"
              :error="v$.password.$error"
            />
            <!-- <input
              type="text"
              class="w-full h-[43px] py-2 px-6 rounded-full border-none outline-none bg-[#F1F2F6] text-[#333333]"
            /> -->
          </div>
        </div>
      </div>
      <div v-else-if="steps === 4" class="px-6 pt-3.5 pb-4">
        <h2 class="text-xl text-[#707070]">Enter the mail settings</h2>
        <div class="mt-[22px]">
          <p class="text-[#333333]">
            Enter the mail <NAME_EMAIL>.
            <span class="text-[#3964D0]">Learn more</span>
          </p>
          <div class="grid grid-cols-[110px_1fr] gap-x-[7px] mt-[14px]">
            <p class="text-[#333333]">Email address:</p>
            <p class="text-[#333333] font-semibold">
              {{ emailAccount.importEmail }}
            </p>
          </div>
          <!-- <div class="grid grid-cols-[105px_1fr] gap-x-[7px]">
            <p class="text-[#333333]">Email address:</p>
            <p class="text-[#333333] font-semibold">{{ importEmail }}</p>
          </div> -->
          <div class="grid grid-cols-1 gap-y-[8px] mt-[6px]">
            <p class="text-[#333333]">Username:</p>
            <InputsTextInput
              id="username"
              v-model="v$.userName.$model"
              email-input="emailInput"
              :error-message="v$.userName.$errors"
              placeHolder=""
              color="#333333"
              background="#F1F2F6"
              :error="v$.userName.$error"
            />
          </div>
          <div class="grid grid-cols-1 gap-y-[8px] mt-1.5">
            <p class="text-[#333333]">Password:</p>
            <InputsTextInput
              id="password"
              v-model="v$.password.$model"
              email-input="emailInput"
              :error-message="v$.password.$errors"
              placeHolder=""
              color="#333333"
              background="#F1F2F6"
              :error="v$.password.$error"
            />
          </div>
          <div class="grid grid-cols-[1fr_0.3fr] gap-x-[8px] mt-1.5">
            <div class="flex flex-col space-y-2">
              <p class="text-[#333333]">POP Server:</p>
              <InputsTextInput
                id="pop_server"
                v-model="v$.popServer.$model"
                email-input="emailInput"
                :error-message="v$.popServer.$errors"
                placeHolder=""
                color="#333333"
                background="#F1F2F6"
                :error="v$.popServer.$error"
              />
            </div>
            <div>
              <p class="text-[#333333]">Port:</p>
              <BaseDropsDown
                class="rounded-full language-dropdown mt-2"
                :options="portNumbers"
                labelKey="text"
                placeholder="110"
                :menuWidth="202"
                :menuHeight="43"
                :dropdownWidth="202"
                :dropdownMaxHeight="290"
                menuBgColor="#F1F2F6"
                menuTextColor="#333333"
                dropsdownTextColor="#333333"
                scrollbarTrackColor="#a1cdff50"
                scrollbarThumbColor="#a1cdff"
                hoverColor="#4A71D4"
                hoverTextColor="#FFFFFF"
              />
            </div>
          </div>
          <div class="mt-[14px] flex flex-col space-y-1.5">
            <label
              class="w-full relative flex space-x-2 items-start cursor-pointer"
              for="retrieved_message"
            >
              <div class="w-4 h-4 relative mt-0.5">
                <input
                  ref="retrieved_message"
                  id="retrieved_message"
                  type="checkbox"
                  :checked="retrievedMessage ? true : false"
                  class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                  :class="retrievedMessage ? '' : 'border border-[#333333]'"
                />
                <ClientOnly>
                  <fa
                    class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                    :icon="['fas', 'check']"
                  />
                </ClientOnly>
              </div>
              <div class="inline-block">
                <p class="text-[#333333] leading-5">
                  Leave a copy of retrieved message on the server.<span
                    class="text-[#3964D0]"
                  >
                    Learn more</span
                  >
                </p>
              </div>
            </label>
            <label
              class="w-full relative flex space-x-2 items-start cursor-pointer"
              for="secure_connection"
            >
              <div class="w-4 h-4 relative mt-0.5">
                <input
                  ref="secure_connection"
                  id="secure_connection"
                  type="checkbox"
                  :checked="secureConnection ? true : false"
                  class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                  :class="secureConnection ? '' : 'border border-[#333333]'"
                />
                <ClientOnly>
                  <fa
                    class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                    :icon="['fas', 'check']"
                  />
                </ClientOnly>
              </div>
              <div class="inline-block">
                <p class="text-[#333333] leading-5">
                  Always use a secure connection (SSL) when retrieving mail.
                </p>
                <p class="text-[#3964D0] leading-5">Learn more</p>
              </div>
            </label>
            <div class="flex space-x-1 items-center">
              <label
                class="relative flex space-x-2 items-start cursor-pointer"
                for="incoming_messages"
              >
                <div class="w-4 h-4 relative mt-0.5">
                  <input
                    ref="incoming_messages"
                    id="incoming_messages"
                    type="checkbox"
                    :checked="incomingMessages ? true : false"
                    class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                    :class="incomingMessages ? '' : 'border border-[#333333]'"
                  />
                  <ClientOnly>
                    <fa
                      class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                      :icon="['fas', 'check']"
                    />
                  </ClientOnly>
                </div>
                <div class="">
                  <p class="text-[#333333] leading-5">
                    Label incoming messages:
                  </p>
                </div>
              </label>
              <BaseDropsDown
                class="border-[#C2C2C2] border rounded-full language-dropdown"
                :options="incomingMessagesOptions"
                labelKey="text"
                :placeholder="emailAccount.importEmail"
                :menuWidth="220"
                :menuHeight="27"
                :dropdownWidth="220"
                :dropdownMaxHeight="290"
                menuBgColor="#FFFFFF"
                menuTextColor="#333333"
                dropsdownTextColor="#333333"
                scrollbarTrackColor="#a1cdff50"
                scrollbarThumbColor="#a1cdff"
                hoverColor="#4A71D4"
                hoverTextColor="#FFFFFF"
              />
            </div>
            <label
              class="w-full relative flex space-x-2 items-start cursor-pointer"
              for="archive_messages"
            >
              <div class="w-4 h-4 relative mt-0.5">
                <input
                  ref="archive_messages"
                  id="archive_messages"
                  type="checkbox"
                  :checked="archiveMessages ? true : false"
                  class="appearance-none w-4 h-4 rounded-sm toggle-check-1"
                  :class="archiveMessages ? '' : 'border border-[#333333]'"
                />
                <ClientOnly>
                  <fa
                    class="text-red-deep w-2.5 h-2.5 absolute left-1/2 top-[57%] transform -translate-x-1/2 -translate-y-1/2 font-normal cursor-pointer opacity-0 check-1"
                    :icon="['fas', 'check']"
                  />
                </ClientOnly>
              </div>
              <div class="flex space-x-1 items-center">
                <p class="text-[#333333] leading-5">
                  Archive incoming messages (Skip the Inbox)
                </p>
              </div>
            </label>
          </div>
        </div>
      </div>
    </div>
    <div class="flex justify-end items-center space-x-2 px-6 pt-[18px] pb-4">
      <button
        class="w-[104px] h-[35px] rounded-full flex justify-center items-center border border-[#4A71D4] text-[#4A71D4]"
        @click.stop="
          store.commit('emails/SET_SHOW_ADD_ACCOUNT_MODAL', {
            show: false,
            email: '',
          })
        "
      >
        Cancel
      </button>
      <button
        v-if="steps === 2 || steps === 3"
        class="w-[104px] h-[35px] rounded-full flex justify-center items-center border border-[#4A71D4] text-[#4A71D4]"
        @click="preStep()"
      >
        « Back
      </button>
      <button
        v-if="steps === 1 || steps === 2 || steps === 3"
        class="w-[135px] h-[35px] rounded-full flex justify-center items-center bg-[#4A71D4] text-[#FFFFFF]"
        @click="steps === 3 ? finalStep() : nextStep()"
      >
        Next »
      </button>
      <button
        v-if="steps === 4"
        class="w-[150px] h-[35px] rounded-full flex justify-center items-center bg-[#4A71D4] text-[#FFFFFF]"
        @click.stop="finalStep"
      >
        Add account »
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.toggle-check-1 {
  &:checked {
    @apply bg-[#4A71D4];
  }
  &:checked + .check-1 {
    @apply opacity-100;
    color: white;
  }
}
</style>
