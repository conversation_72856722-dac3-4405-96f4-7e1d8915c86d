<script setup lang="ts">
import { useStore } from 'vuex'
import SingleFilter from './SingleFilter.vue'

const store = useStore()

const filters = computed(() => store.state.emails.filters)

const showImports = defineModel<boolean>({
  type: Boolean,
  default: false,
})
const showImportWndow = ref(false)
const xmlFile = ref<File | null>(null)

const importedFilters = ref([
  {
    id: 1,
    from: '<EMAIL>',
    to: '<EMAIL>',
    subject: 'Project Update',
    hasTheWord: 'deadline',
    doesNotHaveTheWord: 'delay',
    size: '500',
    sizeOperator: { text: 'greater than', value: 's_ss' },
    sizeUnit: { text: 'MB', value: 's_smb' },
    hasAttachment: true,
    excludeChats: false,
    shouldArchive: true,
    shouldMarkAsRead: false,
    shouldStar: true,
    label: {
      isActive: false,
      value: '',
    },
    forwardTo: {
      isActive: false,
      value: '',
    },
    delete: false,
    shouldNeverSpam: true,
    shouldAlwaysMarkAsImportant: false,
    shouldNeverMarkAsImportant: false,
    smartLabelToApply: {
      isActive: false,
      value: null,
    },
    applyMatchingFilter: true,
    isChecked: false,
  },
  {
    id: 2,
    from: '<EMAIL>',
    to: '<EMAIL>',
    subject: 'Weekly News',
    hasTheWord: 'tech',
    doesNotHaveTheWord: '',
    size: '200',
    sizeOperator: { text: 'less than', value: 's_sl' },
    sizeUnit: { text: 'KB', value: 's_skb' },
    hasAttachment: false,
    excludeChats: true,
    shouldArchive: false,
    shouldMarkAsRead: true,
    shouldStar: false,
    label: {
      isActive: false,
      value: null,
    },
    forwardTo: {
      isActive: false,
      value: null,
    },
    delete: false,
    shouldNeverSpam: false,
    shouldAlwaysMarkAsImportant: true,
    shouldNeverMarkAsImportant: false,
    smartLabelToApply: {
      isActive: false,
      value: '',
    },
    applyMatchingFilter: false,
    isChecked: false,
  },
])

const isImportEnabled = computed(() => {
  return importedFilters.value.some((filter: any) => filter.isChecked)
})

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  xmlFile.value = file ? file : null
}

const selectAllImportedFilters = () => {
  importedFilters.value.forEach((filter: any) => {
    filter.isChecked = true
  })
}
const deSelectAllImportedFilters = () => {
  importedFilters.value.forEach((filter: any) => {
    filter.isChecked = false
  })
}
const handleOpenFile = () => {
  showImportWndow.value = true
  showImports.value = false
}
const cancelImport = () => {
  showImports.value = false
  xmlFile.value = null
}
const handleCloseImportWindow = () => {
  showImportWndow.value = false
  xmlFile.value = null
}

const handleCreateFilters = () => {
  importedFilters.value.forEach((filter: any) => {
    if (filter.isChecked && !filters.value.includes(filter)) {
      store.commit('emails/SET_NEW_FILTER', filter)
    }
  })
  handleCloseImportWindow()
}
</script>

<template>
  <div v-if="showImports || showImportWndow" class="mt-[22px] mb-4">
    <p class="text-[#707070] text-xl font-semibold">Import Filters</p>
    <template v-if="showImports">
      <div class="flex items-center justify-between text-base mt-3.5">
        <p class="font-semibold text-[#333333] leading-[21px]">
          First, choose the file containing filters, then click the 'Open file'
          button:
        </p>
        <button @click="cancelImport" class="text-[#3964D0] leading-[21px]">
          Cancel import
        </button>
      </div>
      <div class="mt-2 flex items-center space-x-10">
        <div class="flex items-center space-x-1">
          <label
            for="file-upload"
            class="cursor-pointer rounded-full bg-[#F1F2F6] text-[#333333] hover:bg-[#d8d9dd]/50 text-sm leading-[19px] py-[7px] px-4"
          >
            Choose File
          </label>
          <input
            id="file-upload"
            type="file"
            class="hidden"
            @change="handleFileChange"
            accept=".xml"
          />
          <span class="text-sm text-[#333333]">
            {{ xmlFile ? xmlFile.name : 'No file chosen' }}
          </span>
        </div>

        <button
          :disabled="!xmlFile"
          class="rounded-full bg-[#F1F2F6] text-[#333333] hover:bg-[#d8d9dd]/50 text-sm leading-[19px] py-[7px] px-4"
          :class="{ 'opacity-60': !xmlFile }"
          @click="handleOpenFile"
        >
          Open file
        </button>
      </div>
    </template>
    <template v-else-if="showImportWndow">
      <div class="flex items-center justify-between text-base mt-3.5">
        <p class="font-semibold text-[#333333] leading-[21px]">
          Next, select the filters to import and click the Create button to
          create them:
        </p>
        <button
          @click="handleCloseImportWindow"
          class="text-[#3964D0] leading-[21px]"
        >
          Close import window
        </button>
      </div>
      <div
        v-if="importedFilters && importedFilters.length > 0"
        class="flex flex-col border-t my-2"
      >
        <div
          v-for="filter in importedFilters"
          :key="filter.id"
          class="flex items-center border-b py-[15px] space-x-4"
        >
          <InputsCheckBoxInput
            :id="filter.id"
            v-model="filter.isChecked"
            checkColor="#4A71D4"
          />
          <SingleFilter :filter="filter" />
        </div>
        <div class="flex items-center text-base leading-[21px] mt-[30px]">
          <span class="text-[#333333] mr-1">Select: </span>
          <button @click="selectAllImportedFilters" class="text-[#3964D0]">
            All
          </button>
          <span>,</span>
          <button
            @click="deSelectAllImportedFilters"
            class="text-[#3964D0] ml-1"
          >
            None
          </button>
        </div>
      </div>
      <div class="mt-2 flex items-center justify-center space-x-5">
        <button
          :disabled="!isImportEnabled"
          class="rounded-full bg-[#F1F2F6] text-[#333333] hover:bg-[#d8d9dd]/50 text-sm leading-[19px] py-[7px] px-4"
          :class="{ 'opacity-60': !isImportEnabled }"
          @click="handleCreateFilters"
        >
          Create filters
        </button>
        <label class="flex items-center space-x-2 text-sm">
          <InputsCheckBoxInput
            id="applyNewFiltersToExistingEmail"
            checkColor="#4A71D4"
          />
          <span>Apply new filters to existing email.</span></label
        >
      </div>
    </template>
  </div>
</template>
