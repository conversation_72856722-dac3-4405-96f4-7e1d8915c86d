<script setup lang="ts">
import { useStore } from 'vuex'
const store = useStore()

const blockedAddresses = computed(() => store.state.emails.blockedAddresses)

const isUnblockEnabled = computed(() => {
  return blockedAddresses.value.some((address: any) => address.isChecked)
})

const selectAllBlockedAddresses = () => {
  blockedAddresses.value.forEach((address: any) => {
    address.isChecked = true
  })
}
const deSelectAllBlockedAddresses = () => {
  blockedAddresses.value.forEach((address: any) => {
    address.isChecked = false
  })
}

const openUnlockAddressModal = (item: { type: string; id?: any }) => {
  store.commit('emails/SET_UNBLOCK_ADDRESS_MODAL', {
    isOpen: true,
    ...item,
  })
}

onMounted(() => {
  const data = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      isChecked: false,
    },
    {
      id: 2,
      name: 'New User',
      email: '<EMAIL>',
      isChecked: false,
    },
  ]
  store.commit('emails/SET_BLOCKED_ADDRESSES', data)
})
</script>

<template>
  <div class="mt-3.5">
    <p class="text-base font-semibold text-[#333333] leading-[21px]">
      The following email addresses are blocked. Messages from these addresses
      will appear in Spam:
    </p>
    <div class="w-full mt-4">
      <div
        v-if="blockedAddresses && blockedAddresses.length > 0"
        class="flex flex-col border-t"
      >
        <div
          v-for="address in blockedAddresses"
          :key="address.id"
          class="flex items-center justify-between border-b py-[15px] space-x-4"
        >
          <div class="flex items-center space-x-4">
            <InputsCheckBoxInput
              :id="address.id"
              v-model="address.isChecked"
              checkColor="#4A71D4"
            />
            <p class="text-[##333333] font-semibold">
              {{ address.name }}
              <span class="font-normal"> &lt;{{ address.email }}&gt; </span>
            </p>
          </div>
          <button
            class="text-[#3964D0]"
            @click="openUnlockAddressModal({ type: 'single', ...address })"
          >
            unblock
          </button>
        </div>
      </div>
      <p v-else class="text-[#333333] text-base leading-[21px]">
        You currently have no blocked addresses.
      </p>
      <div class="flex items-center text-base leading-[21px] mt-3.5">
        <span class="text-[#333333] mr-1">Select: </span>
        <button @click="selectAllBlockedAddresses" class="text-[#3964D0]">
          All
        </button>
        <span>,</span>
        <button
          @click="deSelectAllBlockedAddresses"
          class="text-[#3964D0] ml-1"
        >
          None
        </button>
      </div>
      <button
        :disabled="!isUnblockEnabled"
        class="h-[33px] px-4 rounded-full text-[#525252] bg-[#F1F2F6] hover:bg-[#d8d9dd]/50 text-center mt-2"
        :class="{ 'opacity-60': !isUnblockEnabled }"
        @click="openUnlockAddressModal({ type: 'multiple', id: '' })"
      >
        Unblock selected addresses
      </button>
    </div>
  </div>
</template>
