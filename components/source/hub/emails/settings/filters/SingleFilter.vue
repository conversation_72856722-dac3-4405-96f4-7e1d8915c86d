<script setup lang="ts">
const props = defineProps({
  filter: {
    type: Object,
    required: true,
  },
})
</script>

<template>
  <div class="flex flex-col">
    <div class="flex items-center space-x-2">
      <span class="text-[#333333]">Matches: </span>
      <p class="text-[#333333] font-semibold">
        <span v-if="filter.from">from:({{ filter.from }})</span>
        <span v-if="filter.to"> to:({{ filter.to }})</span>
        <span v-if="filter.subject"> subject: {{ filter.subject }}</span>
        <span v-if="filter.hasTheWord">
          {{ filter.hasTheWord }}
        </span>
        <span v-if="filter.doesNotHaveTheWord">
          -{{ filter.doesNotHaveTheWord }}
        </span>
        <span v-if="filter.hasAttachment"> has:attachment</span>
        <span v-if="filter.excludeChats"> -in:chats</span>
        <span v-if="filter.size" class="pl-1">
          {{ filter.sizeOperator.value === 's_ss' ? 'larger' : 'smaller' }}:{{
            filter.size
          }}{{ filter.sizeUnit.text }}</span
        >
      </p>
    </div>
    <div class="flex items-center space-x-2">
      <p class="text-[#333333]">
        <span>Do this: </span>
        <span v-if="filter.shouldArchive">Skip inbox, </span>
        <span v-if="filter.shouldMarkAsRead">Mark as read, </span>
        <span v-if="filter.shouldStar">Star it, </span>
        <span v-if="filter.label?.value?.text && filter.label?.isActive">
          Apply label: "{{ filter.label?.value?.text }}",
        </span>
        <span
          v-if="filter.forwardTo?.value?.text && filter.forwardTo?.isActive"
        >
          Forward to: {{ filter.forwardTo?.value?.text }},
        </span>
        <span v-if="filter.delete">Delete it, </span>
        <span v-if="filter.shouldNeverSpam">Never send to spam, </span>
        <span v-if="filter.shouldAlwaysMarkAsImportant">
          Mark it as important,
        </span>
        <span v-if="filter.shouldNeverMarkAsImportant">
          Never mark as important,
        </span>
        <span
          v-if="
            filter.smartLabelToApply?.value?.text &&
            filter.smartLabelToApply?.isActive
          "
        >
          Categorize as: {{ filter.smartLabelToApply?.value?.text }}
        </span>
      </p>
    </div>
  </div>
</template>

<style scoped></style>
