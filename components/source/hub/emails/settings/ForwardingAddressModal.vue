<script setup lang="ts">
import { useVuelidate } from '@vuelidate/core'
import { email, required } from '@vuelidate/validators'
import { useStore } from 'vuex'

const store = useStore()

const forwardingEmail = ref<string>('')
const currentStep = ref('email')
const forwardingEmailInput = ref<HTMLInputElement | null>(null)

const rules = {
  forwardingEmail: { required, email },
}

const v$ = useVuelidate(rules, { forwardingEmail })

const closeDeleteModal = () => {
  store.commit('emails/SET_FORWARDING_ADDRESS_MODAL', false)
}

const handleNext = () => {
  v$.value.$touch()
  if (v$.value.$invalid) {
    return
  }
  currentStep.value = 'verify'
}

const handleVerify = () => {
  currentStep.value = 'confirm'
}

const handleConfirm = () => {
  currentStep.value = 'email'
  store.commit('emails/SET_UNVERIFIED_EMAIL', {
    id: new Date().getTime(),
    email: forwardingEmail.value,
  })
  closeDeleteModal()
}

const handleAction = () => {
  if (currentStep.value === 'email') {
    handleNext()
  } else if (currentStep.value === 'verify') {
    handleVerify()
  } else if (currentStep.value === 'confirm') {
    handleConfirm()
  }
}
onMounted(() => {
  nextTick(() => {
    forwardingEmailInput.value?.focus()
  })
})
</script>

<template>
  <div
    class="bg-white w-full max-w-[678px] rounded-2xl fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-11"
  >
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="flex justify-between items-center px-6 py-3.5">
        <p class="text-lg font-semibold text-[#505050]">
          Add a forwarding address
        </p>
        <SharedIconHubEmailsCrossIcon
          @click="closeDeleteModal"
          class="w-4 h-4 cursor-pointer"
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6] px-6 py-4">
      <template v-if="currentStep === 'email'">
        <p class="text-base text-[#525252] font-normal">
          Please enter a new forwarding email address:
        </p>
        <input
          ref="forwardingEmailInput"
          type="email"
          v-model="forwardingEmail"
          placeholder="Enter email address"
          class="w-full h-[43px] px-6 mt-2 text-base font-normal text-[#333333] bg-[#F1F2F6] rounded-full border-none outline-none"
          @blur="v$.forwardingEmail.$touch()"
        />
        <template v-if="v$.forwardingEmail.$error">
          <span
            v-if="v$.forwardingEmail.email.$invalid"
            class="text-red-400 text-sm pl-2 w-full"
          >
            The Email is Invalid
          </span>
          <span
            v-if="v$.forwardingEmail.required.$invalid"
            class="text-red-400 text-sm pl-2 w-full"
          >
            The field is required
          </span>
        </template>
      </template>
      <p
        v-if="currentStep === 'verify'"
        class="text-base text-[#525252] font-normal"
      >
        Forwarding mail to {{ forwardingEmail }}
      </p>
      <p
        v-if="currentStep === 'confirm'"
        class="text-base text-[#525252] font-normal"
      >
        A confirmation link has been sent to {{ forwardingEmail }} to verify
        permission.
      </p>
    </div>
    <div class="flex justify-end items-center space-x-2 px-6 py-3.5">
      <button
        v-if="currentStep !== 'confirm'"
        @click="closeDeleteModal"
        class="rounded-full font-semibold text-[#4A71D4] border-[1px] border-[#4A71D4] min-w-[108px] h-[35px] px-6"
      >
        Cancel
      </button>

      <button
        @click="handleAction"
        class="rounded-full font-semibold text-[#FFFFFF] bg-[#4A71D4] min-w-[108px] h-[35px] px-6"
      >
        {{
          currentStep === 'email'
            ? 'Next'
            : currentStep === 'verify'
              ? 'Proceed'
              : 'Ok'
        }}
      </button>
    </div>
  </div>
</template>

<style scoped></style>
