<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()

const unblockAddressModal = computed(
  () => store.state.emails.unblockAddressModal,
)

const closeUnblockModal = () => {
  store.commit('emails/SET_UNBLOCK_ADDRESS_MODAL', {
    isOpen: false,
    type: '',
  })
}
const unblockAddress = () => {
  store.commit('emails/DELETE_BLOCKED_ADDRESS')
  closeUnblockModal()
}
</script>

<template>
  <div
    class="bg-white w-full max-w-[678px] rounded-2xl fixed z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
  >
    <div class="border-b-[2px] border-[#F1F2F6]">
      <div class="flex justify-between items-center px-6 py-3.5">
        <p class="text-lg font-semibold text-[#505050]">
          Unblock this email address
        </p>
        <SharedIconHubEmailsCrossIcon
          @click="closeUnblockModal"
          class="w-4 h-4 cursor-pointer"
        />
      </div>
    </div>
    <div class="border-b-[2px] border-[#F1F2F6] px-6 py-5 text-[#333333]">
      <template v-if="unblockAddressModal.type === 'single'">
        Messages from
        <span class="font-bold">
          {{ unblockAddressModal.name }} &lt;{{ unblockAddressModal.email }}&gt;
        </span>
        will appear in your Inbox.
      </template>
      <template v-else>
        Messages from these addresses will appear in your Inbox.
      </template>
    </div>

    <div class="flex justify-end items-center space-x-2 px-6 py-3.5">
      <button
        @click="closeUnblockModal"
        class="rounded-full font-semibold text-[#4A71D4] border-[1px] border-[#4A71D4] min-w-[108px] h-[35px] px-6"
      >
        Cancel
      </button>

      <button
        @click="unblockAddress"
        class="rounded-full font-semibold text-[#FFFFFF] bg-[#4A71D4] min-w-[108px] h-[35px] px-6"
      >
        Unblock
      </button>
    </div>
  </div>
</template>

<style scoped></style>
