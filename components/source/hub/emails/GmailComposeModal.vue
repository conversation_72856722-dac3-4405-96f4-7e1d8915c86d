<script setup lang="ts">
import { useStore } from 'vuex'

const store = useStore()
const showGmailComposeModal = computed(
  () => store.state.emails.showGmailComposeModal,
)

const closeModal = () => {
  store.commit('emails/SET_SHOW_GMAIL_COMPOSE_MODAL', false)
}
</script>

<template>
  <Transition name="page" mode="out-in">
    <div
      v-if="showGmailComposeModal"
      class="bg-white w-full h-full rounded-2xl absolute top-0 left-0 z-10"
    >
      <div class="border-b-[2px] border-[#F1F2F6]">
        <div class="flex justify-between items-center px-6 py-3.5">
          <p class="text-lg font-semibold text-[#505050]">New message</p>
          <SharedIconHubEmailsCrossIcon
            @click="closeModal"
            class="w-4 h-4 cursor-pointer"
          />
        </div>
      </div>
      <div
        class="text-[#525252] text-base h-full max-h-[calc(100%-58px)] overflow-y-auto custom-scroll"
      >
        <SourceHubEmailsComposeSection />
      </div>
    </div>
  </Transition>
</template>
