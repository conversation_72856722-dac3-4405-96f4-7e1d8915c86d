<script setup lang="ts">
import { useToast } from 'vue-toastification'

withDefaults(
  defineProps<{
    message: string
    onUndo?: () => void
    toastClass?: string
    undoBtnClass?: string
    closeBtnClass?: string
  }>(),
  {
    message: '',
    toastClass: 'text-white bg-[#323744] ml-[120px]',
    undoBtnClass: 'text-[#9FBAFF] hover:bg-[#6A8ADC]/30 ',
    closeBtnClass: 'text-white',
  },
)

const toast = useToast()

const handleClose = () => {
  toast.clear()
}
</script>

<template>
  <div
    class="flex items-center gap-6 justify-between px-4 py-2 rounded-[4px] shadow-md min-h-[50px] mt-4"
    :class="toastClass"
  >
    <p class="flex-1 text-base leading-[21px] max-w-[350px] line-clamp-2">
      {{ message }}
    </p>
    <div class="flex items-center gap-3.5">
      <button
        v-if="onUndo"
        class="rounded-[4px] font-semibold px-2 py-[7px]"
        :class="undoBtnClass"
        @click="onUndo"
      >
        Undo
      </button>

      <button
        @click="handleClose"
        class="text-xl leading-5 size-5 rounded-full"
        :class="closeBtnClass"
      >
        ✕
      </button>
    </div>
  </div>
</template>
