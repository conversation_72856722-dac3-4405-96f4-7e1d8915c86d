<script setup lang="ts">
import { useStore } from 'vuex'
import MoveToIcon from '~/components/shared/icon/hub/emails/MoveToIcon.vue'
import Reply from '~/components/shared/icon/hub/emails/Reply.vue'
import Forward from '~/components/shared/icon/hub/emails/Forward.vue'
import Filter from '~/components/shared/icon/hub/emails/Filter.vue'
import PrintIcon from '~/components/shared/icon/hub/microsoft/PrintIcon.vue'
import DeleteIcon from '~/components/shared/icon/hub/emails/DeleteIcon.vue'
import BlockIcon from '~/components/shared/icon/hub/microsoft/BlockIcon.vue'
import SpamIcon from '~/components/shared/icon/hub/emails/sidebar/Spam.vue'
import UnreadMessageIcon from '~/components/shared/icon/hub/emails/UnreadMessageIcon.vue'
import ReadMessageIcon from '~/components/shared/icon/hub/emails/ReadMessageIcon.vue'
import Snooze from '~/components/shared/icon/hub/emails/Snooze.vue'
import AddTasks from '~/components/shared/icon/hub/emails/AddTasks.vue'

const store = useStore()

interface Props {
  toggleSingleCheckBox?: Function
  checkedAll?: boolean
  showMinus?: boolean
  messageRead?: boolean
  specificId?: number
}

const props = withDefaults(defineProps<Props>(), {
  toggleSingleCheckBox: () => {},
  checkedAll: false,
  showMinus: false,
  messageRead: false,
  specificId: 0,
})

interface SingleMessageMenuOptions {
  id: number
  image: string | Component
  image1: string | Component
  title: string
  title1: string
  function: (payload: MouseEvent) => void
}

const singleMessageMenuOptions = ref<SingleMessageMenuOptions[]>([
  {
    id: 1,
    image: markRaw(Reply),
    title: 'Reply',
    image1: '',
    title1: '',
    function: (payload: MouseEvent) => {},
  },
  {
    id: 2,
    image: markRaw(Forward),
    title: 'Forward',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 3,
    image: markRaw(Filter),
    title: 'Filter message like these',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 4,
    image: markRaw(PrintIcon),
    title: 'Print',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 5,
    image: markRaw(DeleteIcon),
    title: 'Delete this message',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 6,
    image: markRaw(BlockIcon),
    title: 'Block "Foodie Adventure"',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 7,
    image: markRaw(SpamIcon),
    title: 'Report spam',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 8,
    image: markRaw(UnreadMessageIcon),
    title: 'Report phishing',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 9,
    image: markRaw(Snooze),
    title: 'Show original',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 10,
    image: markRaw(AddTasks),
    title: 'Translate message',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 11,
    image: markRaw(MoveToIcon),
    title: 'Download message',
    image1: '',
    title1: '',
    function: () => {},
  },
  {
    id: 12,
    image: markRaw(UnreadMessageIcon),
    title: 'Mark as unread',
    image1: markRaw(ReadMessageIcon),
    title1: 'Mark as read',
    function: () =>
      store.commit('emails/READ_UNREAD_A_SPECIFIC_MESSAGE', props.specificId),
  },
])

const handleOptionClick = () => {
  store.commit('emails/READ_ALL_MESSAGE')
}
</script>

<template>
  <div class="flex flex-col select-extend-menu-box space-y-3.5 bg-white py-2">
    <div class="flex flex-col">
      <div
        v-for="singleMessageMenuOption in singleMessageMenuOptions"
        :key="singleMessageMenuOption.id"
        class="flex justify-between items-center !space-x-2 px-3.5 py-1.5 hover:bg-[#F1F2F6] cursor-pointer"
        :class="
          singleMessageMenuOption.id === 2 ? 'border-b border-[#2228313D]' : ''
        "
        @click.stop="singleMessageMenuOption.function"
      >
        <div class="flex items-center !space-x-2">
          <component
            v-if="singleMessageMenuOption.id !== 12"
            :is="singleMessageMenuOption.image"
            color="#707070"
          />
          <component
            v-else-if="singleMessageMenuOption.id === 12"
            :is="
              messageRead
                ? singleMessageMenuOption.image
                : singleMessageMenuOption.image1
            "
            color="#707070"
          />
          <p v-if="singleMessageMenuOption.id !== 12" class="text-[#525252]">
            {{ singleMessageMenuOption.title }}
          </p>
          <p
            v-else-if="singleMessageMenuOption.id === 12"
            class="text-[#525252]"
          >
            {{
              messageRead
                ? singleMessageMenuOption.title
                : singleMessageMenuOption.title1
            }}
          </p>
        </div>
        <!-- <SharedIconHubEmailsDownArrow
          v-if="
            singleMessageMenuOption.id === 1 ||
            singleMessageMenuOption.id === 11 ||
            singleMessageMenuOption.id === 12
          "
          class="transform rotate-[-90deg]"
        /> -->
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.select-menu-box {
  min-width: 176px;
  max-width: 176px;
  height: fit-content;
  z-index: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
.select-extend-menu-box {
  min-width: 244px;
  max-width: 244px;
  height: fit-content;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 2px 2px 4px #22283114;
  border-radius: 8px;
}
</style>
