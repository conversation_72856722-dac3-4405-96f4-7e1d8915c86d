<!-- components/DropdownOption.vue -->
<template>
  <div>
    <!-- Single Option -->
    <div class="">
      <p
        :style="{
          '--hovercolor': hoverColor,
          '--hovertextcolor': hoverTextColor,
          paddingLeft: `${depth * 14}px`,
          paddingRight: `${paddingRight}px`,
          color: mailLabelColor,
        }"
        class="font-semibold py-2"
        v-if="option[mailLabelKey]"
      >
        {{ option[mailLabelKey] }}
      </p>
      <div
        v-if="option[labelKey]"
        class="py-2 cursor-pointer option transition-all duration-200 grid grid-cols-[auto_1fr] !space-x-2 items-center"
        :class="[option?.sectionEnd ? 'border-b border-[#E3E3E3]' : '']"
        :style="{
          '--hovercolor': hoverColor,
          '--hovertextcolor': hoverTextColor,
          paddingLeft: `${depth * 14}px`,
          paddingRight: `${paddingRight}px`,
        }"
        @click.stop="handleClick(option)"
      >
        <slot name="beforeTick" />
        <img
          class="w-8 h-8 rounded-full"
          v-if="option.image"
          :src="option.image"
          :alt="option[labelKey]"
        />
        <div>
          <p>{{ option[labelKey] || '[Missing Label]' }}</p>
          <p v-if="option[subTextKey]" class="text-xs">
            {{ option[subTextKey] || '[Missing Label]' }}
          </p>
        </div>
      </div>
    </div>

    <!-- Children -->
    <DropdownOption
      v-for="child in option.children"
      :key="child[idKey] || JSON.stringify(child)"
      :option="child"
      :depth="depth + 1"
      :labelKey="labelKey"
      :mailLabelKey="mailLabelKey"
      :idKey="idKey"
      :hoverColor="hoverColor"
      :hoverTextColor="hoverTextColor"
      :padding-right="paddingRight"
      @select="emitSelect"
    />
  </div>
</template>

<script setup>
defineProps({
  option: Object,
  depth: { type: Number, default: 1 },
  labelKey: String,
  subTextKey: String,
  mailLabelKey: String,
  idKey: String,
  hoverColor: String,
  hoverTextColor: String,
  mailLabelColor: String,
  paddingRight: String,
})

const emit = defineEmits(['select'])

const handleClick = (opt) => {
  emit('select', opt)
}
const emitSelect = (val) => {
  emit('select', val)
}
</script>

<style scoped>
.option:hover {
  background-color: var(--hovercolor);
  color: var(--hovertextcolor);
}
</style>
