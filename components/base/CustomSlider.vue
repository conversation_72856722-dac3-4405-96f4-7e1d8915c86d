<script setup lang="ts">
const value = ref(0)
const max = 10
const slider = ref<HTMLInputElement | null>(null)
const isHovered = ref(false)

const updateSlider = () => {
  if (!slider.value) return
  const percent = (+value.value / +max) * 100
  const fillColor = isHovered.value ? '#4A71D4' : '#707070'
  // const fillColor = '#707070'
  const emptyColor = '#C2C2C2'

  slider.value.style.background = `linear-gradient(to right, ${fillColor} ${percent}%, ${emptyColor} ${percent}%)`
  console.log(value.value)
  // Update border color based on value
  const thumbBorderColor =
    value.value === 0 && !isHovered.value ? '#707070' : fillColor
  console.log(thumbBorderColor)
  slider.value.style.setProperty('--thumb-border-color', thumbBorderColor)
}

onMounted(updateSlider)
watch([value, isHovered], updateSlider)
</script>

<template>
  <div
    class="flex items-center space-x-2 w-full max-w-[400px]"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
  >
    <input
      ref="slider"
      type="range"
      min="0"
      :max="max"
      step="5"
      v-model="value"
      class="range-slider"
      @input="updateSlider"
    />
    <span class="text-sm text-black w-6 text-right">{{ value }}</span>
  </div>
</template>

<style lang="scss" scoped>
.range-slider {
  -webkit-appearance: none;
  padding: 0px;
  border: none;
  width: 100%;
  height: 4px;
  border-radius: 9999px;
  background: linear-gradient(to right, #4a71d4 0%, #c2c2c2 0%);
  transition: background 0.3s ease-in-out;
  outline: none;
}

.range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 9999px;
  background: white;
  border: 2px solid var(--thumb-border-color, #4a71d4);
  cursor: pointer;
  margin-top: 0px; /* aligns with the track */
  transition: border-color 0.3s ease;
  position: relative;
  z-index: 10;
}

.range-slider::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 9999px;
  background: white;
  border: 3px solid var(--thumb-border-color, #4a71d4);
  cursor: pointer;
}

.range-slider:hover::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 9999px;
  background: white;
  border: 2px solid var(--thumb-border-color, #4a71d4);
  cursor: pointer;
  margin-top: 0px; /* aligns with the track */
  transition: border-color 0.3s ease;
  position: relative;
  z-index: 10;
}
</style>
