<script setup lang="ts">
const props = defineProps({
  opacity: {
    type: Number,
    default: 0.32,
  },
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="32"
    viewBox="0 0 32 32"
  >
    <g id="button-undo" transform="translate(-604 -361)">
      <rect
        id="button"
        width="32"
        height="32"
        rx="16"
        transform="translate(604 361)"
        fill="#f1f2f6"
        opacity="0"
      />
      <g id="icon" transform="translate(-452 134)" :opacity="opacity">
        <rect
          id="icon-frame"
          width="20"
          height="20"
          transform="translate(1062 233)"
          fill="#fff"
          opacity="0"
        />
        <path
          id="Path_2251"
          data-name="Path 2251"
          d="M198.606-751.61a.825.825,0,0,1-.613-.252.857.857,0,0,1-.247-.625.857.857,0,0,1,.247-.625.825.825,0,0,1,.613-.252h7.37a4.372,4.372,0,0,0,3.09-1.208,3.918,3.918,0,0,0,1.295-2.983,3.9,3.9,0,0,0-1.295-2.978,4.383,4.383,0,0,0-3.09-1.2h-8.051l2.587,2.636a.847.847,0,0,1,.249.616.848.848,0,0,1-.249.616.8.8,0,0,1-.61.248.824.824,0,0,1-.6-.248l-3.93-4.005a.968.968,0,0,1-.227-.346,1.141,1.141,0,0,1-.066-.393,1.141,1.141,0,0,1,.066-.393.968.968,0,0,1,.227-.346l3.93-4a.815.815,0,0,1,.6-.254.815.815,0,0,1,.6.254.835.835,0,0,1,.244.621.855.855,0,0,1-.244.61l-2.587,2.636h8.051a6.022,6.022,0,0,1,4.31,1.712,5.6,5.6,0,0,1,1.794,4.222,5.622,5.622,0,0,1-1.794,4.228,6.012,6.012,0,0,1-4.31,1.717Z"
          transform="translate(867.92 1002.61)"
          fill="#525252"
        />
      </g>
    </g>
  </svg>
</template>
