<script setup lang="ts"></script>
<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
  >
    <g id="icon" transform="translate(-1116 -222)">
      <rect
        id="icon-frame"
        width="20"
        height="20"
        transform="translate(1116 222)"
        fill="#fff"
        opacity="0"
      />
      <path
        id="Path_2544"
        data-name="Path 2544"
        d="M82.4-864a1.541,1.541,0,0,1-1.13-.47,1.541,1.541,0,0,1-.47-1.13v-9.02a1.756,1.756,0,0,1-.58-.57A1.49,1.49,0,0,1,80-876v-2.4a1.541,1.541,0,0,1,.47-1.13A1.541,1.541,0,0,1,81.6-880H94.4a1.541,1.541,0,0,1,1.13.47A1.541,1.541,0,0,1,96-878.4v2.4a1.49,1.49,0,0,1-.22.81,1.756,1.756,0,0,1-.58.57v9.02a1.541,1.541,0,0,1-.47,1.13,1.541,1.541,0,0,1-1.13.47Zm0-10.4v8.8H93.6v-8.8Zm-.8-1.6H94.4v-2.4H81.6Zm4.8,5.6h3.2a.774.774,0,0,0,.57-.23.774.774,0,0,0,.23-.57.774.774,0,0,0-.23-.57.774.774,0,0,0-.57-.23H86.4a.774.774,0,0,0-.57.23.774.774,0,0,0-.23.57.774.774,0,0,0,.23.57A.774.774,0,0,0,86.4-870.4ZM88-870Z"
        transform="translate(1038 1104)"
        fill="currentColor"
      />
    </g>
  </svg>
</template>

<style scoped></style>
