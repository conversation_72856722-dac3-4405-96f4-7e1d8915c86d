<script setup lang="ts"></script>
<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
  >
    <g id="icon" transform="translate(-1116 -222)">
      <rect
        id="icon-frame"
        width="20"
        height="20"
        transform="translate(1116 222)"
        fill="#fff"
        opacity="0"
      />
      <path
        id="Path_2768"
        data-name="Path 2768"
        d="M81.6-788a1.594,1.594,0,0,1-1.13-.441A1.4,1.4,0,0,1,80-789.5v-9a1.4,1.4,0,0,1,.47-1.059A1.594,1.594,0,0,1,81.6-800h4.14a1.672,1.672,0,0,1,.61.112,1.566,1.566,0,0,1,.51.319L88-798.5h6.4a1.593,1.593,0,0,1,1.13.441A1.4,1.4,0,0,1,96-797v7.5a1.4,1.4,0,0,1-.47,1.059A1.594,1.594,0,0,1,94.4-788Zm0-1.5H94.4V-797H87.34l-1.6-1.5H81.6Zm0,0v0Z"
        transform="translate(1038 1026)"
        fill="currentColor"
      />
    </g>
  </svg>
</template>

<style scoped></style>
