<script setup lang="ts">
const props = defineProps({
  opacity: {
    type: Number,
    default: 1,
  },
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="32"
    viewBox="0 0 32 32"
  >
    <g id="button-move-up" transform="translate(-604 -361)" :opacity="opacity">
      <rect
        id="button"
        width="32"
        height="32"
        rx="16"
        transform="translate(604 361)"
        fill="#f1f2f6"
        opacity="0"
      />
      <g id="icon" transform="translate(-452 134)">
        <rect
          id="icon-frame"
          width="20"
          height="20"
          transform="translate(1062 233)"
          fill="#fff"
          opacity="0"
        />
        <path
          id="Path_2927"
          data-name="Path 2927"
          d="M182.563-779.507l-4.524,5.034a.8.8,0,0,1-.646.3.89.89,0,0,1-.646-.321,1.15,1.15,0,0,1-.265-.719,1,1,0,0,1,.265-.719l6.094-6.78a.807.807,0,0,1,.3-.218.928.928,0,0,1,.346-.064.928.928,0,0,1,.346.064.807.807,0,0,1,.3.218l6.094,6.78a1.017,1.017,0,0,1,.254.706,1.115,1.115,0,0,1-.254.732.857.857,0,0,1-.658.308.857.857,0,0,1-.658-.308l-4.5-5.008v11.48a1.055,1.055,0,0,1-.265.732.849.849,0,0,1-.658.3.849.849,0,0,1-.658-.3,1.055,1.055,0,0,1-.265-.732Z"
          transform="translate(888.52 1018)"
          fill="#525252"
        />
      </g>
    </g>
  </svg>
</template>

<style scoped></style>
