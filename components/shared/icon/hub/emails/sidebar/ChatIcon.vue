<script setup lang="ts">
const props = defineProps({
  color: {
    type: String,
    default: '#525252',
  },
})
</script>

<template>
  <div>
    <svg
      class="outline-icon"
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
    >
      <g id="Icon" transform="translate(13699 16927)">
        <rect
          id="icon-frame"
          width="20"
          height="20"
          transform="translate(-13699 -16927)"
          fill="#fff"
          opacity="0"
        />
        <path
          id="Vector32154"
          d="M0,16V1.6A1.541,1.541,0,0,1,.47.47,1.541,1.541,0,0,1,1.6,0H14.4a1.541,1.541,0,0,1,1.13.47A1.541,1.541,0,0,1,16,1.6v9.6a1.6,1.6,0,0,1-1.6,1.6H3.2Zm2.52-4.8H14.4V1.6H1.6V12.1Z"
          transform="translate(-13697 -16925)"
          :fill="color"
        />
      </g>
    </svg>
    <svg
      class="solid"
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
    >
      <g id="Icon" transform="translate(-2348 -87)">
        <rect
          id="icon-frame"
          width="20"
          height="20"
          transform="translate(2348 87)"
          fill="#fff"
          opacity="0"
        />
        <path
          id="Vector32154"
          d="M0,16V1.6A1.541,1.541,0,0,1,.47.47,1.541,1.541,0,0,1,1.6,0H14.4a1.541,1.541,0,0,1,1.13.47A1.541,1.541,0,0,1,16,1.6v9.6a1.6,1.6,0,0,1-1.6,1.6H3.2Zm2.52-4.8H14.4v0l-12.8.9Z"
          transform="translate(2350 89)"
          fill="#fff"
        />
      </g>
    </svg>
  </div>
</template>

<style scoped>
.router-link-exact-active > .menu-container > svg > path,
.router-link-active > .menu-container > svg > path {
  fill: #fff;
}
.solid {
  display: none;
}
.router-link-exact-active > .menu-container > div > .solid,
.router-link-active > .menu-container > div > .solid {
  display: block;
}
.router-link-exact-active > .menu-container > div > .outline-icon,
.router-link-active > .menu-container > div > .outline-icon {
  display: none;
}
</style>
