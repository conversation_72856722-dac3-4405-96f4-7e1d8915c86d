<script setup lang="ts">
const props = defineProps({
  color: {
    type: String,
    default: '#707070',
  },
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
  >
    <g id="icon" transform="translate(-610 -367)">
      <rect
        id="frame"
        width="20"
        height="20"
        transform="translate(610 367)"
        fill="#f1f2f6"
        opacity="0"
      />
      <path
        id="Path_2744"
        data-name="Path 2744"
        d="M129.729-786h-9.505a.807.807,0,0,1-.778-.481.826.826,0,0,1,.086-.919l4.148-5.6-4.148-5.6a.826.826,0,0,1-.086-.919.807.807,0,0,1,.778-.481h9.505a1.7,1.7,0,0,1,.778.186,1.663,1.663,0,0,1,.6.514L135-794.05a1.7,1.7,0,0,1,.346,1.05,1.7,1.7,0,0,1-.346,1.05l-3.888,5.25a1.663,1.663,0,0,1-.6.514A1.7,1.7,0,0,1,129.729-786Zm-7.777-1.75h7.777l3.888-5.25-3.888-5.25h-7.777l3.111,4.2a1.7,1.7,0,0,1,.346,1.05,1.7,1.7,0,0,1-.346,1.05ZM127.785-793Z"
        transform="translate(492.654 1170)"
        :fill="color"
      />
    </g>
  </svg>
</template>

<style scoped></style>
