<script setup lang="ts">
const props = defineProps({
  color: {
    type: String,
    default: '#4a71d4',
  },
})
</script>

<template>
  <svg
    id="icon"
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
  >
    <rect id="frame" width="20" height="20" fill="#f1f2f6" opacity="0" />
    <path
      id="Path_2849"
      data-name="Path 2849"
      d="M121.711-824a1.618,1.618,0,0,1-1.209-.522,1.746,1.746,0,0,1-.5-1.256v-12.445a1.746,1.746,0,0,1,.5-1.255,1.618,1.618,0,0,1,1.209-.522h10.674a.812.812,0,0,1,.61.256.878.878,0,0,1,.246.633.878.878,0,0,1-.246.633.812.812,0,0,1-.61.255H121.711v12.445H133.69v-5.778a.878.878,0,0,1,.246-.633.812.812,0,0,1,.61-.256.812.812,0,0,1,.61.256.878.878,0,0,1,.246.633v5.778a1.746,1.746,0,0,1-.5,1.256,1.618,1.618,0,0,1-1.209.522Zm5.583-6.044,7.273-7.556a.77.77,0,0,1,.578-.244.841.841,0,0,1,.6.244.822.822,0,0,1,.257.622.9.9,0,0,1-.257.644l-7.85,8.156a.806.806,0,0,1-.6.267.806.806,0,0,1-.6-.267l-3.636-3.778a.861.861,0,0,1-.235-.622.861.861,0,0,1,.235-.622.8.8,0,0,1,.6-.244.8.8,0,0,1,.6.244Z"
      transform="translate(-118 842)"
      :fill="color"
    />
  </svg>
</template>

<style scoped></style>
