<script setup lang="ts">
import { useStore } from 'vuex'
import { GET_QR_CODE } from '~/constants/urls'

interface QRCodeResponse {
  status: 'success' | 'error' | string
  connected: boolean
  qr_code: string
  message: string
  expires_in: number
}

const downloadLoader = computed(() => store.state.archive.downloadLoader)

const store = useStore()
const { fetch } = useFetched()
const onBoardingContents = [
  {
    id: 1,
    text: `<p>
                    Open <span class="font-bold">WhatsApp</span>
                  </p>
                  <img class="size-6" src="/images/icon/whatsappicon.svg" />
                  <p>on your phone</p>`,
  },
  {
    id: 2,
    text: `<p>Navigate to “Link Device”</p>`,
  },
  {
    id: 3,
    text: `<p>Scan the QR code to confirm</p>`,
  },
]
const qrCodeImage = ref<string>('')
const isQRLoading = ref<boolean>(false)

const getQRCode = async () => {
  isQRLoading.value = true
  try {
    const response = (await fetch(GET_QR_CODE)) as QRCodeResponse
    if (response.status === 'success') {
      qrCodeImage.value = response.qr_code
    }
  } catch (error) {
    console.error('Error fetching QR code:', error)
  } finally {
    isQRLoading.value = false
  }
}
const addLoading = () => {
  store.commit('archive/SET_DOWNLOAD_LOADER', true)
  setTimeout(() => {
    store.commit('archive/SET_DOWNLOAD_LOADER', false)
    store.commit('home/SET_SHOW_WHATSAPP_ONBOARDING', false)
    const responseData = {
      profile_id: '**********',
      items: [
        {
          id: '**********',
          type: 'VoiceFax DirectNumber',
          username: '+19832009841',
          name: 'Chad Gordon',
          profile_image_url:
            'https://dev-api.sharparchive.com/api/social/media/**********.jpg?provider=RingCentral',
          added: true,
        },
        {
          id: '**********',
          type: 'VoiceFax DirectNumber',
          username: '+***********',
          name: 'Edom Dance',
          profile_image_url:
            'https://dev-api.sharparchive.com/api/social/media/**********.jpg?provider=RingCentral',
          added: true,
        },
      ],
    }
    store.commit('header/SET_FB_AUTH_DATA', responseData)
    // this.$store.commit('header/SET_PROVIDER_NAME', provider)
    store.commit('header/SET_TEXT_ACCOUNT_ONBOARDING', true)
  }, 2000)
}
onMounted(async () => {
  await getQRCode()
})
</script>

<template>
  <div
    class="md:w-[691px] w-[90%] max-h-[720px] md:h-[90%] h-auto z-1 bg-white rounded-2xl absolute transform top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
  >
    <div class="overflow-auto flex flex-col h-full">
      <div class="border-b-[2px] border-[#F1F2F6]">
        <div class="px-6 pt-3.5 pb-4 flex justify-between items-center">
          <h2 class="text-[#505050] font-semibold">WhatsApp Onboarding</h2>
          <SharedIconHubEmailsCrossIcon
            class="w-4 h-4 cursor-pointer"
            @click.stop="
              store.commit('home/SET_SHOW_WHATSAPP_ONBOARDING', false)
            "
          />
        </div>
      </div>
      <div
        v-if="!downloadLoader"
        class="border-b-[2px] border-[#F1F2F6] flex-grow"
      >
        <div class="px-6 pt-4 pb-[22px] h-full">
          <div class="grid grid-cols-2 gap-x-[31px] h-full">
            <div class="flex flex-col justify-between">
              <div class="flex flex-col space-y-[20px]">
                <div
                  v-for="onBoardingContent in onBoardingContents"
                  :key="onBoardingContent.id"
                  class="flex space-x-3 settings_wrapper"
                >
                  <div
                    class="relative w-[30px] min-w-[30px] h-[30px] min-h-[30px] border border-[#525252] rounded-full flex justify-center items-center point_circle"
                  >
                    <p class="text-[#333333] text-lg">
                      {{ onBoardingContent.id }}
                    </p>
                  </div>
                  <div
                    class="flex space-x-1.5 items-center whitespace-nowrap text-[#333333] text-lg"
                    v-html="onBoardingContent.text"
                  ></div>
                </div>
              </div>
              <img
                v-if="!isQRLoading && qrCodeImage"
                class="w-[350px] min-w-[350px] h-[350px] md:block hidden"
                :src="qrCodeImage"
                alt="qrcode"
                @click.stop="addLoading"
              />
              <div
                v-else-if="isQRLoading"
                class="w-[350px] min-w-[350px] h-[350px] animate-pulse bg-gray-300"
              ></div>
            </div>
            <div class="md:flex justify-end hidden">
              <img
                class="w-[262px] h-full border border-[#525252] rounded-2xl"
                src="/images/gifs/main_comp_1.gif"
                alt="Loading animation"
              />
            </div>
          </div>
          <img
            class="w-full block md:hidden mt-10"
            src="/images/icon/qrcode.png"
            alt="qrcode"
          />
        </div>
      </div>
      <div v-if="!downloadLoader" class="flex justify-center items-center py-4">
        <button
          class="w-26 h-[35px] rounded-full border border-orange-dark text-orange-dark flex justify-center items-center"
          @click.stop="store.commit('home/SET_SHOW_WHATSAPP_ONBOARDING', false)"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.point_circle::after {
  content: '';
  position: absolute;
  top: 100%; /* place just below the circle */
  left: 50%;
  transform: translateX(-50%);
  width: 1px;
  height: 21px;
  min-width: 1px;
  min-height: 21px;
  background: #525252;
}
.settings_wrapper:last-child .point_circle::after {
  display: none;
}
</style>
