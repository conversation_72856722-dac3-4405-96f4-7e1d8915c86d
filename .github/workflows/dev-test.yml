# name: Check Unit Test

# on:
#   pull_request:
#     branches:
#       - next

# jobs:
#   unit-test:
#     runs-on: ubuntu-latest

#     steps:
#       - name: Checkout code
#         uses: actions/checkout@v3

#       - name: Set up Node.js and enable Corepack
#         uses: actions/setup-node@v3
#         with:
#           node-version: '18' # Use the Node.js version required for your project

#       - name: Enable and set Yarn version using Corepack
#         run: |
#           corepack enable
#           corepack prepare yarn@4.2.2 --activate
#         # This ensures the workflow uses Yarn 4.2.2

#       - name: Cache Yarn Dependencies
#         uses: actions/cache@v3
#         with:
#           path: ~/.yarn/cache
#           key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
#           restore-keys: |
#             ${{ runner.os }}-yarn-
#         # Caches Yarn dependencies based on the `yarn.lock` file. If the exact cache is not found, a partial match is restored.

#       - name: Install dependencies
#         run: |
#           yarn install --immutable

#       - name: Build the project
#         run: |
#           yarn build

#       - name: Run unit tests with Vitest
#         run: |
#           yarn vitest

