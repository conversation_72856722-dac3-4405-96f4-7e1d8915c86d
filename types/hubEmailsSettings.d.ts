interface ImapAccess {
  expunge: string
  deleteAction: string
  folderSizeLimit: string
  currentFolderSize: {
    label: string
    value: number
  }
}

interface Pop {
  status: string
  enabledSince: string
  popAction: {
    label: string
    value: string
  }
}

interface EmailAddress {
  id: number
  email: string
}

interface Forwarding {
  status: string
  forwardingEmail: EmailAddress | null
  forwardingAction: {
    label: string
    value: string
  }
}

interface ForwardingAndPopImap {
  imapAccess?: ImapAccess | null
  pop?: Pop | null
  forwarding?: Forwarding | null
}

interface Signature {
  id?: string
  label: string
  value: string
}

interface DefaultSignature {
  newMessageSignatureId: string | null
  replyForwardMessageSignatureId: string | null
}

interface DefaultSignatures {
  newMessageSignature: Signature | null
  replyForwardMessageSignature: Signature | null
}

interface OutlookFolder {
  id: string
  title: string
  iconKey: string
}

interface LanguageAndTime {
  language: string
  dateFormat: string
  timeFormat: string
  askToUpdateTimeZone: boolean
  timeZone: string
}

interface Language {
  id: string
  code: string
  name: string
  nativeName: string
  region: string
  nativeRegion: string
  shortName: string
}
interface Timezone {
  id: number
  text: string
  value: string
}

interface DateFormat {
  id: string
  text: string
  value: string
}

interface TimeFormat {
  id: string
  text: string
  value: string
}

interface Search {
  searchScope: string
  includeDeletedItems: boolean
  showTopThreeRelevantResults: boolean
}

interface History {
  id: string
  folder: string
  date: string
  filesCount: number
  destination: string
  status: string
}

interface LocalImportData {
  folder: string
  filesCount: number
  destination: string
  files: File[]
}

interface ImportFileHistory {
  isLoading: boolean
  localData: LocalImportData | null
  history: History[]
}

interface SenderEmail {
  id: string
  email: string
}

interface SenderList {
  safeSenders: SenderEmail[]
  blockedSenders: SenderEmail[]
  safeMailingLists: SenderEmail[]
}

interface JunkMail {
  incomingMailHandleType: 'standard' | 'strict'
  blockAttachments: boolean
  trustEmailFromContacts: boolean
}

interface EmittedSenderList {
  senderList: SenderList
  selectedSender: SenderOption['value']
}

interface SenderOption {
  label: string
  value: 'safe_sender' | 'blocked_sender' | 'safe_mailing_list'
}

interface MessageHandling {
  emptyDeletedItemsFolder: boolean
  markAsRead: string
  alwaysKeepUnread: boolean
  readReceipt: string
  showShoppingLinks: boolean
  confirmAction: boolean
  translateMessage: string
  translateLanguage: string | null
  noTranslateLanguages: string[]
}

interface GeneralSettings {
  vacationResponder: VacationResponder | null
}

interface VacationResponder {
  status: string
  firstDay: string | null
  lastDay: string | null
  enableLastDay: boolean
  subject: string
  message: string
  myContent: boolean
}

export {
  DateFormat,
  DefaultSignature,
  DefaultSignatures,
  EmailAddress,
  EmittedSenderList,
  Forwarding,
  ForwardingAndPopImap,
  GeneralSettings,
  History,
  ImapAccess,
  ImportFileHistory,
  JunkMail,
  Language,
  LanguageAndTime,
  LocalImportData,
  MessageHandling,
  OutlookFolder,
  Pop,
  Search,
  SenderEmail,
  SenderList,
  SenderOption,
  Signature,
  TimeFormat,
  Timezone,
  VacationResponder,
}
