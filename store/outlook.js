const outlookStore = {
  namespaced: true,
  state() {
    return {
      isSettingsChanged: false,
      showWarningModal: false,
      autoReplies: {
        autoRepliesOn: true,
        timePeriodOn: true,
        startTime: '2025-06-18T00:00:00.000Z',
        endTime: '2025-06-20T00:00:00.000Z',
        blockCalendar: true,
        declineInvitations: true,
        declineAndCancelMeetings: true,
        message: '',
        sendRepliesOn: true,
      },
      signatureOptions: [],
      defaultSignature: {
        newMessageSignatureId: null,
        replyForwardMessageSignatureId: null,
      },
      importEmailModal: false,
      importFileHistory: {
        isLoading: false,
        localData: null,
        history: [],
      },
      languageAndTime: {
        language: 'en-US',
        dateFormat: 'MM-dd-yyyy',
        timeFormat: 'HH:mm',
        askToUpdateTimeZone: true,
        timeZone: 'US/Arizona',
      },
      otherTimeZones: [],
      search: {
        searchScope: 'all_folders_inbox',
        includeDeletedItems: false,
        showTopThreeRelevantResults: false,
      },
      view: {
        displayContactsBy: 'first_name',
      },
    }
  },

  getters: {
    getDefaultSignature(state) {
      const newMessageSignature = state.signatureOptions.find(
        (option) => option.id === state.defaultSignature.newMessageSignatureId,
      )
      const replyForwardMessageSignature = state.signatureOptions.find(
        (option) =>
          option.id === state.defaultSignature.replyForwardMessageSignatureId,
      )

      return {
        newMessageSignature,
        replyForwardMessageSignature,
      }
    },
  },

  mutations: {
    SET_IS_SETTINGS_CHANGED(state, payload) {
      state.isSettingsChanged = payload
    },
    SET_SHOW_WARNING_MODAL(state, payload) {
      state.showWarningModal = payload
    },
    SET_AUTO_REPLIES(state, payload) {
      state.autoReplies = { ...state.autoReplies, ...payload }
    },
    SET_DEFAULT_SIGNATURE(state, payload) {
      state.defaultSignature = {
        ...payload,
      }
    },
    SET_NEW_SIGNATURE(state, payload) {
      state.signatureOptions.unshift({
        id: new Date().getTime().toString(),
        ...payload,
      })
    },
    SET_UPDATED_SIGNATURE(state, payload) {
      state.signatureOptions = state.signatureOptions.map((option) => {
        if (option.id === payload.id) {
          return payload
        }
        return option
      })
    },
    SET_DELETED_SIGNATURE(state, payload) {
      state.signatureOptions = state.signatureOptions.filter(
        (option) => !payload.includes(option.id),
      )
    },
    SET_IMPORT_EMAIL_MODAL(state, payload) {
      state.importEmailModal = payload
    },
    SET_LANGUAGE_AND_TIME(state, payload) {
      state.languageAndTime = { ...state.languageAndTime, ...payload }
    },
    SET_OTHER_TIME_ZONES(state, payload) {
      state.otherTimeZones = payload
    },
    SET_SEARCH(state, payload) {
      state.search = { ...state.search, ...payload }
    },
    SET_VIEW(state, payload) {
      state.view = { ...state.view, ...payload }
    },
    SET_IMPORT_FILE_HISTORY(state, payload) {
      state.importFileHistory.history.unshift({ ...payload })
    },
    REMOVE_A_IMPORT_FILE_HISTORY(state, payload) {
      state.importFileHistory.history = state.importFileHistory.history.filter(
        (history) => history.id !== payload.id,
      )
    },
    async IMPORT_FILE(state, payload) {
      state.importFileHistory.localData = { ...payload }
      state.importFileHistory.isLoading = true
      await new Promise((resolve) => setTimeout(resolve, 3000))
      state.importFileHistory.history.unshift({
        ...payload,
        id: crypto.randomUUID(),
        date: new Date().toISOString(),
        status: `${payload.filesCount} files successfully imported`,
      })
      state.importFileHistory.isLoading = false
      state.importFileHistory.localData = null
    },
  },

  actions: {},
}

export default outlookStore
