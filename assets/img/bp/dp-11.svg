<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="95" height="95" viewBox="0 0 95 95">
  <defs>
    <style>
      .cls-1 {
        fill: url(#pattern);
      }
    </style>
    <pattern id="pattern" preserveAspectRatio="xMidYMid slice" width="100%" height="100%" viewBox="0 0 225 225">
      <image width="225" height="225" xlink:href="data:image/png;base64,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"/>
    </pattern>
  </defs>
  <circle id="dp" class="cls-1" cx="47.5" cy="47.5" r="47.5"/>
</svg>
