<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16"><g clip-path="url(#clip0)"><path fill="url(#paint0_linear)" d="M16 8C16 10.1217 15.1571 12.1566 13.6569 13.6569C12.1566 15.1571 10.1217 16 8 16C5.87827 16 3.84344 15.1571 2.34315 13.6569C0.842855 12.1566 0 10.1217 0 8C0 5.87827 0.842855 3.84344 2.34315 2.34315C3.84344 0.842855 5.87827 0 8 0C10.1217 0 12.1566 0.842855 13.6569 2.34315C15.1571 3.84344 16 5.87827 16 8"/><path fill="url(#paint1_linear)" d="M5.6431 10.888C5.4851 12.733 6.3691 14 8.0001 14C9.6301 14 10.5151 12.733 10.3571 10.888C10.2001 9.042 9.2421 8 8.0001 8C6.7581 8 5.8001 9.042 5.6431 10.888Z"/><path fill="url(#paint2_linear)" d="M3.5 5.5C3.5 4.672 4.059 4 4.75 4C5.441 4 6 4.672 6 5.5C6 6.329 5.441 7 4.75 7C4.059 7 3.5 6.329 3.5 5.5ZM10 5.5C10 4.672 10.56 4 11.25 4C11.941 4 12.5 4.672 12.5 5.5C12.5 6.329 11.941 7 11.25 7C10.56 7 10 6.329 10 5.5Z"/><path fill="#000" d="M3.5 5.5C3.5 4.672 4.059 4 4.75 4C5.441 4 6 4.672 6 5.5C6 6.329 5.441 7 4.75 7C4.059 7 3.5 6.329 3.5 5.5ZM10 5.5C10 4.672 10.56 4 11.25 4C11.941 4 12.5 4.672 12.5 5.5C12.5 6.329 11.941 7 11.25 7C10.56 7 10 6.329 10 5.5Z" filter="url(#filter0_i)"/><path fill="#4E506A" d="M4.48146 4.56717C4.66746 4.60917 4.77146 4.81917 4.71346 5.03617C4.65646 5.25417 4.45946 5.39617 4.27346 5.35417C4.08746 5.31217 3.98346 5.10217 4.04146 4.88417C4.09846 4.66817 4.29546 4.52417 4.48146 4.56717ZM11.1395 4.63017C11.3455 4.67717 11.4615 4.91017 11.3975 5.15017C11.3335 5.39317 11.1155 5.55017 10.9085 5.50417C10.7025 5.45817 10.5865 5.22417 10.6505 4.98317C10.7135 4.74117 10.9325 4.58317 11.1405 4.63017H11.1395Z"/><path fill="#000" d="M11.0682 1.69583C11.1202 1.69083 11.1722 1.68883 11.2252 1.68883C11.7122 1.68883 12.2152 1.89283 12.5972 2.25083C12.6661 2.31649 12.7069 2.4063 12.711 2.50139C12.7151 2.59649 12.6822 2.68948 12.6192 2.76083C12.5888 2.79582 12.5515 2.8243 12.5098 2.84454C12.4681 2.86478 12.4227 2.87637 12.3763 2.87861C12.33 2.88085 12.2837 2.8737 12.2402 2.85758C12.1967 2.84147 12.1569 2.81672 12.1232 2.78483C11.8482 2.52583 11.4672 2.38483 11.1312 2.41583C11.0147 2.42455 10.9014 2.45871 10.7995 2.51589C10.6976 2.57308 10.6094 2.65191 10.5412 2.74683C10.5138 2.78403 10.4792 2.81537 10.4395 2.839C10.3998 2.86263 10.3558 2.87807 10.3101 2.88441C10.2643 2.89074 10.2177 2.88785 10.1731 2.8759C10.1285 2.86395 10.0867 2.84319 10.0502 2.81483C9.9755 2.75549 9.92675 2.66945 9.91425 2.57487C9.90175 2.48028 9.92647 2.38454 9.98321 2.30783C10.1092 2.13357 10.2714 1.98864 10.4587 1.883C10.646 1.77736 10.8539 1.71351 11.0682 1.69583V1.69583ZM3.40321 2.25083C3.77398 1.89822 4.26362 1.69765 4.77521 1.68883C5.0169 1.68359 5.25625 1.73719 5.47261 1.84503C5.68898 1.95286 5.87588 2.11169 6.01721 2.30783C6.07381 2.3845 6.09855 2.4801 6.08625 2.57461C6.07394 2.66912 6.02555 2.7552 5.95121 2.81483C5.91465 2.84321 5.87279 2.864 5.82808 2.87596C5.78338 2.88792 5.73673 2.89082 5.69088 2.88448C5.64504 2.87815 5.60092 2.86271 5.56114 2.83906C5.52135 2.81542 5.48669 2.78406 5.45921 2.74683C5.39097 2.65195 5.30279 2.57316 5.20087 2.51598C5.09895 2.4588 4.98574 2.42462 4.86921 2.41583C4.53421 2.38483 4.15221 2.52583 3.87721 2.78483C3.84352 2.81672 3.80373 2.84147 3.76023 2.85758C3.71673 2.8737 3.67042 2.88085 3.62409 2.87861C3.57776 2.87637 3.53235 2.86478 3.49061 2.84454C3.44887 2.8243 3.41166 2.79582 3.38121 2.76083C3.31821 2.68948 3.2853 2.59649 3.2894 2.50139C3.29351 2.4063 3.3343 2.31649 3.40321 2.25083V2.25083Z" filter="url(#filter1_d)"/><path fill="url(#paint3_linear)" d="M11.0682 1.69583C11.1202 1.69083 11.1722 1.68883 11.2252 1.68883C11.7122 1.68883 12.2152 1.89283 12.5972 2.25083C12.6661 2.31649 12.7069 2.4063 12.711 2.50139C12.7151 2.59649 12.6822 2.68948 12.6192 2.76083C12.5888 2.79582 12.5515 2.8243 12.5098 2.84454C12.4681 2.86478 12.4227 2.87637 12.3763 2.87861C12.33 2.88085 12.2837 2.8737 12.2402 2.85758C12.1967 2.84147 12.1569 2.81672 12.1232 2.78483C11.8482 2.52583 11.4672 2.38483 11.1312 2.41583C11.0147 2.42455 10.9014 2.45871 10.7995 2.51589C10.6976 2.57308 10.6094 2.65191 10.5412 2.74683C10.5138 2.78403 10.4792 2.81537 10.4395 2.839C10.3998 2.86263 10.3558 2.87807 10.3101 2.88441C10.2643 2.89074 10.2177 2.88785 10.1731 2.8759C10.1285 2.86395 10.0867 2.84319 10.0502 2.81483C9.9755 2.75549 9.92675 2.66945 9.91425 2.57487C9.90175 2.48028 9.92647 2.38454 9.98321 2.30783C10.1092 2.13357 10.2714 1.98864 10.4587 1.883C10.646 1.77736 10.8539 1.71351 11.0682 1.69583V1.69583ZM3.40321 2.25083C3.77398 1.89822 4.26362 1.69765 4.77521 1.68883C5.0169 1.68359 5.25625 1.73719 5.47261 1.84503C5.68898 1.95286 5.87588 2.11169 6.01721 2.30783C6.07381 2.3845 6.09855 2.4801 6.08625 2.57461C6.07394 2.66912 6.02555 2.7552 5.95121 2.81483C5.91465 2.84321 5.87279 2.864 5.82808 2.87596C5.78338 2.88792 5.73673 2.89082 5.69088 2.88448C5.64504 2.87815 5.60092 2.86271 5.56114 2.83906C5.52135 2.81542 5.48669 2.78406 5.45921 2.74683C5.39097 2.65195 5.30279 2.57316 5.20087 2.51598C5.09895 2.4588 4.98574 2.42462 4.86921 2.41583C4.53421 2.38483 4.15221 2.52583 3.87721 2.78483C3.84352 2.81672 3.80373 2.84147 3.76023 2.85758C3.71673 2.8737 3.67042 2.88085 3.62409 2.87861C3.57776 2.87637 3.53235 2.86478 3.49061 2.84454C3.44887 2.8243 3.41166 2.79582 3.38121 2.76083C3.31821 2.68948 3.2853 2.59649 3.2894 2.50139C3.29351 2.4063 3.3343 2.31649 3.40321 2.25083V2.25083Z"/></g><defs><filter id="filter0_i" width="9" height="3" x="3.5" y="4" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset/><feGaussianBlur stdDeviation=".5"/><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/><feColorMatrix values="0 0 0 0 0.0980392 0 0 0 0 0.101961 0 0 0 0 0.2 0 0 0 0.819684 0"/><feBlend in2="shape" result="effect1_innerShadow"/></filter><filter id="filter1_d" width="15.422" height="7.199" x=".289" y="-.312" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="1"/><feGaussianBlur stdDeviation="1.5"/><feColorMatrix values="0 0 0 0 0.803922 0 0 0 0 0.388235 0 0 0 0 0.00392157 0 0 0 0.145679 0"/><feBlend in2="BackgroundImageFix" result="effect1_dropShadow"/><feBlend in="SourceGraphic" in2="effect1_dropShadow" result="shape"/></filter><linearGradient id="paint0_linear" x1="8" x2="8" y1="1.64" y2="16" gradientUnits="userSpaceOnUse"><stop stop-color="#FEEA70"/><stop offset="1" stop-color="#F69B30"/></linearGradient><linearGradient id="paint1_linear" x1="8" x2="8" y1="8" y2="14" gradientUnits="userSpaceOnUse"><stop stop-color="#472315"/><stop offset="1" stop-color="#8B3A0E"/></linearGradient><linearGradient id="paint2_linear" x1="8" x2="8" y1="4" y2="7" gradientUnits="userSpaceOnUse"><stop stop-color="#191A33"/><stop offset=".872" stop-color="#3B426A"/></linearGradient><linearGradient id="paint3_linear" x1="8" x2="8" y1="1.688" y2="2.888" gradientUnits="userSpaceOnUse"><stop stop-color="#E78E0D"/><stop offset="1" stop-color="#CB6000"/></linearGradient><clipPath id="clip0"><rect width="16" height="16" fill="#fff"/></clipPath></defs></svg>