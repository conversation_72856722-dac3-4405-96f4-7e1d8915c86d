@tailwind base;
@tailwind components;
@tailwind utilities;

/* for page */
.page-enter-active,
.page-leave-active {
  transition: opacity 0.5s ease;
}
.page-enter-from,
.page-leave-to {
  opacity: 0;
}

/* for layout  */
.my-layouts-enter-active,
.my-layouts-leave-active {
  transition: opacity 0s ease-in-out;
}
.my-layouts-enter-from,
.my-layouts-leave-to {
  opacity: 0;
}

.globalFadeInFadeOut-enter-active,
.globalFadeInFadeOut-leave-active {
  transition: opacity 0.5s ease-in-out;
}
.globalFadeInFadeOut-enter-from,
.globalFadeInFadeOut-leave-to {
  opacity: 0;
}

.landing_header_two {
  @apply text-3xl md:text-4xl lg:text-5xl text-center text-gray-1200 font-bold px-4;
}
.landing_header_two b {
  @apply text-yellow-primary;
}
.h_tag_one {
  @apply text-[#505050] xl:text-3xl md:text-2xl text-xl font-medium;
}
.h_tag_two {
  @apply text-[#505050] xl:text-2xl md:text-xl text-lg font-medium;
}
.p_tag_one {
  @apply text-[#656565] xl:text-xl md:text-lg text-base;
}
.orange_button {
  @apply w-[170px] h-10 rounded-full bg-orange-600 text-white px-9 text-base font-bold flex items-center justify-center whitespace-nowrap;
}
.orange_border_button {
  @apply w-[170px] h-10 rounded-full border-2 border-yellow-primary text-yellow-primary px-9 text-base font-bold flex items-center justify-center whitespace-nowrap;
}

html,
body {
  font-family: 'Roboto';
  font-display: swap;
}
