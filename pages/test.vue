<template>
  <form class="mt-[122px]" @submit.prevent="onSubmit">
    <div v-for="(item, idx) in form.items" :key="idx" class="mb-4">
      <label :for="`item-${idx}`">Item {{ idx + 1 }}</label>
      <input
        :id="`item-${idx}`"
        v-model="item.name"
        type="text"
        class="border p-1"
        @blur="v$.value.items.$touch()"
      />
      <div v-if="v$.items.$each.$response.$data[idx].name.$error">
        <div
          v-for="error in v$.items.$each.$response.$errors[idx].name"
          :key="error"
        >
          <p v-if="v$.items.$each.$response.$data[idx].name">{{ error.$message }} {{ v$.items.$each.$response.$data[idx].name }} {{ v$.items.$each.$response }}</p>
        </div>
      </div>
      <!-- <div
        v-for="error in v$.items.$each.$response.$errors[idx]?.name || []"
        :key="error"
        class="text-red-600 text-sm"
      >
        <p v-if="v$.items.$each[idx].name.$dirty">{{ error.$message }}</p>
      </div> -->
      <!-- <div v-if="v$.items.$each[idx].$error" class="text-red-600 text-sm">
        <div v-if="v$.items.$each[idx].required.$invalid">
          This field is required.
        </div>
        <div v-if="v$.items.$each[idx].minLength?.$invalid">
          Must be at least
          {{ rules.items.$each.minLength.$params.min }} characters.
        </div>
      </div> -->
      <button type="button" @click="removeItem(idx)" class="ml-2 text-sm">
        Remove
      </button>
    </div>

    <button type="button" @click="addItem" class="mr-2">+ Add another</button>
    <button type="submit">Submit</button>
  </form>
</template>

<script setup lang="ts">
import { reactive, computed } from 'vue'
import useVuelidate from '@vuelidate/core'
import { helpers, required, minLength } from '@vuelidate/validators'

interface FormModel {
  items: string[]
}

const form = reactive<FormModel>({
  items: [{ name: '' }, { name: 'a' }],
})

// define your validation rules for the array:
// here we say “for each element in items, apply both required and minLength(3)”
// const rules = {
//   form: {
//     required,
//     minLength: minLength(3)
//   },
// }
// const v$ = useVuelidate(rules, form)
// const rules = computed(() => ({
//   items: {
//     $each: {
//       required,
//       minLength: minLength(3)
//     }
//   }
// }))
const rules = {
  items: {
    $each: helpers.forEach({
      name: {
        required,
        minLength: minLength(3),
      },
    }),
  },
}
// bind Vuelidate
const v$ = useVuelidate(rules, form)

// helpers to add/remove
function addItem() {
  form.items.push({ name: '' })
}

function removeItem(idx: number) {
  form.items.splice(idx, 1)
}

// on submit, touch all fields and check validity
function onSubmit() {
  console.log(v$.value.items, 'v$.value', v$.value.items.$each)
  v$.value.$touch()
  if (!v$.value.$invalid) {
    // all good!
    console.log('submitted items:', form.items)
  }
}
</script>
