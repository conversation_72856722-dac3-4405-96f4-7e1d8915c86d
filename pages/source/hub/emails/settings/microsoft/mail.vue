<script setup lang="ts">
import { isEqual } from 'lodash'
import { useStore } from 'vuex'
import ComposeReply from '~/components/source/hub/microsoft/settings/mail/ComposeReply.vue'
import JunkEmail from '~/components/source/hub/microsoft/settings/mail/junk-email/index.vue'
import MessageHandling from '~/components/source/hub/microsoft/settings/mail/MessageHandling.vue'
import Rules from '~/components/source/hub/microsoft/settings/mail/Rules.vue'
import type {
  EmittedSenderList,
  JunkMail,
  MessageHandling as MessageHandlingType,
  SenderList,
  SenderOption,
} from '~/types/hubEmailsSettings'

type CommonComponent =
  | InstanceType<typeof ComposeReply>
  | InstanceType<typeof Rules>
  | InstanceType<typeof JunkEmail>
  | InstanceType<typeof MessageHandling>

interface MailSidebarOptions {
  id: number
  text: string
  currentComp: string
}

const store = useStore()
const mailSidebarOptions = ref<MailSidebarOptions[]>([
  {
    id: 1,
    text: 'Compose and reply',
    currentComp: 'SourceHubMicrosoftSettingsMailComposeReply',
  },
  {
    id: 2,
    text: 'Rules',
    currentComp: 'SourceHubMicrosoftSettingsMailRules',
  },
  {
    id: 3,
    text: 'Junk email',
    currentComp: 'SourceHubMicrosoftSettingsMailJunkEmail',
  },
  {
    id: 4,
    text: 'Message handling',
    currentComp: 'SourceHubMicrosoftSettingsMailMessageHandling',
  },
])
const showEditRule = computed(() => store.state.emails.showEditRule)
const currentComp = ref<string>('SourceHubMicrosoftSettingsMailComposeReply')
const commonComponent = ref<CommonComponent | null>(null)
const finalMessageFormatLocal = ref({
  selectedMessageOption: [],
  selectedMessageFormat: 'HTML',
  selectedFontFamily: 'Aptos',
  selectedHeaderSize: 8,
  bold: false,
  italic: false,
  underline: false,
  colorText: '#000000',
})
const finalCopyPasteFormatLocal = ref({
  selectedPastingFromEmailsOption: 'Keep source formatting',
  selectedPastingFromAppsOption: 'Merge formatting',
})
const finalMessageFormat = computed(() => store.state.emails.finalMessageFormat)
const finalCopyPasteFormat = computed(
  () => store.state.emails.finalCopyPasteFormat,
)
const isCompanyReplyChanged = computed(() => {
  return (
    !isEqual(finalMessageFormat.value, finalMessageFormatLocal.value) ||
    !isEqual(finalCopyPasteFormat.value, finalCopyPasteFormatLocal.value)
  )
})
const handleChangeFinalMessageFormat = (data: any) => {
  finalMessageFormatLocal.value = data
}
const handleChangeFinalCopyPasteFormat = (data: any) => {
  finalCopyPasteFormatLocal.value = data
}
const newRuleDataStore = computed(() => store.state.emails.newRuleData)
const newRuleDataLocal = ref(JSON.parse(JSON.stringify(newRuleDataStore.value)))
const isNewRuleChanged = computed(() => {
  return !isEqual(newRuleDataStore.value, newRuleDataLocal.value)
})
const handleChangeNewRuleData = (data: any) => {
  newRuleDataLocal.value = data
}
const junkMailFromStore = computed<JunkMail>(() => store.state.emails.junkMail)
const junkMailFromLocal = ref<JunkMail | null>(junkMailFromStore.value)
const senderListFromStore = computed<SenderList>(
  () => store.state.emails.senderList,
)
const senderListFromLocal = ref<SenderList>(senderListFromStore.value)
const selectedSenderFromLocal = ref<SenderOption['value']>('safe_sender')

const isJunkMailChanged = computed(() => {
  return (
    !isEqual(junkMailFromStore.value, junkMailFromLocal.value) ||
    !isEqual(senderListFromStore.value, senderListFromLocal.value)
  )
})

const handleChangeJunkMail = (junkMail: JunkMail) => {
  junkMailFromLocal.value = junkMail
}
const handleChangeSenderList = (senderList: EmittedSenderList) => {
  senderListFromLocal.value = senderList.senderList
  selectedSenderFromLocal.value = senderList.selectedSender
}

const messageHandlingFromStore = computed(
  () => store.state.emails.messageHandling,
)
const messageHandlingFromLocal = ref<MessageHandlingType | null>(
  messageHandlingFromStore.value,
)
const isMessageHandlingChanged = computed(() => {
  return !isEqual(
    messageHandlingFromStore.value,
    messageHandlingFromLocal.value,
  )
})
const handleChangeMessageHandling = (messageHandling: MessageHandlingType) => {
  messageHandlingFromLocal.value = messageHandling
}

const handleSave = () => {
  if (currentComp.value === 'SourceHubMicrosoftSettingsMailComposeReply') {
    store.commit(
      'emails/SET_FINAL_MESSAGE_FORMAT',
      finalMessageFormatLocal.value,
    )
    store.commit(
      'emails/SET_FINAL_COPY_PASTE_FORMAT',
      finalCopyPasteFormatLocal.value,
    )
  } else if (currentComp.value === 'SourceHubMicrosoftSettingsMailRules') {
    store.commit(
      'emails/SET_NEW_RULE_DATA',
      JSON.parse(JSON.stringify(newRuleDataLocal.value)),
    )
    if (!showEditRule.value) {
      store.commit(
        'emails/SET_GENERATED_RULES',
        JSON.parse(JSON.stringify(newRuleDataLocal.value)),
      )
      store.commit('emails/SHOW_NEW_RULE', false)
    } else {
      store.commit(
        'emails/SET_SELECTED_UPDATED_GENERATED_RULE',
        JSON.parse(JSON.stringify(newRuleDataLocal.value)),
      )
      store.commit('emails/SET_SHOW_EDIT_RULE', false)
    }
  } else if (currentComp.value === 'SourceHubMicrosoftSettingsMailJunkEmail') {
    store.commit('emails/UPDATE_JUNK_MAIL', junkMailFromLocal.value)
    store.commit('emails/UPDATE_SENDER_LIST', {
      senderList: JSON.parse(JSON.stringify(senderListFromLocal.value)),
      selectedSender: selectedSenderFromLocal.value,
    })
  } else if (
    currentComp.value === 'SourceHubMicrosoftSettingsMailMessageHandling'
  ) {
    store.commit(
      'emails/UPDATE_MESSAGE_HANDLING',
      JSON.parse(JSON.stringify(messageHandlingFromLocal.value)),
    )
  }
}
const handleDiscard = () => {
  if (currentComp.value === 'SourceHubMicrosoftSettingsMailComposeReply') {
    ;(
      commonComponent.value as InstanceType<typeof ComposeReply>
    )?.setInitialValue()
  } else if (currentComp.value === 'SourceHubMicrosoftSettingsMailRules') {
    handleChangeNewRuleData(JSON.parse(JSON.stringify(newRuleDataStore.value)))
    store.commit('emails/SHOW_NEW_RULE', false)
    store.commit('emails/SET_SHOW_EDIT_RULE', false)
  } else if (currentComp.value === 'SourceHubMicrosoftSettingsMailJunkEmail') {
    ;(commonComponent.value as InstanceType<typeof JunkEmail>)?.initJunkMail()
  } else if (
    currentComp.value === 'SourceHubMicrosoftSettingsMailMessageHandling'
  ) {
    ;(
      commonComponent.value as InstanceType<typeof MessageHandling>
    )?.initMessageHandling()
  }
}
const showSaveDiscardButton = ref(true)
</script>

<template>
  <div class="w-full relative">
    <div class="w-full h-full grid grid-cols-[224px_1fr] relative">
      <ul class="border-r border-[#F1F2F6] p-4 space-y-1">
        <li
          v-for="mailSidebarOption in mailSidebarOptions"
          :key="mailSidebarOption.id"
        >
          <button
            class="px-4 w-full rounded-full py-[7px] text-base leading-[21px] text-left"
            :class="[
              currentComp === mailSidebarOption.currentComp
                ? 'bg-[#4A71D4] text-white'
                : 'bg-white text-[#333333]',
            ]"
            @click="
              ;(currentComp = mailSidebarOption.currentComp),
                store.commit('emails/SHOW_NEW_RULE', false)
              store.commit('emails/SET_SHOW_EDIT_RULE', false)
            "
          >
            {{ mailSidebarOption.text }}
          </button>
        </li>
      </ul>
      <div class="w-full h-full pb-4 overflow-hidden">
        <Transition name="page" mode="out-in">
          <component
            ref="commonComponent"
            :is="currentComp"
            @set-message-format="handleChangeFinalMessageFormat"
            @set-copy-paste-format="handleChangeFinalCopyPasteFormat"
            @set-new-rule="handleChangeNewRuleData"
            @change-junk-mail="handleChangeJunkMail"
            @change-sender-list="handleChangeSenderList"
            @change-message-handling="handleChangeMessageHandling"
            @hide-save-discard-button="
              ($event) => {
                showSaveDiscardButton = $event
              }
            "
          ></component>
        </Transition>
      </div>
    </div>
    <div
      v-if="
        (isCompanyReplyChanged &&
          currentComp === 'SourceHubMicrosoftSettingsMailComposeReply') ||
        (isNewRuleChanged &&
          showSaveDiscardButton &&
          currentComp === 'SourceHubMicrosoftSettingsMailRules') ||
        (isJunkMailChanged &&
          currentComp === 'SourceHubMicrosoftSettingsMailJunkEmail') ||
        (isMessageHandlingChanged &&
          currentComp === 'SourceHubMicrosoftSettingsMailMessageHandling')
      "
      class="w-full sticky bottom-0 left-0 z-1 flex items-center justify-center space-x-1 py-4 border-t border-[#F1F2F6] bg-white"
    >
      <button
        class="h-[35px] w-[104px] bg-white text-[#4A71D4] text-base leading-[21px] border border-[#4A71D4] px-[26px] py-1.5 rounded-full font-semibold"
        @click="handleDiscard"
      >
        Discard
      </button>
      <button
        class="h-[35px] w-[104px] text-white bg-[#4A71D4] text-base leading-[21px] border border-[#4A71D4] px-[26px] py-1.5 rounded-full font-semibold"
        @click="handleSave"
      >
        Save
      </button>
    </div>
  </div>
</template>

<style scoped></style>
