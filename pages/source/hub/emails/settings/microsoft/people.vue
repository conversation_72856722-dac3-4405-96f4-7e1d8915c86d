<script setup lang="ts">
import { isEqual } from 'lodash'
import { useStore } from 'vuex'
import DiscardWarning from '~/components/source/hub/microsoft/modal/DiscardWarning.vue'

interface ViewOption {
  label: string
  value: string
  description: string
}

interface View {
  displayContactsBy: string
}

const store = useStore()

const viewOptions = ref<ViewOption[]>([
  {
    label: 'First name',
    value: 'first_name',
    description: '',
  },
  {
    label: 'Last name',
    value: 'last_name',
    description: '',
  },
])

const storedView = computed<View>(() => store.state.outlook.view)

const view = ref({
  displayContactsBy: 'first_name',
})

const isViewChanged = computed(() => {
  return !isEqual(view.value, storedView.value)
})

const hasChanges = computed(() => store.state.outlook.isSettingsChanged)
const showWarningModal = computed(() => store.state.outlook.showWarningModal)

const setWarningModal = (state: boolean) => {
  store.commit('outlook/SET_SHOW_WARNING_MODAL', state)
}
const setIsSettingsChanged = (state: boolean) => {
  store.commit('outlook/SET_IS_SETTINGS_CHANGED', state)
}

watch(
  isViewChanged,
  (newVal) => {
    if (newVal) {
      setIsSettingsChanged(true)
    } else {
      setIsSettingsChanged(false)
    }
  },
  { immediate: true },
)

onMounted(() => {
  view.value = { ...storedView.value }
})

const handleSave = () => {
  store.commit('outlook/SET_VIEW', view.value)
  setWarningModal(false)
}
const handleDiscard = () => {
  view.value = { ...storedView.value }
  setWarningModal(false)
}
</script>

<template>
  <div class="w-full grid grid-cols-[224px_1fr] relative">
    <ul class="border-r border-[#F1F2F6] p-4 space-y-1">
      <li>
        <button
          class="px-4 w-full rounded-full py-[7px] text-base leading-[21px] text-left bg-[#4A71D4] text-white"
        >
          View
        </button>
      </li>
    </ul>
    <div class="w-full pb-4 overflow-y-auto custom-scroll h-full">
      <p
        class="px-6 py-4 text-xl leading-[26px] font-semibold text-[#333333] border-b border-[#F1F2F6]"
      >
        View
      </p>
      <div class="px-6 py-4 space-y-3.5">
        <p class="text-base leading-[21px] text-[#333333] font-semibold">
          Display contacts by
        </p>
        <InputsRadioOptionGroup
          v-model="view.displayContactsBy"
          :options="viewOptions"
          labelClass="font-normal"
          class="!space-y-3.5"
        />
      </div>
    </div>
    <div
      v-if="hasChanges"
      class="w-full flex items-center justify-center absolute bottom-0 left-0 space-x-1 py-4 border-t border-[#F1F2F6] bg-white"
    >
      <button
        @click="handleDiscard"
        class="h-[35px] w-[104px] bg-white text-[#4A71D4] text-base leading-[21px] border border-[#4A71D4] px-[26px] py-1.5 rounded-full font-semibold"
      >
        Discard
      </button>
      <button
        @click="handleSave"
        class="h-[35px] w-[104px] text-white bg-[#4A71D4] text-base leading-[21px] border border-[#4A71D4] px-[26px] py-1.5 rounded-full font-semibold"
      >
        Save
      </button>
    </div>
    <DiscardWarning
      :isOpen="showWarningModal"
      @exit="setWarningModal(false)"
      @discard="handleDiscard"
      @save="handleSave"
    />
  </div>
</template>

<style scoped></style>
