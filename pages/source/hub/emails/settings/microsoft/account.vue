<script setup lang="ts">
import { isEqual } from 'lodash'
import { useStore } from 'vuex'
import DiscardWarning from '~/components/source/hub/microsoft/modal/DiscardWarning.vue'
import AutomaticReplies from '~/components/source/hub/microsoft/settings/account/automatic-replies.vue'
import Signatures from '~/components/source/hub/microsoft/settings/account/signatures.vue'
import type { DefaultSignature, Signature } from '~/types/hubEmailsSettings'

const store = useStore()

const navigationItems = [
  {
    key: 'automatic-replies',
    label: 'Automatic replies',
  },
  {
    key: 'signatures',
    label: 'Signatures',
  },
]

const automaticRepliesRef = ref<InstanceType<typeof AutomaticReplies> | null>(
  null,
)
const signaturesRef = ref<InstanceType<typeof Signatures> | null>(null)
const autoRepliesCurrent = ref(null)
const currentComp = ref('automatic-replies')
const autoRepliesOld = computed(() => store.state.outlook.autoReplies)
const isAutoRepliesChanged = computed(() => {
  return !isEqual(autoRepliesOld.value, autoRepliesCurrent.value)
})

const handleChangeAutoReplies = (data: any) => {
  autoRepliesCurrent.value = data
}

const defaultSignatureOld = computed(() => store.state.outlook.defaultSignature)
const defaultSignature = ref<DefaultSignature | null>(defaultSignatureOld.value)
const isDefaultSignatureChanged = computed(() => {
  return (
    !isEqual(defaultSignatureOld.value, defaultSignature.value) &&
    currentComp.value === 'signatures'
  )
})

const handleChangeDefaultSignatures = (data: DefaultSignature) => {
  defaultSignature.value = data
}
const isSignatureNameError = ref(false)
const isSignatureCreated = ref(false)
const isSignatureChanged = ref(false)
const isSignatureNameChanged = ref(false)
const deletedSignatures = ref<string[]>([])
const updatedSignature = ref<Signature | null>(null)
const signatureOptions = computed<Signature[]>(
  () => store.state.outlook.signatureOptions,
)
const isSignatureActuallyChanged = computed(() => {
  return (
    (isDefaultSignatureChanged.value ||
      ((isSignatureChanged.value ||
        isSignatureNameChanged.value ||
        isSignatureCreated.value) &&
        updatedSignature.value?.label) ||
      deletedSignatures.value.length > 0) &&
    !isSignatureNameError.value
  )
})

const handleUpdateSignature = (data: Signature | null) => {
  updatedSignature.value = data
  if (isSignatureCreated.value) {
    isSignatureChanged.value = false
    isSignatureNameChanged.value = false
    return
  }
  isSignatureChanged.value = isSignatureChangedFN(data)
}
const isSignatureChangedFN = (data: Signature | null) => {
  if (data && data?.id) {
    const matchedSignature = signatureOptions.value?.find(
      (option) => option.id === data?.id,
    )
    return !isEqual(matchedSignature, data)
  }
  return false
}
const handleCreateNewSignature = () => {
  isSignatureCreated.value = true
  isSignatureChanged.value = false
  isSignatureNameChanged.value = false
}
const handleDeleteSignature = (ids: string[]) => {
  deletedSignatures.value = ids
}
const handleUpdateSignatureName = () => {
  isSignatureNameChanged.value = true
}

const hasChanges = computed(() => store.state.outlook.isSettingsChanged)
const showWarningModal = computed(() => store.state.outlook.showWarningModal)

const setWarningModal = (state: boolean) => {
  store.commit('outlook/SET_SHOW_WARNING_MODAL', state)
}
const setIsSettingsChanged = (state: boolean) => {
  store.commit('outlook/SET_IS_SETTINGS_CHANGED', state)
}

watch(
  [
    isAutoRepliesChanged,
    isSignatureActuallyChanged,
    deletedSignatures,
    currentComp,
  ],
  ([autoRepliesChanged, signatureChanged, deletedSigs, currentComponent]) => {
    if (currentComponent === 'automatic-replies') {
      setIsSettingsChanged(autoRepliesChanged)
    } else if (currentComponent === 'signatures') {
      setIsSettingsChanged(signatureChanged || deletedSigs.length > 0)
    } else {
      setIsSettingsChanged(false)
    }
  },
  { immediate: true },
)

const handleChangeComp = (comp: string) => {
  if (hasChanges.value && currentComp.value !== comp) {
    setWarningModal(true)
  } else {
    currentComp.value = comp
  }
}

const handleDiscard = () => {
  if (currentComp.value === 'automatic-replies') {
    automaticRepliesRef.value?.initializeData()
  } else if (currentComp.value === 'signatures') {
    signaturesRef.value?.initializeData()
    isSignatureChanged.value = false
    isSignatureNameChanged.value = false
    deletedSignatures.value = []
    if (isSignatureCreated.value && signatureOptions.value.length === 0) {
      isSignatureCreated.value = true
    } else if (updatedSignature.value?.label) {
      isSignatureCreated.value = false
    } else {
      isSignatureChanged.value = true
    }
  }
  setWarningModal(false)
}
const handleSave = () => {
  if (currentComp.value === 'automatic-replies') {
    store.commit('outlook/SET_AUTO_REPLIES', autoRepliesCurrent.value)
  } else if (currentComp.value === 'signatures') {
    if (isDefaultSignatureChanged.value) {
      store.commit('outlook/SET_DEFAULT_SIGNATURE', defaultSignature.value)
    }
    if (isSignatureCreated.value) {
      store.commit('outlook/SET_NEW_SIGNATURE', updatedSignature.value)
    }
    if (isSignatureChanged.value || isSignatureNameChanged.value) {
      store.commit('outlook/SET_UPDATED_SIGNATURE', updatedSignature.value)
    }
    if (deletedSignatures.value.length > 0) {
      store.commit('outlook/SET_DELETED_SIGNATURE', deletedSignatures.value)
    }
  }
  handleDiscard()
}

const handleIsError = (isError: boolean) => {
  isSignatureNameError.value = isError
}
</script>

<template>
  <div class="w-full grid grid-cols-[224px_1fr] relative">
    <ul class="border-r border-[#F1F2F6] p-4 space-y-1">
      <li v-for="item in navigationItems" :key="item.key">
        <button
          class="px-4 w-full rounded-full py-[7px] text-base leading-[21px] text-left"
          :class="[
            currentComp === item.key
              ? 'bg-[#4A71D4] text-white'
              : 'bg-white text-[#333333]',
          ]"
          @click="handleChangeComp(item.key)"
        >
          {{ item.label }}
        </button>
      </li>
    </ul>
    <div
      class="w-full pb-4 overflow-y-auto custom-scroll"
      :class="[hasChanges ? 'h-[calc(100%-68px)]' : 'h-full']"
    >
      <Transition name="page" mode="out-in">
        <AutomaticReplies
          ref="automaticRepliesRef"
          v-if="currentComp === 'automatic-replies'"
          @autoRepliesChange="handleChangeAutoReplies"
        />
        <Signatures
          ref="signaturesRef"
          v-else-if="currentComp === 'signatures'"
          @defaultSignatureChange="handleChangeDefaultSignatures"
          @updateSignature="handleUpdateSignature"
          @createNewSignature="handleCreateNewSignature"
          @updateSignatureName="handleUpdateSignatureName"
          @deleteSignature="handleDeleteSignature"
          @isError="handleIsError"
        />
      </Transition>
    </div>
    <div
      v-if="hasChanges"
      class="w-full flex items-center justify-center absolute bottom-0 left-0 space-x-1 py-4 border-t border-[#F1F2F6] bg-white"
    >
      <button
        @click="handleDiscard"
        class="h-[35px] w-[104px] bg-white text-[#4A71D4] text-base leading-[21px] border border-[#4A71D4] px-[26px] py-1.5 rounded-full font-semibold"
      >
        Discard
      </button>
      <button
        @click="handleSave"
        class="h-[35px] w-[104px] text-white bg-[#4A71D4] text-base leading-[21px] border border-[#4A71D4] px-[26px] py-1.5 rounded-full font-semibold"
      >
        Save
      </button>
    </div>
    <DiscardWarning
      :isOpen="showWarningModal"
      @exit="setWarningModal(false)"
      @discard="handleDiscard"
      @save="handleSave"
    />
  </div>
</template>

<style scoped></style>
