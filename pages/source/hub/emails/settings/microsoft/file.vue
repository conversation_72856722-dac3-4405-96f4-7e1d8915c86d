<script setup lang="ts">
import { useStore } from 'vuex'
import FileImportLoader from '~/components/source/hub/microsoft/settings/file/FileImportLoader.vue'
import ImportHistoryCard from '~/components/source/hub/microsoft/settings/file/ImportHistoryCard.vue'
import type { ImportFileHistory } from '~/types/hubEmailsSettings'

const store = useStore()

const importFileHistory = computed<ImportFileHistory>(
  () => store.state.outlook.importFileHistory,
)

const handleStartImport = () => {
  store.commit('outlook/SET_IMPORT_EMAIL_MODAL', true)
}
</script>

<template>
  <div class="w-full grid grid-cols-[224px_1fr] relative">
    <ul class="border-r border-[#F1F2F6] p-4 space-y-1">
      <li>
        <button
          class="px-4 w-full rounded-full py-[7px] text-base leading-[21px] text-left bg-[#4A71D4] text-white"
        >
          Import
        </button>
      </li>
    </ul>
    <div class="w-full pb-4 overflow-y-auto custom-scroll h-full">
      <p
        class="px-6 py-4 text-xl leading-[26px] font-semibold text-[#333333] border-b border-[#F1F2F6]"
      >
        Import
      </p>
      <div class="px-6 pt-4">
        <p
          class="text-[#333333] text-base leading-[21px] font-semibold flex items-center space-x-2"
        >
          <span>Import email</span>
          <SharedIconInfoRounded class="text-[#434343]" />
        </p>
        <p class="text-[#707070] text-base leading-[21px] pt-4">
          You can import .eml files to your mailbox by selecting a folder.
        </p>
        <button
          :disabled="importFileHistory?.isLoading"
          class="text-white rounded-full px-6 py-[7px] text-base leading-[21px] mt-3.5"
          :class="[
            importFileHistory?.isLoading ? 'bg-[#C2C2C2]' : 'bg-[#4A71D4]',
          ]"
          @click="handleStartImport"
        >
          Start Import
        </button>
      </div>
      <div
        v-if="importFileHistory && importFileHistory.isLoading"
        class="px-6 mt-5"
      >
        <FileImportLoader />
      </div>
      <div
        v-if="importFileHistory && importFileHistory.history.length > 0"
        class="px-6 mt-12"
      >
        <p class="text-[#333333] text-base leading-[21px] font-semibold">
          History
        </p>
        <div class="pt-4 space-y-4">
          <ImportHistoryCard
            v-for="history in importFileHistory.history"
            :key="history.id"
            :history="history"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
