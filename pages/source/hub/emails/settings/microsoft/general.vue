<script setup lang="ts">
import { isEqual } from 'lodash'
import { useStore } from 'vuex'
import DiscardWarning from '~/components/source/hub/microsoft/modal/DiscardWarning.vue'
import LanguageAndTime from '~/components/source/hub/microsoft/settings/general/LanguageAndTime.vue'
import SearchSettings from '~/components/source/hub/microsoft/settings/general/Search.vue'
import type {
  LanguageAndTime as LanguageAndTimeType,
  Search,
} from '~/types/hubEmailsSettings'

type CommonComponentInstance =
  | InstanceType<typeof LanguageAndTime>
  | InstanceType<typeof SearchSettings>

interface GeneralSidebarOptions {
  id: number
  text: string
  currentComp: string
}

const generalSidebarOptions = ref<GeneralSidebarOptions[]>([
  {
    id: 1,
    text: 'Language and time',
    currentComp: 'SourceHubMicrosoftSettingsGeneralLanguageAndTime',
  },
  {
    id: 2,
    text: 'Search',
    currentComp: 'SourceHubMicrosoftSettingsGeneralSearch',
  },
])

const commonComponent = ref<CommonComponentInstance | null>(null)
const currentComp = ref<string>(
  'SourceHubMicrosoftSettingsGeneralLanguageAndTime',
)

const setWarningModal = (state: boolean) => {
  store.commit('outlook/SET_SHOW_WARNING_MODAL', state)
}
const setIsSettingsChanged = (state: boolean) => {
  store.commit('outlook/SET_IS_SETTINGS_CHANGED', state)
}

const handleChangeComp = (comp: string) => {
  if (hasChanges.value && currentComp.value !== comp) {
    setWarningModal(true)
  } else {
    currentComp.value = comp
  }
}

const store = useStore()

const languageAndTimeSettings = computed(
  () => store.state.outlook.languageAndTime,
)
const updatedLanguageAndTimeSettings = ref<LanguageAndTimeType | null>(
  languageAndTimeSettings.value,
)
const isLanguageAndTimeSettingsChanged = computed(() => {
  return !isEqual(
    languageAndTimeSettings.value,
    updatedLanguageAndTimeSettings.value,
  )
})
const handleLanguageAndTimeChange = (languageAndTime: LanguageAndTimeType) => {
  updatedLanguageAndTimeSettings.value = languageAndTime
}

const otherTimeZones = computed<string[]>(
  () => store.state.outlook.otherTimeZones,
)
const updatedOtherTimeZones = ref<string[]>([])
const isOtherTimeZonesChanged = computed(() => {
  return !isEqual(otherTimeZones.value, updatedOtherTimeZones.value)
})
const handleTimezonesChange = (timezones: string[]) => {
  updatedOtherTimeZones.value = timezones
}

const searchSettings = computed(() => store.state.outlook.search)
const updatedSearchSettings = ref<Search | null>(searchSettings.value)
const isSearchSettingsChanged = computed(() => {
  return !isEqual(searchSettings.value, updatedSearchSettings.value)
})

const hasChanges = computed(() => store.state.outlook.isSettingsChanged)
const showWarningModal = computed(() => store.state.outlook.showWarningModal)

watch(
  [
    isLanguageAndTimeSettingsChanged,
    isOtherTimeZonesChanged,
    isSearchSettingsChanged,
    currentComp,
  ],
  ([
    languageTimeChanged,
    timeZonesChanged,
    searchChanged,
    currentComponent,
  ]) => {
    if (
      currentComponent === 'SourceHubMicrosoftSettingsGeneralLanguageAndTime'
    ) {
      setIsSettingsChanged(languageTimeChanged || timeZonesChanged)
    } else if (currentComponent === 'SourceHubMicrosoftSettingsGeneralSearch') {
      setIsSettingsChanged(searchChanged)
    } else {
      setIsSettingsChanged(false)
    }
  },
  { immediate: true },
)

const handleSearchChange = (search: Search) => {
  updatedSearchSettings.value = search
}

const handleDiscard = () => {
  if (
    currentComp.value === 'SourceHubMicrosoftSettingsGeneralLanguageAndTime'
  ) {
    ;(
      commonComponent.value as InstanceType<typeof LanguageAndTime>
    )?.initializeLanguageTimeData()
    updatedLanguageAndTimeSettings.value = null
    updatedOtherTimeZones.value = []
  } else if (currentComp.value === 'SourceHubMicrosoftSettingsGeneralSearch') {
    ;(
      commonComponent.value as InstanceType<typeof SearchSettings>
    )?.initializeSearchData()
    updatedSearchSettings.value = null
  }
  setWarningModal(false)
}

const handleSave = () => {
  if (
    currentComp.value === 'SourceHubMicrosoftSettingsGeneralLanguageAndTime'
  ) {
    store.commit(
      'outlook/SET_LANGUAGE_AND_TIME',
      updatedLanguageAndTimeSettings.value,
    )
    store.commit('outlook/SET_OTHER_TIME_ZONES', updatedOtherTimeZones.value)
  } else if (currentComp.value === 'SourceHubMicrosoftSettingsGeneralSearch') {
    store.commit('outlook/SET_SEARCH', updatedSearchSettings.value)
  }
  setWarningModal(false)
}
</script>

<template>
  <div class="w-full grid grid-cols-[224px_1fr] relative">
    <ul class="border-r border-[#F1F2F6] p-4 space-y-1">
      <li
        v-for="generalOption in generalSidebarOptions"
        :key="generalOption.id"
      >
        <button
          class="px-4 w-full rounded-full py-[7px] text-base leading-[21px] text-left"
          :class="[
            currentComp === generalOption.currentComp
              ? 'bg-[#4A71D4] text-white'
              : 'bg-white text-[#333333]',
          ]"
          @click="handleChangeComp(generalOption.currentComp)"
        >
          {{ generalOption.text }}
        </button>
      </li>
    </ul>
    <div
      class="w-full pb-4 overflow-hidden overflow-y-auto custom-scroll"
      :class="[hasChanges ? 'h-[calc(100%-68px)]' : 'h-full']"
    >
      <Transition name="page" mode="out-in">
        <component
          ref="commonComponent"
          :is="currentComp"
          @changeSearch="handleSearchChange"
          @changeLanguageAndTime="handleLanguageAndTimeChange"
          @changeTimezones="handleTimezonesChange"
        ></component>
      </Transition>
    </div>
    <div
      v-if="hasChanges"
      class="w-full flex items-center justify-center absolute bottom-0 left-0 space-x-1 py-4 border-t border-[#F1F2F6] bg-white"
    >
      <button
        @click="handleDiscard"
        class="h-[35px] w-[104px] bg-white text-[#4A71D4] text-base leading-[21px] border border-[#4A71D4] px-[26px] py-1.5 rounded-full font-semibold"
      >
        Discard
      </button>
      <button
        @click="handleSave"
        class="h-[35px] w-[104px] text-white bg-[#4A71D4] text-base leading-[21px] border border-[#4A71D4] px-[26px] py-1.5 rounded-full font-semibold"
      >
        Save
      </button>
    </div>
    <DiscardWarning
      :isOpen="showWarningModal"
      @exit="setWarningModal(false)"
      @discard="handleDiscard"
      @save="handleSave"
    />
  </div>
</template>

<style scoped></style>
