<script setup lang="ts">
import { useStore } from 'vuex'
import Forwarding from '~/components/source/hub/emails/settings/forwardandpop/Forwarding.vue'
import ImapAccess from '~/components/source/hub/emails/settings/forwardandpop/ImapAccess.vue'
import PopDownload from '~/components/source/hub/emails/settings/forwardandpop/PopDownload.vue'
import type {
  ForwardingAndPopImap,
  Forwarding as ForwardingType,
  ImapAccess as ImapAccessType,
  Pop,
} from '~/types/hubEmailsSettings'

import { isEqual } from 'lodash'

const store = useStore()

const forwardingAndPopImap = ref<ForwardingAndPopImap>({
  forwarding: null,
  imapAccess: null,
  pop: null,
})

const oldForwardingAndPopImap = computed<ForwardingAndPopImap>(
  () => store.state.emails.forwardingAndPopImap,
)

const isOldNewDateAreEqual = computed(() =>
  isEqual(forwardingAndPopImap.value, oldForwardingAndPopImap.value),
)

const handleImapAccessChange = (newImapAccess: ImapAccessType) => {
  forwardingAndPopImap.value.imapAccess = newImapAccess
}

const handlePopChange = (pop: Pop) => {
  forwardingAndPopImap.value.pop = pop
}

const handleForwardingChange = (forwarding: ForwardingType) => {
  forwardingAndPopImap.value.forwarding = forwarding
}

const handleSaveChanges = () => {
  store.commit(
    'emails/UPDATE_FORWARDING_AND_POP_IMAP',
    forwardingAndPopImap.value,
  )
}
</script>

<template>
  <div class="w-full flex flex-col px-6 pb-4">
    <Forwarding @changeForwarding="handleForwardingChange" />
    <PopDownload @changePop="handlePopChange" />
    <ImapAccess @changeImapAccess="handleImapAccessChange" />

    <div class="w-full flex items-center justify-center space-x-2 pt-4">
      <button
        class="h-[35px] bg-white text-[#4A71D4] text-base leading-[21px] border border-[#4A71D4] px-[26px] py-1.5 rounded-full font-semibold"
      >
        Cancel
      </button>
      <button
        :disabled="isOldNewDateAreEqual"
        class="h-[35px] text-white text-base leading-[21px] px-[26px] py-1.5 rounded-full font-semibold"
        :class="[!isOldNewDateAreEqual ? 'bg-[#4A71D4]' : 'bg-[#C2C2C2]']"
        @click="handleSaveChanges"
      >
        Save Changes
      </button>
    </div>
  </div>
</template>

<style scoped></style>
